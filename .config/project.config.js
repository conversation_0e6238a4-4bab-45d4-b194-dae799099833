/**
 * <AUTHOR> mengling
 * @File         : 用途
 * @Date         : 2021-03-31 16:25:36
 * @LastEditors  : lubiao
 * @LastEditTime : 2022-09-19 18:58:21
*/
module.exports = {
    buildName: 'liveshow,nmp', // 决定当前开发的项目
    devPages: ['index', 'nmp'], // 决定当前开发环境响应的页面
    dev: {
        // mock: true,
        // proxyTable: {
        //     '/searchbox': {
        //         target: 'http://localhost:5000',
        //         changeOrigin: true,
        //         secure: false
        //     }
        // },
    },
    build: {
        // assetsPublicPath: '/',
        eslint: false, // 关闭eslint 可大幅提高打包速度
    },
};
