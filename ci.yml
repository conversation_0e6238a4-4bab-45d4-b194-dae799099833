Global:
    version: 2.0
    group_email: <EMAIL>

Default:
    profile: [buildProduction]

Profiles:

  - profile:
    name: buildProduction
    mode: AGENT
    environment:
        image: DECK_STD_CENTOS7
        resourceType: 2XLARGE
        tools:
          - nodejs: 20.latest
          - yarn: 1.22.4
    check:
      - reuse: TASK
        enable: true
    build:
        command: sh scripts/build.sh ${BUILD_TARGET}
    cache:
        enable: false
        paths:
          - node_modules
    artifacts:
        release: true

  - profile:
    name: jscov
    mode: AGENT
    environment:
        image: DECK_STD_CENTOS7
        tools:
          - nodejs: 20.latest
          - yarn: 1.22.4
    check:
      - reuse: TASK
        enable: true
    build:
        command: wget hdc.baidu-int.com:9119/home/<USER>/ecom_fcstar/BDAPFT_tools/zeroFrontAuto/jscov.sh && sh jscov.sh && sh syprejscovbuild.sh -index ${file} && sh jscovbuild ${data} && rm -rf output* jscov* syprejscovbuild* && sh scripts/build.sh ${BUILD_TARGET}
    artifacts:
        release: true
