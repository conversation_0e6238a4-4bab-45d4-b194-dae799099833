<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta
            name="viewport"
            content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no,initial-scale=1,viewport-fit=cover"
        />
        <meta name="screen-orientation" content="portrait">
        <meta name="x5-orientation" content="portrait">
        <meta name="format-detection" content="telephone=no, email=no">
        <meta name="apple-mobile-web-app-status-bar-style" content="white" />
        <meta name="apple-mobile-web-app-capable" content="no">
        <meta name="tpl" content="live/media/clue">
        <link rel="dns-prefetch" href="//b.bdstatic.com">
        <link rel="dns-prefetch" href="//s.bdstatic.com">
        <link rel="dns-prefetch" href="//mv.bdstatic.com">
        <title><%= htmlWebpackPlugin.options.title %></title>
        <style>html {font-size: 24.1546vw;}</style>
        <% if (htmlWebpackPlugin.options.whiteScreen) { %>
        <script>
            !function(e,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):(e="undefined"!=typeof globalThis?globalThis:e||self).__spyHead=n()}(this,function(){"use strict";function b(e){var n,t=function(e){if(URL){e=new URL(e);if(void 0!==e.host)return{protocol:e.protocol,host:e.host,pathname:e.pathname,ext:""}}}(e),e=(t||((n=document.createElement("a")).href=e,t={protocol:n.protocol,host:n.host||location.host,pathname:n.pathname,ext:""}),t.pathname.split("."));return t.ext=e[e.length-1],t}function o(e){return+e.toFixed(1)}var x={conf:{},winerrors:[],errorDestroy:function(){},observerDestroy:function(){},entryMap:{},init:function(e){this.conf=e},addError:function(e){if(0<this.winerrors.length){var n=this.winerrors[this.winerrors.length-1];if(e.info.msg===n.info.msg)return void(n.info.count+=n.info.count||0)}this.winerrors.length<1e3&&this.winerrors.push(e)},send:function(e,n,t){var o,r=this.conf;e.type=e.type||"except",e.pid=r.pid,e.lid=r.lid,e.ts=Date.now(),this.addError(e),this.interceptor&&this.interceptor(e),!1!==n&&(t=t||r.logServer,n="".concat(t,"?pid=").concat(e.pid,"&lid=").concat(e.lid,"&ts=").concat(e.ts)+"&type=".concat(e.type,"&group=").concat(e.group,"&info=").concat(encodeURIComponent(JSON.stringify(e.info))),e.dim&&(n+="&dim="+encodeURIComponent(JSON.stringify(e.dim))),(o=new Image).src=n,o.onload=o.onerror=function(){o=null})},sendUbc:function(e){var n,t,o={cateid:"99",actiondata:{id:e.id,type:"0",timestamp:Date.now(),content:{value:e.value,page:e.page,type:e.type,source:e.source,from:e.from,ext:e.ext}}};e.handler&&!1===e.handler(e)||(e="object"==typeof window&&window.self===window?navigator.userAgent:"",n="baiduboxapp",/bdhonorbrowser/.test(e)&&(n="bdhonorbrowser"),/ (lite|info|pro|mission) (baiduboxapp|biduboxapp)/.test(e)&&(n="baiduboxlite"),/tomas/i.exec(e)&&(n="tomas"),/baiduboxsenior/i.exec(e)&&(n="baiduboxsenior"),e="https://mbd.baidu.com/ztbox?action=zpblog&v=2.0&appname=".concat(n,"&data=").concat(encodeURIComponent(JSON.stringify(o))),(t=new Image).src=e,t.onload=t.onerror=function(){t=null})}};function t(e){var h=e.resourceError||{},m=e.jsError||{},g=Math.random()<(m.sample||0),w=Math.random()<(h.sample||0),v=x.winerrors,y=0;function n(e){try{var n=e.target,t={info:{},dim:{},group:""},o=t.info,r=e.srcElement,i=navigator.connection||{};if(o.downlink=i.downlink,o.effectiveType=i.effectiveType,o.rtt=i.rtt,o.deviceMemory=navigator.deviceMemory||0,o.hardwareConcurrency=navigator.hardwareConcurrency||0,r===window){t.group=m.group;for(var a,c=e.error||{},s=(o.msg=e.message,o.file=e.filename,o.ln=e.lineno,o.col=e.colno,o.stack=(c.stack||"").split("\n").slice(0,3).join("\n"),-1!==o.msg.indexOf("MODULE_TIMEOUT")&&(a=o.msg.match(/^.*Hang:(.*); Miss:(.*)/))&&a[2]&&(o.msg="MODULE_TIMEOUT for miss:"+a[2]),[]),d=0;d<v.length;d++){var f=v[d],p=1<f.info.count?"(".concat(f.info.count,")"):"";s.push(p+f.info.msg)}o.hisErrors=s.join("----");var u=!0;!1!==(u=m.handler?m.handler(t):u)&&x.send(t,g)}else{t.group=h.group,t.dim.type=r.tagName.toLowerCase();var l=r.src||r.href,u=(o.msg=l||"unknown load eror",t.dim.host=b(l).host,n&&"IMG"===n.tagName&&(o.xpath=function(e){if(!e)return{xpath:""};for(var n=[];e&&1===e.nodeType&&e!==e.parentNode;){var t=e.tagName.toLowerCase();if(e.getAttribute("id")?t+="[#"+e.getAttribute("id")+"]":e.classList&&e.classList.length&&(t+="[."+e.classList[e.classList.length-1]+"]"),n.push(t),e===document.body)break;e=e.parentNode}return{xpath:n.join("<")}}(n).xpath),y&&(o.hisErrCount=y),!0);!1!==(u=h.handler?h.handler(t):u)&&x.send(t,w),y++}}catch(e){console.error(e)}}window.addEventListener("error",n,!0),x.errorDestroy=function(){window.removeEventListener("error",n,!0),x.winerrors=[]}}var h=function(){return(h=Object.assign||function(e){for(var n,t=1,o=arguments.length;t<o;t++)for(var r in n=arguments[t])Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r]);return e}).apply(this,arguments)};function r(e){var i=e.whiteScreenError||{},a=e.whiteScreenUbcConf||{},c=i.handler,s=i.selector,d=i.subSelector,f=i.percentage,n=i.timeout||6e3,t=Math.random(),p=t<(i.sample||0),u=e.whiteScreenUbcConf&&t<(a.sample||0);function l(){var e;return!!window.performance&&(e={wait:o((e=window.performance.timing).domainLookupStart-(e.navigationStart||e.fetchStart||e.startTime)),dns:o(e.domainLookupEnd-e.domainLookupStart),connect:o(e.connectEnd-e.connectStart),req:o(e.responseStart-e.requestStart),res:o(e.responseEnd-e.responseStart)},"&wait=".concat(e.wait)+"&dns=".concat(e.dns)+"&connect=".concat(e.connect)+"&requestTime=".concat(e.req)+"&resoneTime=".concat(e.res))}s&&setTimeout(function(){var e,n,t=l(),o=(e={},o=navigator.connection||{},e.downlink=o.downlink,e.effectiveType=o.effectiveType,e.rtt=o.rtt,e.deviceMemory=navigator.deviceMemory||0,e.hardwareConcurrency=navigator.hardwareConcurrency||0,e),r=(void 0===(e=f)&&(e=2/3),!(r=document.querySelector(s))||!r.querySelector(d)||r.clientHeight<window.innerHeight*e);r&&(e={group:i.group,info:{netTime:t,deviceInfo:o,msg:"WhiteScren Error",hisErrors:function(){if(!x.winerrors)return!1;for(var e=x.winerrors,n=[],t=0;t<e.length;t++){var o=(e[t].info.stack||"").split("\n")[0];n.push("(".concat(t,")").concat(o||e[t].info.msg))}return n.join(";;")}()}},!(n=!0)!==(n=c?c(e):n))&&e.info.msg&&x&&x.send(e,p),u&&x.sendUbc(h(h({},a),{value:r?"white":"noWhite",ext:{netTime:t,deviceInfo:JSON.stringify(o),pageUrl:location.href}}))},n)}return x.init=function(e){if(e.logServer||(e.logServer="https://sp1.baidu.com/5b1ZeDe5KgQFm2e88IuM_a/mwb2.gif"),t(this.conf=e),window.PerformanceObserver){var n=new window.PerformanceObserver(function(e){for(var n=x.entryMap,t=e.getEntries(),o=0;o<t.length;o++){var r=t[o];n[r.entryType]||(n[r.entryType]=[]),n[r.entryType].push(r)}});x.observerDestroy=function(){n.disconnect()};try{n.observe({entryTypes:["longtask","layout-shift","first-input","largest-contentful-paint"]})}catch(e){}}r(e)},window.__spyclientConf&&x.init(window.__spyclientConf),x});
            try {
                var __whiteScreenConf = {
                    params: (function(url) {
                        var reg = /([^\?\=\&]+)\=([^\?\=\&]*)/g;
                        var obj = {};
                        var ret = reg.exec(url);
                        while (ret) {
                            obj[ret[1]] = ret[2];
                            ret = reg.exec(url);
                        }
                        return obj;
                    })(window.location.search),
                    name: '<%= htmlWebpackPlugin.options.whiteScreen.name %>',
                    selector: '<%= htmlWebpackPlugin.options.whiteScreen.selector %>',
                    subSelector: '<%= htmlWebpackPlugin.options.whiteScreen.subSelector %>',
                };
                window.__spyHead && window.__spyHead.init({
                    // pid: '<%= htmlWebpackPlugin.options.vspeed.pid %>',
                    // jsError: {
                    //     group: '<%= htmlWebpackPlugin.options.vspeed.group %>',
                    //     sample: 1
                    // },
                    whiteScreenError: {
                        selector: __whiteScreenConf.selector,
                        subSelector: __whiteScreenConf.subSelector,
                        timeout: 6000, // 必填
                        percentage: 1/3, // 必填
                    },
                    whiteScreenUbcConf: {
                        sample: <%= htmlWebpackPlugin.options.whiteScreen.sample %>,
                        id: '16130',// 必填
                        page: __whiteScreenConf.name,
                        source: __whiteScreenConf.params.source || '',
                        from: 'feed',// 必填
                        type: 'exception',
                        handler(data) {
                            var selectorEle = document.querySelector(__whiteScreenConf.selector);
                            var subSelectorEle = selectorEle && selectorEle.querySelector(__whiteScreenConf.subSelector);
                            var errorSelector = document.querySelector('.error-wrapper .g-error-text');
                            data.ext = data.ext || {};
                            data.ext.hasSelector = !!selectorEle;
                            data.ext.hasSubSelector = !!subSelectorEle;
                            data.ext.selectorHeight = !!selectorEle ? selectorEle.clientHeight : 0;
                            data.ext.subSelectorHeight = !!subSelectorEle ? subSelectorEle.clientHeight : 0;
                            data.ext.windowInnerHeight = window.innerHeight;
                            data.ext.hasErrorSelector = !!errorSelector;
                            data.ext.errorText = !!errorSelector && errorSelector.innerText;
                            Object.assign(data.ext, __whiteScreenConf.params);
                        }
                    }
                });
            } catch (err) {
                console.log('err', err);
            }
        </script>
        <% } %>
        <% if (htmlWebpackPlugin.options.headerScript) { %>
            <%= htmlWebpackPlugin.options.headerScript %>
        <% } %>
        <!--注入css by html-webpack-plugin-->
        <%= htmlWebpackPlugin.tags.headTags %>
        <script async src="//sofire.bdstatic.com/js/xaf3.js"></script>
        <% if (htmlWebpackPlugin.options.speed && htmlWebpackPlugin.options.speed.on) { %>
            <!--********性能基础库配置********-->
            <script>
            window.vSpeedConfig = {
                pid: '<%= htmlWebpackPlugin.options.vspeed.pid %>',
                group: '<%= htmlWebpackPlugin.options.vspeed.group %>',
                on: <%= htmlWebpackPlugin.options.speed.on %>,
                autoFMP: <%= htmlWebpackPlugin.options.speed.autoFMP %>
            };
            </script>
        <% } %>
        <script type="text/javascript" src="https://res2.wx.qq.com/open/js/jweixin-1.6.0.js "></script>
        <script src="//efe-h2.cdn.bcebos.com/ceug/resource/res/2021-11/1638269734284/9149bdf2e2fb.js"></script>
    </head>
    <body ontouchstart>

        <% if ('production' != htmlWebpackPlugin.options.__internal.env && htmlWebpackPlugin.options.debugger) { %>
            <!--debugger-->
            <script type="text/javascript" src="https://code.bdstatic.com/npm/eruda@1.5.6/eruda.min.js"></script><%= htmlWebpackPlugin.options.__internal.debugger.pluginSrc %>
            <script type="text/javascript">
                eruda.init({useShadowDom: true});<%= htmlWebpackPlugin.options.__internal.debugger.pluginAddCall %>
            </script>
        <% } %>

        <!--参数提示-->
        <% if (htmlWebpackPlugin.options.paramCheckScript) { %>
            <%= htmlWebpackPlugin.options.paramCheckScript %>
        <% } %>

        <div id="nmp-root"></div>

        <!--注入chunks by html-webpack-plugin-->
        <%= htmlWebpackPlugin.tags.bodyTags %>

        <% if (htmlWebpackPlugin.options.hmSiteId) { %>
            <script>
            var _hmt = _hmt || [];(function() {var hm = document.createElement("script");hm.src = "https://hm.baidu.com/hm.js?<%= htmlWebpackPlugin.options.hmSiteId %>";var s = document.getElementsByTagName("script")[0];s.parentNode.insertBefore(hm, s);})();
            </script>
        <% } %>
        <script type="text/javascript" src="https://passport.baidu.com/passApi/js/uni_wrapper.js"></script>
    </body>
</html>
