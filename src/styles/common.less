.flex() {
    display: flex;
}
.flexColumn() {
    display: flex;
    flex-direction: column;
}
// 横向排列 水平居中
.flexRowHorizontalCenter() {
    justify-content: center;
}
// 横向排列 垂直居中
.flexRowVerticalCenter() {
    align-items: center;
}
// 纵向排列 水平居中
.flexColumnHorizontalCenter() {
    align-items: center;
}
// 纵向排列 垂直居中
.flexColumnVerticalCenter() {
    justify-content: center;
}
.flexAllCenter() {
    align-items: center;
    justify-content: center;
}
.ellipsis() {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-width: 0;
}
