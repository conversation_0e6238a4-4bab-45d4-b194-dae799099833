.hairline-common(@color, @top, @right, @bottom, @left) {
    content: "";
    position: absolute;
    z-index: 1;
    top: @top;
    right: @right;
    bottom: @bottom;
    left: @left;
    box-sizing: border-box;
    border: 0 solid @color;
    pointer-events: none;
}

.calc-scaleY() {
    @media screen and (min-device-pixel-ratio: 2),
    (-webkit-min-device-pixel-ratio: 2) {
        transform: scaleY(.5);
    }
    @media screen and (min-device-pixel-ratio: 3),
    (-webkit-min-device-pixel-ratio: 3) {
        transform: scaleY(.333333);
    }
}

.calc-scaleX() {
    @media screen and (min-device-pixel-ratio: 2),
    (-webkit-min-device-pixel-ratio: 2) {
        transform: scaleX(.5);
    }
    @media screen and (min-device-pixel-ratio: 3),
    (-webkit-min-device-pixel-ratio: 3) {
        transform: scaleX(.333333);
    }
}

.hairline(@color: #000, @border-width) {
    .hairline-common(@color, 0, 0, 0, 0);
    border-width: @border-width;

    @media screen and (min-device-pixel-ratio: 2),
    (-webkit-min-device-pixel-ratio: 2) {
        transform: scale(.5);
        .hairline-common(@color, -50%, -50%, -50%, -50%);
        border-width: @border-width;
    }

    @media screen and (min-device-pixel-ratio: 3),
    (-webkit-min-device-pixel-ratio: 3) {
        transform: scale(.333333);
        .hairline-common(@color, -100%, -100%, -100%, -100%);
        border-width: @border-width;
    }
}

.hairline-top(@color: #000, @left: 0, @right: 0) {
    .calc-scaleY();
    .hairline-common(@color, 0, @right, auto, @left);
    border-top-width: 1px;
}

.hairline-right(@color: #000, @top: 0, @bottom: 0) {
    .calc-scaleX();
    .hairline-common(@color, @top, 0, @bottom, auto);
    border-right-width: 1px;
}

.hairline-bottom(@color: #000, @left: 0, @right: 0) {
    .calc-scaleY();
    .hairline-common(@color, auto, @right, 0, @left);
    border-bottom-width: 1px;
}

.hairline-left(@color: #000, @top: 0, @bottom: 0) {
    .calc-scaleX();
    .hairline-common(@color, @top, auto, @bottom, 0);
    border-left-width: 1px;
}
