{
    "compilerOptions": {
        "target": "ES2015",
        "module": "ESNext",
        "lib": [
            "DOM",
            "DOM.Iterable",
            "ESNext"
        ],
        "jsx": "react",
        "moduleResolution": "node",
        "allowSyntheticDefaultImports": true,
        "esModuleInterop": true,
        "strict": true,
        "skipLibCheck": true,
        "forceConsistentCasingInFileNames": true,
        "declaration": true,
        "declarationDir": "dist",
        "emitDeclarationOnly": true
    },
    "include": [
        "src/**/*",
    ],
    "exclude": [
        "node_modules",
        "dist"
    ]
}