{"name": "@baidu/abtest", "version": "0.0.15", "description": "Tongji A/B testing infrastructure. (ESM only)", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.mts", "files": ["dist"], "publishConfig": {"access": "public", "registry": "http://registry.npm.baidu-int.com"}, "author": "<EMAIL>", "scripts": {"build": "tsup src/index.tsx --config tsup.config.ts", "dev": "tsup src/index.tsx --config tsup.config.ts --watch", "release": "pnpm build && npm version patch && npm publish"}, "peerDependencies": {"crc-32": "^1.2.2"}, "peerDependenciesMeta": {"crc-32": {"optional": true}}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitest/coverage-v8": "3.1.1", "tsup": "^8.4.0", "typescript": "^5.8.3", "vitest": "^3.1.1"}}