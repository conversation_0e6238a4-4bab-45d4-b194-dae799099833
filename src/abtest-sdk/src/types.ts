import { ReactNode } from 'react';

export interface ABTestConfig {
    key: string;
    paramName: string;
    value: number;
}

export interface ABTestConfigMap {
    [key: string]: ABTestConfig;
}

export interface ABTestContextType {
    abTestConfig: ABTestConfigMap;
    pending: boolean;
    userstat: string;
}

export interface ABTestOptions {
    strategy?: 'baiduTongji' | 'random' | 'crc32';
    userId?: string;
}

export interface ABTestStrategy {
    name: string;
    getValue: (config: ABTestConfig, userId?: string) => Promise<number>;
}

export interface ABTestProviderProps {
    children: ReactNode;
    abTestConfig: ABTestConfigMap;
    injectScript?: () => void;
    options?: ABTestOptions;
}
