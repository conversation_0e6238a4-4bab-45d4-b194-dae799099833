import crc32 from 'crc-32';
import { ABTestConfig, ABTestStrategy } from './types';

declare global {
    interface Window {
        _hmt: any[];
        $abtestUserstat: string;
    }
}


// 百度统计分流策略
export const baiduTongjiStrategy: ABTestStrategy = {
    name: 'baiduTongji',
    getValue: async (config: ABTestConfig): Promise<number> => {
        return new Promise(resolve => {
            window._hmt.push(['_fetchABTest', {
                paramName: config.paramName,
                defaultValue: -1,
                callback: function (value: number) {
                    resolve(value);
                }
            }]);
        });
    }
};

// 随机分流策略
export const randomStrategy: ABTestStrategy = {
    name: 'random',
    getValue: async (): Promise<number> => {
        return Math.random() < 0.5 ? 0 : 1;
    }
};

// CRC32分流策略
export const crc32Strategy: ABTestStrategy = {
    name: 'crc32',
    getValue: async (_config: ABTestConfig, userId?: string): Promise<number> => {
        if (!userId) {
            console.warn('CRC32 strategy requires userId');
            return -1;
        }
        const hash = crc32.str(userId);
        const unsigned = hash >>> 0;
        return unsigned % 2; // 返回0或1
    }
};

// 策略工厂
export const getStrategy = (strategyName: string): ABTestStrategy => {
    switch (strategyName) {
        case 'baiduTongji':
            return baiduTongjiStrategy;
        case 'random':
            return randomStrategy;
        case 'crc32':
            return crc32Strategy;
        default:
            console.warn(`Unknown strategy: ${strategyName}, falling back to baiduTongji`);
            return baiduTongjiStrategy;
    }
};
