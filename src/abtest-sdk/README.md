# [ABTest SDK 使用文档](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/qD7ZqVQ83l/huROa2CBdVPY8-)

## 1. 简介

基于百度统计（Tongji）A/B测试的React SDK【支持自定义分流策略】，提供了简单易用的A/B测试功能集成。该SDK支持与百度统计无缝集成，并提供了强制命中实验模式用于调试。

- 通过hooks API获取实验值
- 自动订阅Context状态变化
- 实验值变化会触发组件重新渲染

## 2. 安装

```bash
npm install @baidu/abtest
# 或
pnpm add @baidu/abtest
```

## 3. 核心功能

### 3.1 初始化配置

SDK提供了以下主要组件和功能：

1. `ABTestProvider`: React上下文提供者，用于管理全局A/B测试状态
2. `useABTest`: Hook用于访问A/B测试上下文
3. `useABTestValue`: Hook用于获取特定A/B测试的值
4. `initABTestsConfig`: 初始化A/B测试配置的函数

### 3.2 使用示例

```jsx
import { ABTestProvider, useABTestValue } from '@baidu/abtest';

// 定义A/B测试配置
const abTestConfig = {
  featureA: {
    key: 'feature_a',
    paramName: 'feature_a_test',
    value: -1
  },
  featureB: {
    key: 'feature_b',
    paramName: 'feature_b_test',
    value: -1
  }
};

// 在应用根组件中使用Provider
function App() {
  return (
    <ABTestProvider
      abTestConfig={abTestConfig}
      injectScript={() => {
        // 注入百度统计脚本
        const script = document.createElement('script');
        script.src = '//hm.baidu.com/hm.js?YOUR_SITE_ID';
        document.head.appendChild(script);
      }}
    >
      <YourApp />
    </ABTestProvider>
  );
}

// 在组件中使用A/B测试
function YourComponent() {
  const featureAValue = useABTestValue('featureA');

  return (
    <div>
      {featureAValue === 1 ? '实验组' : '对照组'}
    </div>
  );
}
```

## 4. 架构设计

### 4.1 核心架构

SDK采用React Context API实现状态管理，主要包含以下部分：

1. **状态管理层**

- - 使用React Context管理全局A/B测试状态
  - 提供状态访问和更新的接口

1. **初始化层**

- - 负责与百度统计集成
  - 处理A/B测试配置的初始化
  - 支持异步加载和状态更新

1. **工具层**

- - 提供URL参数解析
  - 支持强制命中实验策略
  - 提供用户统计信息生成

### 4.2 数据流

![img](https://rte.weiyun.baidu.com/wiki/attach/image/api/imageDownloadAddress?attachId=d7f6b5b659214f2daba379b289340c0e&docGuid=huROa2CBdVPY8-)



## 5. 高级特性

### 5.1 强制命中实验模式

通过URL参数`forceHitTestFlag`可以强制设置A/B测试的值，格式为：

```plain
?forceHitTestFlag=feature_a-1;feature_b-0
```

### 5.2 实验命中统计

SDK会自动生成实验命中统计信息（userstat），格式为：

```plain
feature_a-1;feature_b-0
```

### 5.3 自定义分流策略

1. 百度统计分流（默认）
2. 随机分流
3. CRC32分流

```jsx
// 使用百度统计策略（默认）
function AppWithBaiduTongji() {
  return (
    <ABTestProvider 
      abTestConfig={abTestConfig}
      injectScript={() => {
        const script = document.createElement('script');
        script.src = '//hm.baidu.com/hm.js?YOUR_SITE_ID';
        document.head.appendChild(script);
      }}
    >
      <App />
    </ABTestProvider>
  );
}

// 使用随机分流策略
function AppWithRandom() {
  return (
    <ABTestProvider 
      abTestConfig={abTestConfig}
      options={{
        strategy: 'random'
      }}
    >
      <App />
    </ABTestProvider>
  );
}
```

**策略模式：**SDK采用了策略模式来实现不同的分流策略：

1. **策略接口**

- - 每个策略都实现了相同的接口
  - 包含 `name` 和 `getValue` 方法
  - `getValue` 方法返回 Promise，确保异步兼容性

1. **策略工厂**

- - 通过 `getStrategy` 函数获取对应的策略实现
  - 支持策略的动态切换
  - 提供默认回退机制

**配置选项**

```tsx
interface ABTestOptions {
  strategy?: 'baiduTongji' | 'random' | 'crc32';
  userId?: string;  // 用于CRC32策略
}
```

源码实现：

```js
import crc32 from 'crc-32';

// 百度统计分流策略
export const baiduTongjiStrategy = {
    name: 'baiduTongji',
    getValue: async (config) => {
        return new Promise(resolve => {
            window._hmt.push(['_fetchABTest', {
                paramName: config.paramName,
                defaultValue: -1,
                callback: function (value) {
                    resolve(value);
                }
            }]);
        });
    }
};

// 随机分流策略
export const randomStrategy = {
    name: 'random',
    getValue: async (config) => {
        return Math.random() < 0.5 ? 0 : 1;
    }
};

// CRC32分流策略
export const crc32Strategy = {
    name: 'crc32',
    getValue: async (config, userId) => {
        if (!userId) {
            console.warn('CRC32 strategy requires userId');
            return -1;
        }
        const hash = crc32.str(userId);
        const unsigned = hash >>> 0;
        return unsigned % 2; // 返回0或1
    }
};

// 策略工厂
export const getStrategy = (strategyName) => {
    switch (strategyName) {
        case 'baiduTongji':
            return baiduTongjiStrategy;
        case 'random':
            return randomStrategy;
        case 'crc32':
            return crc32Strategy;
        default:
            console.warn(`Unknown strategy: ${strategyName}, falling back to baiduTongji`);
            return baiduTongjiStrategy;
    }
};
```

## 6. 注意事项

1. 默认分流策略下，确保在使用SDK前已正确配置百度统计
2. 初始化是异步的，使用`useABTestValue`时需要考虑`pending`状态
3. 强制命中实验模式仅用于开发调试，不要在生产环境使用

## 7. 最佳实践

1. 将A/B测试配置集中管理
2. 使用TypeScript定义配置类型
3. 在关键功能点添加错误处理
4. 合理使用强制测试模式进行开发调试



# 落地场景

直播 H5 和轻舸首页

[评审：CPD-281565 wip 轻舸首页卡片一键采纳能力AB测试](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/fc-fe/fe-cangjie/reviews/116413716/files/base...latest/package.json)

# 其他资料

https://zhuanlan.zhihu.com/p/571901803

[前端 ABTest 的实验机制设计](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/gv0WKdV3ZF/KO91dTP6dJ/HcnM8HYZtrIhrk)