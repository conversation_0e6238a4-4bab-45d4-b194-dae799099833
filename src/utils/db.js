/**
 * @file 一个简单的本地持久化类
 * <AUTHOR>
 * @date 2020-12-22 16:19:23
 */

export default class SettingItem {
    constructor(key) {
        this.key = key;
    }

    get() {
        try {
            let data = localStorage.getItem(this.key);
            if (data) {
                return JSON.parse(data);
            }
        } catch (err) {
            console.warn(err);
            return '';
        }
    }

    isTrue(defaultVal = false) {
        let data = this.get();
        if (!data) {
            return defaultVal;
        }
        return data !== '0' && data !== 'false';
    }

    set(data) {
        try {
            localStorage.setItem(this.key, JSON.stringify(data));
        } catch (error) {
            //
        }
    }

    clear() {
        try {
            localStorage.removeItem(this.key);
        } catch (error) {
            //
        }
    }
}
