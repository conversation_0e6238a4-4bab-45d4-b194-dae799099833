import React from 'react';
import WechatSDK from '@baidu/m-wechat-sdk/es/nativePage.es';
import bmls from '@baidu/bdmedialive-scheme';
import {CopyOutlined} from '@ant-design/icons';
import {message} from 'antd';
import copy from 'copy-to-clipboard';
import {adCommonClickLog} from '@/pages/liveshow/components/AdLiveShow/monitor/ad';

const showWechatToast = (wechatId) => {
    message.info(
        <span style={{display: 'inline-flex', alignItems: 'center', gap: 8}}>
            微信号：{wechatId}
            <CopyOutlined
                onClick={() => {
                    copy(wechatId);
                    message.success('微信号已复制');
                }}
                style={{cursor: 'pointer'}}
            />
        </span>
    );
};

export const anyMountCardUrlTypeMap = {
    common: 0,
    wechat: 1
};

export function isWeChatUrl(anyMountCardUrlType) {
    return anyMountCardUrlType === anyMountCardUrlTypeMap.wechat;
}
function nativeCopyAndToast(toastMessage, accountName) {
    try {
        bmls.common.toast({
            message: toastMessage
        });
        bmls.common.setClipboardData({
            content: accountName
        });
    }
    catch (error) {
        console.error('nativeCopyAndToast: ', error);
    }
    return 'ok';
}
const apiEnv = process.env.NODE_ENV === 'production' ? 'online' : 'offline';

export const wxLaunchImpl = function ({
    solutionId,
    solutionRelatedId,
    from = 'h5',
    h5Options = {},
    isOldVersion = false
}) {
    const {
        status,
        nid,
        screen,
        anyMountCardUrlH5,
        anyMountCardUrlType,
        hasClickShowModel
    } = h5Options;
    if (from  === 'h5') {
        adCommonClickLog({
            page: status,
            value: 'trade_button',
            nid,
            screen,
            serverId: '19091',
            action: `${hasClickShowModel ? 'inner' : 'out'}_card`,
            anyMountCardUrlH5,
            anyMountCardUrlType
        });
    }
    async function inner() {
        let launchRes = '';
        async function launchInner() {
            if (!solutionId || !solutionRelatedId) {
                from === 'h5' ? message.info('客服暂时不在线~') : bmls.common.toast({
                    message: '客服暂时不在线~'
                });
                launchRes = 'solutionId or solutionRelatedId is empty';
                return;
            }
            const wechatInstance = new WechatSDK({
                solutionId,
                siteUrl: location.href,
                apiEnv,
                sourceType: 72001
            });
            // 初始化微信数据
            const wechatData = await wechatInstance.initWechatData({
                solutionId,
                vpUcId: solutionRelatedId
            });
            const antiFraudRes = await wechatInstance.antiFraudCheck({
                siteType: 11001
            });
            const {status, errors = []} = antiFraudRes ?? {};
            const {fanAssistantUrlWithAdInfo = '', fanAssistantStatus} = wechatData?.solutionInfo || {};
            const linkAvailable = +fanAssistantStatus === 1 && fanAssistantUrlWithAdInfo;
            if (!linkAvailable) {
                const {solutionInfo: {accountName = '', account = ''} = {}} = wechatData || {};
                if (from === 'h5') {
                    showWechatToast(accountName || account);
                }
                else {
                    nativeCopyAndToast('已复制企业微信号', accountName || account);
                }
                return null;
            }
            if (status === 200) {
                let launchResult;
                try {
                    launchResult = await wechatInstance.launch();
                }
                catch (error) {
                    console.error('launch error:', error);
                }
                const {status: launchStaus} = launchResult || {};
                launchRes = launchStaus;
                if (+launchStaus === 0) {
                    // 调起成功
                    try {
                        await wechatInstance.submitWechatClue({
                            vpUcId: solutionRelatedId,
                            siteType: 11001,
                            siteUrl: location.href,
                            solutionId,
                            solutionRefType: 67, // 获客链接
                            apiEnv
                        });
                    }
                    catch (error) {}

                }
                else {
                    // 调起微信失败
                    const {solutionInfo: {accountName = ''} = {}} = wechatData || {};
                    if (from === 'h5') {
                        showWechatToast(accountName);
                    }
                    else {
                        nativeCopyAndToast('已复制企业微信号', accountName);
                    }
                }
                return null;
            }
            if (errors.length) {
            // 反作弊校验不通过
                if (from === 'h5' || isOldVersion) {
                    message.warning('客服暂时不在线~');
                }
                else {
                    bmls.common.toast({
                        message: '客服暂时不在线~'
                    });
                }
                launchRes = 'antiFraudCheck failed';
                return null;
            }
            return null;
        }
        await launchInner();
        if (from  === 'h5') {
            adCommonClickLog({
                page: 'live',
                value: 'entry',
                serverId: '19336',
                isNewVersion: true,
                launchRes
            });
        }
        else {
            adCommonClickLog({
                page: 0,
                value: 'entry',
                serverId: '19462',
                anyMountCardUrlH5: location.href,
                launchRes
            });
        }
    }
    inner();
    return 'ok';
};
