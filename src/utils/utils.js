/**
 * @file 常用通用方法集合
 * <AUTHOR>
 * @date 2020-12-08 21:05:45
 */
import {boxVersion, isIOS, versionCompare} from '@baidu/boxx/env';
import system from '@baidu/boxx/system';
import {invokeApp} from '@baidu/xbox/na/ios';

export const buildParam = data => {
    if (typeof data === 'object') {
        return Object.keys(data)
            .map(key => {
                let value = data[key] === undefined ? '' : encodeURIComponent(data[key]);
                return `${encodeURIComponent(key)}=${value}`;
            })
            .join('&');
    }
    return '';
};

export const replaceProtocol = url => {
    const protocol = location.protocol.indexOf('https') >= 0 ? 'https' : 'http';
    if (typeof url !== 'string') {
        return;
    }
    return url.replace(/^(https|http)/, protocol);
};

export const getUrlParam = name => {
    let reg = new RegExp(`(^|(&|/?))${name}=([^&]*)(&|$)`, 'i');
    let r = window.location.search.substr(1).match(reg);
    if (r !== null) {
        return r[3];
    }
    return null;
};

// 随机定长字符串
export const randomString = length => {
    let chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    for (let i = length; i > 0; --i) {
        result += chars[Math.floor(Math.random() * chars.length)];
    }
    return result;
};

// 迁移于npm包 @super-fe/BdrainfnEventDispatch
// eslint-disable-next-line no-unused-vars
const createEventDispatcherMiddleware = () => (eventMap = []) => ({dispatch, getState}) => next => action => {
    if (Object.prototype.toString.call(action) === '[object Object]' && action.eventName) {
        return eventMap
            .map(item => {
                if (
                    (Object.prototype.toString.call(item.eventName) === '[object RegExp]' &&
                        item.eventName.exec(action.eventName)) ||
                    item.eventName === action.eventName
                ) {
                    return item.handler && dispatch(item.handler(action.eventName, action.data, eventMap));
                }
                return '';
            })
            .filter(item => item && typeof item !== 'undefined');
    }
    return next(action);
};

export const eventDispatcher = createEventDispatcherMiddleware();

eventDispatcher.withExtraArgument = createEventDispatcherMiddleware;
/**
 * ios theme切换并在html元素上添加类名 / 即将废弃，可以使用getAndSetTheme代替
 * @return {null}
 */
export const iosThemeSwitch = () => {
    const global = window;

    if (isIOS && versionCompare(boxVersion, '11.12.0') >= 0) {
        let unregister = () => {
            invokeApp('v5/datachannel/unregister', {
                action: 'com.baidu.channel.foundation.themechanged',
                page: 'listpage',
            });
        };
        let setDarkMode = data => {
            let modeClass = data.theme === 'dark' ? 'darkmode' : '';
            let html = document.querySelector('html');
            html.classList.remove('darkmode');
            html.classList.add(modeClass);
        };
        // 夜间模式获取（渲染前，获取）
        let initDarkMode = (global.initDarkMode = res => {
            if (typeof res === 'string') {
                res = JSON.parse(res);
            }
            setDarkMode(res.data);
        });

        // 数据通道注册（渲染后，通知设置夜间模式）
        global.darkModeCallback = (action, data) => {
            if (typeof data === 'string') {
                data = JSON.parse(data);
            }
            setDarkMode(data);
        };

        // 反注册
        global.addEventListener('unload', unregister);
        global.addEventListener('beforeunload', unregister);

        // 方法1：初始化通过UA判断
        let ua = decodeURIComponent(navigator.userAgent).toLocaleLowerCase();
        if (ua.indexOf('theme/dark') > 0) {
            initDarkMode({
                data: {
                    theme: 'dark',
                },
            });
        }

        // 方法2：初始夜间模式(端能力)
        invokeApp('theme/getTheme', {}, initDarkMode);

        // 设置DarkMode模式(数据通道)
        invokeApp('v5/datachannel/register', {
            action: 'com.baidu.channel.foundation.themechanged',
            page: 'listpage',
            jscallback: 'darkModeCallback',
        });
    }
};

/**
 * 星级转换 例如4.5转换成[1, 1, 1, 1, 0.5, 0]，用于渲染星级icon
 * @params {Number} 星级分数
 * @return {Array} 星级数组长度
 */
export const handleStar = (numParam, len = 5) => {
    const num = Number(numParam);
    if (typeof num !== 'number') {
        return [];
    }

    const lower = Math.floor(num);
    let arr = Array.from({length: len}, (v, idx) => {
        if (idx < lower) {
            return 1;
        } else {
            if (numParam - idx > 0) {
                // 有小数
                return numParam - idx;
            } else {
                return 0;
            }
        }
    });
    return arr;
};

/**
 * 日期格式化
 * @function dateFormat
 * @param {Date} d - date 对象
 * @param {string} [pattern = 'yyyy-MM-dd'] - 字符串
 * @return {string} 处理后的字符串
 * @example
 *	var d = new Date();
 *  dateFormat(d," yyyy年M月d日\n yyyy-MM-dd\n MM-dd-yy\n yyyy-MM-dd hh:mm:ss")
 *  //2018年11月10日\n 2018-01-01\n 01-01-18\n 2018-11-12 12:01:02
 */
export const dateFormat = (d, pattern = 'yyyy-MM-dd') => {
    let y = d.getFullYear().toString();
    let o = {
        M: d.getMonth() + 1, // month
        d: d.getDate(), // day
        h: d.getHours(), // hour
        m: d.getMinutes(), // minute
        s: d.getSeconds(), // second
    };
    pattern = pattern.replace(/(y+)/gi, (a, b) => y.substr(4 - Math.min(4, b.length)));
    Object.keys(o).forEach(i => {
        pattern = pattern.replace(new RegExp('(' + i + '+)', 'g'), (a, b) =>
            (o[i] < 10 && b.length > 1 ? '0' + o[i] : o[i])
        );
    });
    return pattern;
};

/**
 * 调用scheme
 * @param {string} scheme
 */
const invokeScheme = scheme => {
    let ifr = document.createElement('iframe');
    ifr.src = scheme;
    ifr.style.display = 'none';
    document.head.appendChild(ifr);
    setTimeout(() => {
        document.head.removeChild(ifr);
        ifr = null;
    }, 0);
};

/**
 * todo: night模式变更时不能及时通知更改
 * 获取模式（light、night、dark）
 * 给html添加模式类名（lightmode、darkmode、nightmode）
 * @params {Function} callback 回调函数
 * @return {String} 模式 light、night、dark
 */
export const getAndSetTheme = callback => {
    const global = window;
    let setMode = data => {
        let theme = 'light';
        let modeClass = 'lightmode';
        let html = document.querySelector('html');

        if (data.theme === 'dark') {
            theme = 'dark';
            modeClass = 'darkmode';
        } else if (data.isNightMode) {
            theme = 'night';
            modeClass = 'nightmode';
        }
        html.classList.remove('nightmode', 'darkmode');
        html.classList.add(modeClass);
        callback && callback(theme);
    };
    // ios
    if (isIOS && versionCompare(boxVersion, '11.12.0') >= 0) {
        let unregister = () => {
            invokeApp('v5/datachannel/unregister', {
                action: 'com.baidu.channel.foundation.themechanged',
                page: 'listpage',
            });
        };
        // 模式获取（渲染前，获取）
        let initDarkMode = (global.initDarkMode = res => {
            if (typeof res === 'string') {
                res = JSON.parse(res);
            }
            setMode(res.data);
        });

        // 数据通道注册（渲染后，通知设置夜间模式）
        global.darkModeCallback = (action, data) => {
            if (typeof data === 'string') {
                data = JSON.parse(data);
            }
            setMode(data);
        };

        // 反注册
        global.addEventListener('unload', unregister);
        global.addEventListener('beforeunload', unregister);

        // 方法1：初始化通过UA判断
        let ua = decodeURIComponent(navigator.userAgent).toLocaleLowerCase();
        if (ua.indexOf('theme/dark') > 0) {
            initDarkMode({
                data: {
                    theme: 'dark',
                },
            });
        }

        // 方法2：初始夜间模式(端能力)
        invokeApp('theme/getTheme', {}, initDarkMode);

        // 设置DarkMode模式(数据通道)
        invokeApp('v5/datachannel/register', {
            action: 'com.baidu.channel.foundation.themechanged',
            page: 'listpage',
            jscallback: 'darkModeCallback',
        });
    }

    // 安卓
    if (!isIOS && versionCompare(boxVersion, '10.0') >= 0) {
        if(versionCompare(boxVersion, '12.24') >= 0) {
            const scheme = 'baiduboxapp://v16/theme/getNightMode?callback=xfunc_getNightMode';
            window.xfunc_getNightMode = res => {
                try {
                    setMode(JSON.parse(decodeURIComponent(res)).data);
                } catch (error) {
                    // eslint-disable-next-line no-console
                    console.error(error);
                }
            };
            invokeScheme(scheme);
        } else {
            system.getNightMode({
                success(res) {
                    setMode(res.data);
                },
                fail(res) {
                    console.log(res);
                },
            });
        }
    }
};
export const jsonToQuery = (json) => {
    if (typeof json === 'string') {
        return json;
    }
    let arr = [];
    Object.keys(json).forEach(i => {
        arr.push(`${i}=${json[i]}`);
    });
    return arr.join('&');
};

/**
 * 返回一个对象字典排序后的字符串
 * @param {Object} data
 * @return {String} 字典排序后的字符串
*/
export const dictionaryOrder = (data, value = '') => {
    let str = '';
    const allkeys = Object.keys(data).sort();
    for (let i = 0, len = allkeys.length; i < len; i++) {
        str += `${allkeys[i]}=${data[allkeys[i]]}${value}`;
    }
    return str;
};