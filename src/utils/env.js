/**
 * @file 处理所有判断代码执行环境的入口
 * <AUTHOR>
 * @date 2020-12-14 15:31:59
 */
/* eslint-disable */
import { isAndroid, isBox, isIOS, isMaybeBox } from '@baidu/xbox-native/detect';
import { versionCompare } from '@baidu/boxx/env';
import getBoxVersion from '@baidu/xbox/get-box-version';
import { getUrlParam } from '@/utils/index';

const ua = navigator.userAgent.toLocaleLowerCase();
const isLiteBox = / ((lite|info) baiduboxapp\/)|baiduboxvision\//i.test(ua); // 百度青春版和lite保持一直
const isBaiduChillin = /baiduboxvision\//i.test(ua);
const isKnews = / knews\//i.test(ua);

const browserList = {
    toutiao: /TTWebView\/([\d\.]+)/i,
    wechat: /MicroMessenger\/([\d\.]+)/i,
    ipadqq: /IPadQQ\/([\d\.]+)/i,
    mqq: /qq\/([\d\.]+)/i,
    qzone: /QZONEJSSDK\/([\d\.]+)/i,
    mqqbrowser: /mqqbrowser\/([\d\.]+)/i,
    qqbrowser: /[^m]QQBrowser\/([\d\.]+)/i,
    x5: /tbs\/(\d+)/i,
    uc: /UCBrowser\/([\d\.]+)/i,
    safari1: /Version\/(([\d\.]+))\sSafari\/[\d\.]+/i,
    safari2: /Safari\/([\d\.]+)/i,
    firefox: /Firefox\/([\d\.]+)/i,
    opera: /OPR\/([\d\.]+)/i,
    ie1: /MSIE\s([\d\.]+)/i,
    ie2: /(trident\/\d\.\d)/i,
    ie3: /(Edge)\/\d+\.\d+/i,
    weibo: /weibo__([\d\.]+)/i,
    qqnewsAd: /TADChid\/([\d\.]+)/i,
    qqnews: /qqnews\/([\d\.]+)/i,
    qqlive1: /QQLiveBrowser\/([\d\.]+)/i,
    qqlive2: /QQLiveHDBrowser\/([\d\.]+)/i,
    kuaibao: /qnreading\/([\d\.]+)/i,
    liebao: /LieBaoFast\/([\d\.]+)/i,
    douban: /com\.douban\.frodo\/([\d\.]+)/i,
    miuibrowser: /MiuiBrowser/i,
    baidulite: /lite baiduboxapp/i,
    baidupro: /pro baidubox/i,
    baiduinfo: /info baidubox/i,
    baidu: /baiduboxapp/i,
    baidukankan: /baiduboxvision/i,
    browser360: /360browser/i,
    oppobrowser: /OppoBrowser/i,
    chrome1: /CriOS\/([\d\.]+)/i,
    chrome2: /Chrome\/([\d\.]+)/i,
    qqdownloader: /qqdownloader\/([\d\.]+)/i,
    nintendo: /NintendoBrowser\/([\d\.]+)/i,
};

const getBrwoserType = (ua = navigator.userAgent) => {
    // eslint-disable-next-line no-restricted-syntax
    for (let i in browserList) {
        if (ua.match(browserList[i])) {
            return i;
        }
    }
    return 'unknown';
};

export const env = {
    isAndroid: isAndroid(),
    isIOS: isIOS(),
    // 主线或者矩阵产品
    isBox: isBox(),
    // 主线
    isMainBox: isBox() && !isMaybeBox('lite,info,mission,pro'),
    // lite
    isLiteBox: isLiteBox || isKnews,
    isBaiduChillin: isBaiduChillin || isKnews, // 这么写是因为Knews这个APPua有问题
    // mission
    isMissionBox: isBox('mission'),
    // 矩阵产品 看多多/lite
    isMatrix: isMaybeBox('lite,info,mission,pro'),
    boxVersion: getBoxVersion(),
    browserType: getBrwoserType(),
    isYoujia: !!navigator.userAgent.match(/youjia/i),
    isBaiduHi: /baiduhi/i.test(ua) || /Infoflow\/iOS/i.test(ua),
    isTomas: /tomas\//i.test(ua), // 大字版
    isTomasSource: /tomas\//i.test(ua) || getUrlParam('source') === 'tomas',
    isWechat: /MicroMessenger\/([\d\.]+)/i.test(ua),
    isHonorBrowser: /bdhonorbrowser/i.test(ua), // 荣耀白牌浏览器
    isHaokan: /haokan\//i.test(ua),
    isBdmap: /baidumap/i.test(ua),
};

export const isMobile = (ua = navigator.userAgent) => !!ua.match(/AppleWebKit.*Mobile.*/);

export const isPC = () => !window.navigator.userAgent.match(/Android|iPhone|SymbianOS|Windows Phone|iPad|Pod/);

export const isOppoBrowser = (ua = navigator.userAgent) => {
    let oppo = !!ua.match(/OppoBrowser/i);
    let heytap = !!ua.match(/HeyTapBrowser/i);
    return oppo || heytap;
};


export const isIE = (ua = navigator.userAgent) => !!ua.match(/MSIE\s([\d\.]+)/i) || !!ua.match(/(trident\/\d\.\d)/i);

export const isAndroidSougou = () => {
    // 安卓 搜狗 因为video劫持问题 导致一些浮层bug 特殊处理一下
    if (env.isAndroid) {
        if (ua.indexOf('sogoumobilebrowser') >= 0) {
            return true;
        }
        if (ua.indexOf('sogoumse') >= 0) {
            return true;
        }
    }
    return false;
};

/**
 * 比较二个版本大小,返回 boolen
 * 如果只有一个参数,默认与比较手百主版本 boxVersion
 * @param  {...string} arg
 * @returns {boolean}
 */
export const isLargerVersion = (...arg) => {
    if (arg.length === 1) {
        return versionCompare(env.boxVersion, arg[0]) >= 0;
    }
    return versionCompare(arg[0], arg[1]) >= 0;
};

/**
 * [getAppName 获取在哪个端]
 *
 * @return  {[string]}  [return 端名称]
 */
 export function getAppName() {
    const UA = window.navigator.userAgent;
    const isInApp = /(bdminivideo|haokan|baiduboxapp|tieba|BaiJiaHao|tomas)\//i.test(UA);
    if (!isInApp) {
        console.error('不在APP内');
        return null;
    }
    const match = /(bdminivideo|haokan|baiduboxapp|tieba|BaiJiaHao|tomas)\/([\d+.]+)/.exec(UA);
    return match ? match[1] : '';
}
