/**
 * @file scrollApprove.js
 * @description 优化iOS滑动效果
 * <AUTHOR>
 */

import { env } from './env';

// 2代表竖屏 1代表横屏
let orientation = 1;

const judgeOrientation = () => {
    orientation = window.innerWidth > window.innerHeight ? 1 : 2;
    return Promise.resolve(orientation);
};

const setOrientation = () => {
    try {
        if (orientation === 2) {
            document.body.classList.remove('landscape');
            document.body.classList.add('portait');
        }
        else {
            document.body.classList.add('landscape');
            document.body.classList.remove('portait');
        }
    }
    catch (e) {
        console.error(e);
    }
};

export const watchOrientation = (callback) => {
    judgeOrientation();
    setOrientation();
    callback && callback(orientation);
    window.addEventListener('onorientationchange' in window ? 'orientationchange' : 'resize', () => {
        setTimeout(() => {
            judgeOrientation();
            setOrientation();
            callback && callback(orientation);
        }, 300);
    }, false);
};

/**
 * @name scrollApprove
 * @description iOS优化滑动效果
 * 1. 通过禁用touchmove以达到禁用body的回弹效果
 * 2. 通过判断点击元素及其父亲的class是否在白名单以让页面的子元素能滚动
 * @param {Array} whiteList 白名单
 */
export const scrollApprove = (whiteList = []) => {
    if (!Array.isArray(whiteList)) {
        return;
    }
    let html = document.documentElement;
    html.style.overflow = 'hidden';
    if (env.isIOS) {
        document.body.addEventListener('touchmove', e => {
            if (orientation === 1) {
                return;
            }
            let target = e.target;
            let flag = false;
            while (target !== document.body && target) {
                target = target.parentElement;
                // todo classList api 是否需要降级方案，这个函数可以优化下跑得更快一点
                for (let i = 0; i < whiteList.length; i++) {
                    if (target.classList.contains(whiteList[i])) {
                        flag = true;
                    }
                }
                // 提前跳出，可以跑得快一点
                if (flag) {
                    break;
                }
            }
            if (!flag) {
                e.preventDefault();
            }
        }, {
            passive: false
        });
    }
};
