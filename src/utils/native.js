/**
 * @file 处理所有与端交互的逻辑(所有函数默认执行于端内)
 * <AUTHOR>
 * @date 2021-03-05 15:08:24
 */
import invoke, {invokeP} from '@baidu/xbox-native/invoke';
import {getBoxVersion, isBox, version_compare} from '@baidu/xbox-native/detect';
import cuid from '@baidu/xbox-native/device/cuid'
import commonUI from '@baidu/boxx/commonUI';
import event from '@baidu/boxx/event';
import {env} from './env';
import {getUrlParam} from '@/utils/index';

/**
 * 手百端内使用 弹窗组件
 * @params {title} 标题 必填
 * @params {content} 内容 必填
 * @params {showCancel} 是否显示取消按钮，默认为 true
 * @params {cancelText} 取消按钮的文字，默认为"取消"，最多 4 个字符，非必传
 * @params {cancelColor}  取消按钮的文字颜色，默认为"#000000"，非必传
 * @params {confirmText} 确定按钮的文字，默认为"确定"，最多 4 个字符，非必传
 * @params {confirmColor} 确定按钮的文字颜色，默认为"#3c76ff", 非必传
 * @return {Promise}
 */
export const appConfirm = params => {
    return invokeP('v19/utils/showModal', {
        params,
    }).then(res => {
        return res.type === 'confirm';
    });
};
/**
 * 手百端内写入剪贴板内容
 * @params {content string} 直接输入文本
 * @return {Promise}
 */
export const setClipboardData = content => {
    return invokeP('v19/utils/setClipboardData', {
        params: {
            data: content || '',
        },
    });
};
/**
 * 手百端内读取剪贴板内容
 * @return {Promise}
 */
export const getClipboardData = () => {
    return invokeP('v19/utils/getClipboardData');
};
/**
 * 手百端内使用 toast弹窗组件
 * @params {msg string} 直接输入文本
 * @params {msg object} 对象结构 {msg,type}
 */
export const toast = msg => {
    if (typeof msg === 'string') {
        commonUI.showToast({
            type: '1',
            message: msg,
        });
    } else {
        commonUI.showToast({
            type: msg.type,
            message: msg.msg,
        });
    }
};
// 关闭当前浏览框架  沉浸式无效
export const close = () => {
    invoke('baiduboxapp://v11/browser/closeWindow');
};
export const appSettings = () => {
    return invokeP('v9/utils/system/settings', {
        params: {
            upgrade: 0,
        },
    });
};
/**
 *  验证端是否有通知权限
 *   @return Boolen
 */
export const isOpenNotice = () => {
    return invokeP('v15/utils/getPermission', {
        params: {
            types: [3],
        },
    }).then(res => {
        // 0 没有权限
        return +res['3'] !== 0;
    });
};
/**
 *  验证端是否强制更新
 *  @params {subName string} 环境osname 手百直接传入''
 *  @params {checkVersion string} 需要比较的版本
 *  @params {checkV Boolen} 是否只比较大版本 例如 12.12 不然全量比较 12.10.10.0.17
 *  @return Number  大于checkVesion 1 小于 -1 等于 0
 */
export const checkUpdateApp = ({subName = '', checkV = true, checkVersion}) => {
    if (!checkVersion) {
        throw 'checkUpdateApp error: no check error';
    }
    let version = getBoxVersion(subName);
    if (checkV) {
        if (version && typeof version === 'string') {
            let match = /\d+\.\d+/.exec(version);
            if (match) {
                version = match[0];
            }
        }
    }
    let size = version_compare(version, checkVersion);
    return size;
};
/**
 *  通知端打开一个url页面
 * @param {String} url
 */
export const openAppUrl = url => {
    // url 链接
    if (url.indexOf('http') === 0) {
        url = decodeURIComponent(url);
        const cmd = `baiduboxapp://v1/easybrowse/open?url=${encodeURIComponent(url)}`;
        invoke(cmd);
    } else {
        invoke(url);
    }
};
/**
 *  通知端打开一个沉浸式url页面
 * @param {String} url
 */
export const openAppImmerseUrl = url => {
    invokeP('v30/immerseBrowser/open', {
        params: {
            url: url,
        },
    });
};
/**
 * 手百端内使用 注册监听与端交互的通道  只使用
 * @params {page string} 可以理解为定义函数生效作用域空间 必填
 * @params {action string} 定义交互通道名称 必填
 * @params {callback func} 响应函数 必填
 * @params {success func} 订阅成功回调函数 非必填
 * @params {fail func} 订阅失败回调函数 非必填
 */
export const registerDataChannel = (() => {
    let registerCallbackId = 0;
    return params => {
        let {callback, page, action, ...info} = params;
        const callbackName = `register_channel_callback_${registerCallbackId++}`;
        event.on({
            allowDuplicate: 'false',
            page,
            action,
            jscallback: callbackName,
            ...info,
        });
        if (window !== undefined) {
            const cancelCallback = () => {
                event.off({page, action});
            };
            window[callbackName] = function (action, data) {
                if (typeof data === 'string') {
                    data = JSON.parse(data);
                    callback(data);
                }
            };
            window.addEventListener('unload', cancelCallback);
            window.addEventListener('beforeunload', cancelCallback);
        }
    };
})();
/**
 * 手百端内使用 注册与端交互的通道 只能使用于端SDK下的webview环境
 * @params {action string} 定义交互通道名称 必填
 * @params {data object} 响应函数 必填
 * @params {success func} 派发成功回调函数 非必填
 * @params {fail func} 派发失败回调函数  非必填
 */
export const emitDataChannel = params => {
    let {action, data = {}, ...info} = params;
    if (isBox) {
        event.emit({
            action,
            data: JSON.stringify(data),
            ...info,
            fail: err => {
                console.error('端事件通知失败', err, info);
            },
        });
    } else {
        console.error('端外不响应端能力');
    }
};
/**
 * @param {*} url
 * @param {*} immerse
 * @param {*} backPosition 返回按钮位置，0是顶部1是底部
 * @return {*} 返回拼接后的scheme
 */
export const createSchemeForUrl = (url, immerse = false, backPosition = 1) => {
    if (!url) {
        throw 'createSchemeForUrl error no url';
    } else {
        let scheme;
        // 使用沉浸式游览器打开
        if (immerse) {
            let params = encodeURIComponent(
                JSON.stringify({
                    url,
                    toolbarposition: backPosition === 1 ? 0 : 1,
                    mediaPlayNeedUserAction: 'NO',
                })
            );
            if (env.isIOS) {
                scheme = `baiduboxapp://v30/immerseBrowser/open?params=${params}`;
            } else {
                scheme = `baiduboxapp://v1/easybrowse/open?url=${encodeURIComponent(url)}&type=immerse&backlocation=${
                    backPosition === 1 ? 1 : 0
                    }&barcolor=00000000&layoutfullscreen=1`;
            }
        } else {
            scheme = `baiduboxapp://v1/easybrowse/open?url=${encodeURIComponent(url)}`;
        }
        return scheme;
    }
};
// 回流到直播间逻辑 http://wiki.baidu.com/pages/viewpage.action?pageId=1369272274
/**
 * @param {*} roomId
 * @param {*} source
 * @param {*} useNewEnter
 * @param {*} backPosition 返回按钮位置，0是顶部1是底部
 */
export const openLiveRoom = ({roomId, source, useNewEnter, backPosition = 1}) => {
    source = source || getUrlParam('source');
    let option = {
        roomId,
        screen: 0,
        source: JSON.stringify({
            livesource: source,
        }),
    };
    if (env.isAndroid) {
        option.source = source;
    }
    // 最新的 enterStreamRoom option配置
    if (useNewEnter) {
        // 生成兼容版本 enterRoom schema
        let backup = 'baiduboxapp://v11/live/enterRoom?params=' + JSON.stringify(option);
        option = {
            roomId,
            source,
            backup,
            extParams: {
                ext: {live_back_for_both_scheme: createSchemeForUrl(location.href, true, backPosition)},
            },
        };
        invokeP('live/enterStreamRoom', {
            params: option,
        });
    } else {
        invokeP('v11/live/enterRoom', {
            params: option,
        });
    }
};
export function getCuid() {
    return new Promise(resolve => {
        if (isBox()) {
            try {
                cuid().then(res => {
                    resolve(res.data || '');
                }).catch(() => {
                    resolve('');
                })
            } catch (e) {
                resolve('');
            }
        } else {
            resolve('');
        }
    });
}

// 公参 ua
export const commonUa = `${window.innerWidth}_${window.innerHeight}_${
        env.isIOS
        ? 'iphone'
        : env.isAndroid ? 'android' : '0'
    }_${getBoxVersion()}_0`;
