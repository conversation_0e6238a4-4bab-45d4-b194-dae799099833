/**
 * @file 封装所有常用请求
 * <AUTHOR>
 * @date 2020-12-10 11:32:57
 */

let callbackId = 0;

const jsonp = (url, data = {}, serverCallbackName = 'callback', overlay = 5000) => {
    if (typeof url !== 'string') {
        throw new Error('params are no right!');
    }
    return new Promise((resolve, reject) => {
        const callbackName = `__jsonp_callback_${callbackId++}__`;
        let paramStr = '';
        data._ = +new Date();
        if (typeof data === 'object' && data !== null) {
            const keys = Object.keys(data);
            keys.forEach((key) => {
                let value = data[key];
                if (typeof value === 'object') {
                    value = JSON.stringify(value);
                }
                paramStr += `${key}=${value}&`;
            });
        }
        paramStr += `${serverCallbackName}=${callbackName}`;

        const requestUrl = `${url}?${paramStr}`;

        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = requestUrl;
        const timeout = window.setTimeout(() => {
            reject();
        }, overlay);
        window[callbackName] = (param) => {
            resolve(param);
            document.body.removeChild(script);
            clearTimeout(timeout);
            delete window[callbackName];
        };
        document.body.appendChild(script);
    });
};

export {jsonp};
