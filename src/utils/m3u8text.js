/**
 * @file m3u8text.js
 * <AUTHOR>
 */
import {request} from '@super-fe/BdrainfnAjax';

const EXTINF = '#EXTINF';


export default class m3u8Text {
    constructor(options) {
        this.callbackMap = {};
        this.options = Object.assign({}, {
            tsSpaceTime: 3000,
            minSpaceTime: 3000,
            forceTime: false,
            stop: false
        }, options);
        this.dataObj = {};
        this.dataAry = [];

        if (this.options.share) {
            this.options.stop = true;
        }

        this.init();
    }

    stop() {
        this.options.stop = true;
        this.callbackMap = {};
    }

    on(type, callback) {
        if (typeof callback === 'function') {
            if (Array.isArray(this.callbackMap[type])) {
                this.callbackMap[type].push(callback);
            }
            else {
                this.callbackMap[type] = [callback];
            }
        }
    }

    fire(type, data) {
        if (Array.isArray(this.callbackMap[type])) {
            let callbacks = this.callbackMap[type];
            callbacks.forEach(callback => {
                callback.call(this, data);
            });
        }
    }

    init() {
        this.loadM3u8File(this.options.file);
    }

    loadM3u8File(url) {
        request({
            data: {url}
        })
            .then(data => {
                this.parseM3u8Text(data);
            }, () => {
                this.parseM3u8Text('');
            });
    }

    loadTsFile(url) {
        let t = this;
        let m3u8Url = t.options.file;
        // 创建标签获取host
        let aDom = document.createElement('a');
        aDom.href = m3u8Url;
        let urlDomain = `${location.protocol}//${aDom.hostname}`;

        url = urlDomain + url;
        request({
            data: {
                url,
                dataType: 'json'
            }
        }).then(data => {
            this.parseTsFileText(url, data);
        });
    }

    parseM3u8Text(text) {
        let txtAry = text.split(/\n/gi);
        let timeSpace = 0;
        try {
            for (let i = 0, len = txtAry.length; i < len - 1; i++) {
                let lineText = txtAry[i].trim();
                let nextLineText = txtAry[i + 1].trim();
                let lineTextAry;
                let tsData = {};
                let curTime = new Date() * 1 + -10 * 1000;
                if (lineText.indexOf(EXTINF) > -1 && !this.dataObj[nextLineText]) {
                    i++;
                    tsData.ts = nextLineText;
                    lineText = lineText.substring(1);
                    lineTextAry = lineText.split(',');
                    lineTextAry.forEach(info => {
                        let sIndex = info.indexOf(':');
                        tsData[info.slice(0, sIndex).trim()] = info.slice(sIndex + 1).trim();
                    });
                    if (tsData['EXT-X-PROGRAM-DATE-TIME']) {
                        let tsTime = new Date(tsData['EXT-X-PROGRAM-DATE-TIME']) * 1;
                        if (tsTime === 'Invalid Date' || tsTime < curTime) {
                            continue;
                        }
                    }
                    timeSpace += tsData['EXTINF'] * 1000;
                    this.pushTsData(tsData);
                }
            }
        }
        catch (e) {
            console.log(e);
        }
        if (this.options.forceTime) {
            timeSpace = this.options.minSpaceTime;
        }
        else {
            if (timeSpace) {
                this.options.tsSpaceTime = timeSpace;
            }
            timeSpace = Math.max(this.options.minSpaceTime, this.options.tsSpaceTime);
        }
        !this.options.stop && setTimeout(() => {
            this.loadM3u8File(this.options.file);
        }, timeSpace);
    }

    // 解析ts文件
    parseTsFileText(url, data) {
        try {
            this.fire('updateTsData', data);
            if (Array.isArray(data.list)) {
                data.list.forEach(item => {
                    if (Array.isArray(item.messages)) {
                        item.messages.forEach(message => {
                            this.fire('resolveMessage', message);
                        });
                    }
                });
            }
        }
        catch (e) {
            console.log(e);
        }
    }

    pushTsData(tsData) {
        this.dataObj[tsData.ts] = tsData;
        this.dataAry.push(tsData.ts);
        this.fire('pushTsData', tsData);
        this.loadTsFile(tsData.ts);
    }

    parseMessage(data) {
        try {
            if (Array.isArray(data.list)) {
                if (Array.isArray(data.list.messages)) {
                    data.list.messages.forEach(message => {
                        this.fire('resolveMessage', message);
                    });
                }
            }
        }
        catch (e) {
            console.log(e);
        }
    }
}
