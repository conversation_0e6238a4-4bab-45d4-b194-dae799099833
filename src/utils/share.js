/**
 * @file 分享与回流相关
 * <AUTHOR>
 * @date 2021-03-05 15:08:24
 */

import shareSDK from '@baidu/xbox-sdk/es5/share';
import openShare from '@baidu/xbox-native/ui/openShare';
import {share} from '@baidu/ug-matrix';
import {PageStore} from '@/pages/liveshow/service/register';
import {versionCompare} from '@baidu/boxx/env';
import {invokeApp} from '@baidu/ug-invoke-app';
import {createSchemeForUrl} from '@/utils/native';
import {IS_APP_MAP} from '@/pages/liveshow/utils/getUrlParam';
import {env} from '@/utils/env';
// http://wiki.baidu.com/pages/viewpage.action?pageId=321232451
// 用于注册模式分享的初始化
export const initShare = (options, successcallback, errorcallback) => {
    const share = shareSDK();
    window.BoxShareData = {
        options: {
            linkUrl: '',
            type: 'url',
            mediaType: 'all',
            title: '',
            content: '',
            source: '',
            imageUrl: '',
            iconUrl: '',
            wbtitle: '',
            ...options,
        },
        successcallback: () => {
            successcallback && successcallback();
        },
        errorcallback: () => {
            errorcallback && errorcallback();
        },
    };
    return share;
};

// js调起分享组件
export const openShareBox = option => {
    return openShare(option);
};

// js调起图片模式分享
export const openShareForImage = shareImageUrl => {
    if (shareImageUrl) {
        openShare({
            type: env.isAndroid ? '3' : 'image', // 图片类型参数
            imageUrl: shareImageUrl,
        });
    } else {
        throw new Error('share image links cannot be empty');
    }
};

export const generateShareUrl = shareInfo => {
    let urlSource = PageStore.source;

    // 拼接矩阵标识 用于端外回流
    Object.keys(IS_APP_MAP).forEach(key => {
        if (IS_APP_MAP[key]) {
            if (!urlSource) {
                urlSource = key;
            }
            else if (urlSource.includes('|')) {
                const tmp = urlSource.split('|');
                urlSource = `${key}|${tmp[1]}`;
            }
            else {
                urlSource = `${key}|${urlSource}`;
            }
        }
    });

    const shareUrl = shareInfo?.share_url?.indexOf('?') > -1
    ? `${shareInfo.share_url}&source=${urlSource}` : `${shareInfo.share_url}?source=${urlSource}`;

    console.log(shareUrl);

    return shareUrl;
};

// 统一分享 兼容 链接、口令、海报
export const unifiedShare = (new_visual, shareInfo, source = 'live') => {

    // 无基础的分享信息
    if (!shareInfo || Object.keys(shareInfo) <= 0) {
        return;
    }
    const {is_share_token = 0, share_token = '', share_poster_status = '0', share_poster = {}} = new_visual;

    // 支持分享口令的 端
    const supportShareToken = env.isMainBox || env.isLiteBox || env.isTomas || env.isHaokan;

    // 支持分享海报的 端
    const supportSharePoster = (env.isMainBox && versionCompare(env.boxVersion, 13.50) >= 0)
    || (env.isLiteBox && versionCompare(env.boxVersion, 6.23) >= 0);

    const shareUrl = generateShareUrl(shareInfo) || location.href;
    console.log(`${shareUrl}`);

    // 分享信息
    let params = {
        linkUrl: shareUrl,
        title: shareInfo.main_title,
        content: shareInfo.sub_title,
        source,
        iconUrl: shareInfo.cover,
        mediaType: 'all',
        imageUrl: shareInfo.cover
    };
    if (is_share_token && share_token && supportShareToken) {
        params = {
            ...params,
            type: 'text',
            textContent: share_token
        }
    }
    else if (+share_poster_status === 1 && share_poster && supportSharePoster) {
        params = {
            ...params,
            type: 'image',
            is_single_row: true,
            panelStyle: '3',
            // 百度特有参数添加
            _baiduboxapp: {
                poster_taloslitedata: share_poster.talosLiteData
            },
            // 极速版特有参数添加
            _baiduboxlite: {
                poster_taloslitedata: share_poster.talosLiteData
            }
        }
    }

    try {
        share(params).then(res => {
            // 分享成功 {status: 0, message: 分享成功}
            console.log(res);
        }).catch(e => {
            // 端能力调用出错 {status: -1, message: '端能力执行失败'}
            // 低版本  {status: 101, message: '该版本不支持此端能力'}
            console.log(e);
        });
    }
    catch (e) {
        console.log(e);
    }
};

/**
 * @name reflow
 * @param {string} url 回流的url链接
 * @param {boolean} immerse 是否使用沉浸式框架打开
 * @param {string} failedUrl 失败兜底中间页
 * @param {string} osname app协议头
 * @param {number} backPosition 返回按钮位置，0是顶部1是底部
 */
export const reflow = ({url = '', immerse = false, failedUrl = '', osname = 'baiduboxapp', backPosition = 1}) => {
    function failReflow(protocol) {
        location.href = `market://details?packagename=${protocol}`;
    }
    let scheme = createSchemeForUrl(url, immerse, backPosition);
    const opts = {
        appName: osname,
        scheme: scheme,
        failedUrl,
    };
    invokeApp(opts)
        .then(res => {
            if (+res.status >= 20000) {
                failReflow(opts.appName);
            }
        })
        .catch(() => {
            failReflow(opts.appName);
        });
};
