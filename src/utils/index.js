/**
 * @file  常用工具函数封装入口
 * <AUTHOR>
 * @date 2021-02-09 19:52:40
 */

import {appProtoName, boxVersion, isAndroid, isBox, isIOS} from '@baidu/boxx/env';

// 支持hash路由下，hash路由时取query用这个
export const getQueryString = name => {
    let reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`, 'i');
    let r = window.location.search.substr(1).match(reg) || window.location.hash.replace(/#\S+?\?/, '').match(reg);
    if (r != null) {
        return decodeURIComponent(r[2]);
    }
    return null;
};

export const getQueryArr = arr => {
    let paramsNewArr = [];
    for (let i = 0; i < arr.length; i++) {
        paramsNewArr.push(getQueryString(arr[i]));
    }
    return paramsNewArr;
};

export const replaceProtocol = url => {
    const protocol = location.protocol.indexOf('https') >= 0 ? 'https' : 'http';
    if (typeof url !== 'string') {
        return;
    }
    return url.replace(/^(https|http)/, protocol);
};

export const getUrlParam = name => {
    let reg = new RegExp(`(^|(&|/?))${name}=([^&]*)(&|$)`, 'i');
    let r = window.location.search.substr(1).match(reg) || window.location.hash.replace(/#\S+?\?/, '').match(reg);
    if (r !== null) {
        return r[3];
    }
    return '';
};

// object 转 str for url
export const buildParam = data => {
    if (typeof data === 'object') {
        return Object.keys(data)
            .map(key => {
                let value = data[key] === undefined ? '' : encodeURIComponent(data[key]);
                return `${key}=${value}`;
            })
            .join('&');
    }
    return '';
};

// 随机定长字符串
export const randomString = length => {
    let chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    for (let i = length; i > 0; --i) {
        result += chars[Math.floor(Math.random() * chars.length)];
    }
    return result;
};

// 注册页面隐藏展示事件
export const registerPageHidden = (hiddenCallback, showCallback) => {
    let property = 'hidden';
    let eventName = 'visibilitychange';
    if (typeof document.hidden !== 'undefined') {
        // Opera 12.10 and Firefox 18 and later support
        property = 'hidden';
        eventName = 'visibilitychange';
    }
    else if (typeof document.msHidden !== 'undefined') {
        property = 'msHidden';
        eventName = 'msvisibilitychange';
    }
    else if (typeof document.webkitHidden !== 'undefined') {
        property = 'webkitHidden';
        eventName = 'webkitvisibilitychange';
    }
    let pageChange = e => {
        if (document[property] || e.hidden || document.visibilityState === 'hidden') {
            hiddenCallback && hiddenCallback();
        }
        else {
            showCallback && showCallback();
        }
    };
    document.addEventListener(eventName, pageChange, false);
};

/**
 * 日期格式化
 * @function dateFormat
 * @param {Date} d - date 对象
 * @param {string} [pattern = 'yyyy-MM-dd'] - 字符串
 * @return {string} 处理后的字符串
 * @example
 *	var d = new Date();
 *  dateFormat(d," yyyy年M月d日\n yyyy-MM-dd\n MM-dd-yy\n yyyy-MM-dd hh:mm:ss")
 *  //2018年11月10日\n 2018-01-01\n 01-01-18\n 2018-11-12 12:01:02
 */
export const dateFormat = (d, pattern = 'yyyy-MM-dd') => {
    let y = d.getFullYear().toString();
    let o = {
        M: d.getMonth() + 1, // month
        d: d.getDate(), // day
        h: d.getHours(), // hour
        m: d.getMinutes(), // minute
        s: d.getSeconds() // second
    };
    pattern = pattern.replace(/(y+)/gi, (a, b) => y.substr(4 - Math.min(4, b.length)));
    Object.keys(o).forEach(i => {
        pattern = pattern.replace(new RegExp('(' + i + '+)', 'g'), (a, b) => {
            return o[i] < 10 && b.length > 1 ? '0' + o[i] : o[i];
        });
    });
    return pattern;
};

/**
 * 更新url参数
 * @param {Object} 要处理的参数集合
 * @param {String} [url] - 要处理的url，不传默认取当前页面url
 * @return {String} 处理后的url字符串
 */
export const updataParamByUrl = (json, url = location.href) => {
    if (!json) {
        return url;
    }

    let path = url.split('?')[0];
    let paramsString = url.split('?')[1] || '';
    let paramsArray = [];
    let paramsObj = {};

    if (paramsString) {
        paramsArray = paramsString.split('&');
    }

    paramsArray.forEach(item => {
        paramsObj[item.split('=')[0]] = item.split('=')[1];
    });

    Object.keys(json).forEach(key => {
        paramsObj[key] = json[key];
    });

    let resParamsString = buildParam(paramsObj);
    resParamsString = resParamsString ? ('?' + resParamsString) : '';
    return path + resParamsString;
};

export const addClassName = (dom, className) => {
    const type = Object.prototype.toString.call(className);

    if (
        !dom
        || !className
        || (type !== '[object String]' && type !== '[object Array]')
    ) {
        return;
    }

    let classList = dom.className.split(' ');
    classList = classList.filter(item => item !== '');

    let addClassList = className;
    if (typeof className === 'string') {
        addClassList = className.split(' ');
    }

    addClassList = addClassList.filter(item => {
        item = item.trim();
        return item !== '' && classList.indexOf(item) < 0;
    });

    if (addClassList.length > 0) {
        dom.className = (classList.join(' ') + ' ' + addClassList.join(' ')).trim();
    }
};

// 删除class, className暂时只支持string类型
export const removeClassName = (dom, className) => {
    if (!dom || !className || typeof className !== 'string') {
        return;
    }

    let removeClassName = className.trim();
    let classList = dom.className.split(' ');
    classList = classList.filter(item => item !== '' && item !== removeClassName);

    dom.className = classList.join(' ').trim();
};

// 是否包含某class，className string
export const hasClass = (dom, className) => {
    if (!dom || !className || typeof className !== 'string') {
        return;
    }

    const classList = dom.className.split(' ');
    return classList.indexOf(className.trim()) >= 0;
};

// 仅在live.baidu.com 域名下安卓回手百新增微信开放标签
export const isLiveDomain = location.host === 'live.baidu.com';
