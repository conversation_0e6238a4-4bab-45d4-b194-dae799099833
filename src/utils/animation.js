/**
 * @file 动画小组件
 * <AUTHOR>
 */
/**
 * @name 动画形式
 * @param currentTime 当前时间
 * @param start 起始值
 * @param changed 距离
 * @param duration
 */
const easeInOutQuad = (currentTime, start, changed, duration) => {
    currentTime /= duration / 2;
    if (currentTime < 1) {
        return changed / 2 * currentTime * currentTime + start;
    }
    currentTime--;
    return -changed / 2 * (currentTime * (currentTime - 2) - 1) + start;
};
/**
 * @name  滚动动画
 * @param element 节点
 * @param dir  方向 scrollTop or scrollLeft
 * @param to  滑动距离
 * @param duration
 */

export const tinyScrollTo = (element, dir, to, duration) => {
    let start = element[dir];
    let change = to - start;
    let currentTime = 0;
    let increment = 20;
    const animateScroll = () => {
        currentTime += increment;
        element[dir] = easeInOutQuad(currentTime, start, change, duration);
        if (currentTime < duration) {
            setTimeout(animateScroll, increment);
        }
    };
    animateScroll();
};
