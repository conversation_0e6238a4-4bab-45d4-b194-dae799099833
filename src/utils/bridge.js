/**
 * @file 与端交互通用逻辑封装
 * <AUTHOR>
 * @date 2021-02-03 13:15:41
 */
import invoke, {invokeP} from '@baidu/xbox-native/invoke';
import {isBox} from '@baidu/boxx/env';
import event from '@baidu/boxx/event';
import {env} from '@/utils/env';
import commonUI from '@baidu/boxx/commonUI';

/**
 * 手百端内使用 弹窗组件
 * @params {title} 标题 必填
 * @params {content} 内容 必填
 * @params {showCancel} 是否显示取消按钮，默认为 true
 * @params {cancelText} 取消按钮的文字，默认为"取消"，最多 4 个字符，非必传
 * @params {cancelColor}  取消按钮的文字颜色，默认为"#000000"，非必传
 * @params {confirmText} 确定按钮的文字，默认为"确定"，最多 4 个字符，非必传
 * @params {confirmColor} 确定按钮的文字颜色，默认为"#3c76ff", 非必传
 * @return {Promise}
 */
export const confirm = params => {
    return invokeP('v19/utils/showModal', {
        params,
    }).then(res => {
        return res.type === 'confirm';
    });
};
/**
 * 手百端内使用 toast弹窗组件
 * @params {msg string} 直接输入文本
 * @params {msg object} 对象结构 {msg,type}
 */
export const toast = msg => {
    if (isBox) {
        if (typeof msg === 'string') {
            commonUI.showToast({
                type: '1',
                message: msg,
            });
        } else {
            commonUI.showToast({
                type: msg.type,
                message: msg.msg,
            });
        }
    } else {
        console.error('端外不响应端能力');
    }
};
/**
 * 手百端内使用 跳转至 新 url
 * @params {url string} 必填
 */
export const gotoUrl = url => {
    // url 链接
    if (url.indexOf('http') === 0) {
        url = decodeURIComponent(url);
        const cmd = `baiduboxapp://v1/easybrowse/open?url=${encodeURIComponent(url)}`;
        invoke(cmd);
    } else {
        invoke(url);
    }
};

/**
 * 手百端内使用 注册与端交互的通道
 * @params {page string} 可以理解为定义函数生效作用域空间 必填
 * @params {action string} 定义交互通道名称 必填
 * @params {callback func} 响应函数 必填
 * @params {success func} 订阅成功回调函数 非必填
 * @params {fail func} 订阅失败回调函数 非必填
 */
export const registerDataChannel = (() => {
    let registerCallbackId = 0;
    return params => {
        let {callback, page, action, ...info} = params;
        if (isBox) {
            const callbackName = `register_channel_callback_${registerCallbackId++}`;
            event.on({
                allowDuplicate: 'false',
                page,
                action,
                jscallback: callbackName,
                ...info,
            });
            if (window != undefined) {
                const cancelCallback = () => {
                    event.off({page, action});
                };

                window[callbackName] = function (action, data) {
                    if (typeof data === 'string') {
                        data = JSON.parse(data);
                        callback(data);
                    }
                };

                window.addEventListener('unload', cancelCallback);
                window.addEventListener('beforeunload', cancelCallback);
            }
        } else {
            console.error('端外不响应端能力');
        }
    };
})();
/**
 * 手百端内使用 注册与端交互的通道 只能使用于端SDK下的webview环境
 * @params {action string} 定义交互通道名称 必填
 * @params {data object} 响应函数 必填
 * @params {success func} 派发成功回调函数 非必填
 * @params {fail func} 派发失败回调函数  非必填
 */
export const emitDataChannel = params => {
    let {action, data = {}, ...info} = params;
    if (isBox) {
        event.emit({
            action,
            data: JSON.stringify(data),
            ...info,
            fail: err => {
                console.error('端事件通知失败', err, info);
            },
        });
    } else {
        console.error('端外不响应端能力');
    }
};

// 新增 bdmedialive 协议相关端能力封装

/**
 * 构造完整scheme
 * @param {String} scheme
 * @param {Object} data
 * @param {String} callbackName
 * @return {String} 拼接后的scheme
 */
export const buildScheme = (scheme, data = {}, callbackName = '', ext = {}) => {
    if (scheme.endsWith('?')) {
        scheme = scheme.slice(0, -1);
    }
    scheme = scheme + '?tab=h5&source=h5&tag=h5';
    if (Object.keys(data).length) {
        ext.params = encodeURIComponent(JSON.stringify(data));
    }
    ext = {
        ...ext,
        callback: callbackName,
    };
    Object.keys(ext).forEach(k => {
        let v = ext[k];
        if (v !== '' && v !== undefined && v !== null) {
            scheme += `&${k}=${v}`;
        }
    });
    return scheme;
};

/**
 * scheme执行
 *
 * @param {String} scheme  拼接的scheme
 * @return {Promise} 调用的返回
 */
const invokeScheme = scheme => {
    return new Promise((res, rej) => {
        if (!scheme) {
            rej('scheme should not be empty');
            return;
        }
        let $node = document.createElement('iframe');
        $node.style.display = 'none';
        $node.src = scheme;
        const body = document.body || document.getElementsByTagName('body')[0];
        body.appendChild($node);
        setTimeout(() => {
            body.removeChild($node);
            $node = null;
        }, 0);
    });
};

let index = 0;
export const invokeLive = ({schemeName, data = {}, callback = true, ext = {}}) => {
    return new Promise((resolve, reject) => {
        let scheme;
        if (callback) {
            index++;
            const cbName = `_bdlive_${Date.now()}_${index}`;
            const cbNameTimeout = cbName + '_TIMEOUT';
            scheme = buildScheme(schemeName, data, cbName, ext);

            window[cbName] = res => {
                try {
                    res = JSON.parse(decodeURIComponent(res));
                    if (res && +res.status === 0) {
                        resolve(res);
                    } else {
                        reject(res);
                    }
                } catch (error) {
                    reject();
                }
                clearTimeout(window[cbNameTimeout]);
                delete window[cbName];
                delete window[cbNameTimeout];
            };

            window[cbNameTimeout] = setTimeout(() => {
                delete window[cbName];
                delete window[cbNameTimeout];
                reject('SCHEME_METHOD_TIMEOUT');
            }, 5000);
        } else {
            scheme = buildScheme(schemeName, data, '', ext);
        }
        invokeScheme(scheme);
    });
};

const executeScheme = scheme => {
    return invokeLive({
        schemeName: 'bdmedialive://bdmedialive/common/executeScheme',
        data: {
            scheme,
        },
    });
};

export const appConfirm = params => {
    // 手百12.21版本及以上支持
    if (env.isMainBox) {
        const scheme = buildScheme('baiduboxapp://v19/utils/showModal', params);
        return executeScheme(scheme);
    } else {
        return invokeLive({
            schemeName: 'bdmedialive://bdmedialive/common/showModal',
            data: params,
        });
    }
};

export const setClipboardData = content => {
    // 手百12.21版本及以上支持
    if (env.isMainBox) {
        const scheme = buildScheme('baiduboxapp://v19/utils/setClipboardData', {
            data: content || '',
        });
        return executeScheme(scheme);
    } else {
        return invokeLive({
            schemeName: 'bdmedialive://bdmedialive/common/setClipboardData',
            data: {
                content,
            },
        });
    }
};

export default invoke;
