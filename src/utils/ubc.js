import ubcLog from '@baidu/ubc-report-sdk';

class UbcLog {
    constructor(configs = {}) {
        const { params = {}, ...base } = configs;
        this.configs = {
            testMode: false,
            version: '2.0',
            type: 0,
            idtype: 0,
            timestamp: +new Date(),
            from: 'liveshow',
            ...base,
        };

        this.sendParams = {
            page: '',
            type: '',
            value: '',
            ...params,
            ext: {
                fromH5: 1
            }
        };

        this.baseServerId = '';
        this._initMap = {};
    }

    _extendBase(opt) {
        let ext = this.sendParams.ext;
        let configs = Object.assign({}, this.sendParams, opt);
        if (opt.ext || ext) {
            configs.ext = Object.assign({}, ext || {}, opt.ext || {});
        }
        return configs;
    }

    _init(id) {
        if (!this._initMap[id]) {
            ubcLog(id + '.init', id, this.configs);
            this._initMap[id] = true;
            if (!this.baseServerId) {
                this.baseServerId = id;
            }
        }
    }

    init(ids) {
        if (ids) {
            if (typeof ids === 'string') {
                this._init(ids);
            } else if (typeof ids === 'object') {
                // 数组
                ids.forEach(id => {
                    this._init(id);
                });
            }
        } else {
            throw new Error('no serverId for ubc log');
        }
    }

    send(params = {}, isTiming) {
        let { serverId, config = {} } = params;

        if (!serverId) {
            if (!this.baseServerId) {
                throw new Error('no serverId for ubc log');
            }
            serverId = this.baseServerId;
        }

        if (!this._initMap[serverId]) {
            if (isTiming) {
                this.configs = {
                    ...this.configs,
                    type: 1,
                    duration: 10,
                    timingId: serverId,
                };
            }
            this._init(serverId);
        }

        let data = this._extendBase(config);

        if (isTiming) {
            ubcLog(serverId + '.timing', 'start', serverId, data);
        } else {
            ubcLog(serverId + '.event', data);
        }
    }

    endTimingLog(serverId, isTiming) {
        if (!serverId) {
            throw new Error('no serverId for ubc log');
        }
        if (isTiming) {
            ubcLog(serverId + '.timing', 'end', serverId);
        } else {
            ubcLog(serverId + '.event', 'end', serverId);
        }
    }
}

export default UbcLog;
