/**
 * @File         : 回流处理
 * <AUTHOR> gaojiajun01
 * @Date         : 2021-08-10 19:52:14
 * @LastEditors  : gaojiajun01
 * @LastEditTime : 2021-08-10 20:32:12
*/
import {invokeApp} from '@baidu/ug-invoke-app';
import {getUrlQuery} from '@baidu/mvideo-tool';
import {isIOS} from '@baidu/boxx/env';

const appNameList = {
    1: 'mobilebaidu',
    2: 'haokan',
    3: 'quanmin',
    4: 'tieba'
};

function format(obj) {
    let str = '';
    Object.keys(obj).forEach(item => {
        if (typeof obj[item] === 'object') {
            str += `${item}=${encodeURIComponent(JSON.stringify(obj[item]))}&`;
        }
        else {
            str += `${item}=${encodeURIComponent(obj[item])}&`;
        }
    });
    return str.slice(0, -1);
}

export const schemeMap = {
    haokan: {
        webviewScheme({url}) {
            const scheme = 'baiduhaokan://webview/?';
            const objString = format({
                url_key: url
            });
            return scheme + objString;
        },
        appName: 'baiduhaokan'
    },
    quanmin: {
        webviewScheme({url}) {
            const scheme = 'bdminivideo://webview?';
            const objString = format({
                params: {
                    url_key: url
                }
            });
            return scheme + objString;
        },
        appName: 'bdminivideo'
    },
    tieba: {
        webviewScheme({url}) {
            const scheme = 'com.baidu.tieba://unidispatch/tbwebview?';
            const objString = format({
                url
            });
            return scheme + objString;
        },
        appName: 'tieba'
    },
    mobilebaidu: {
        webviewScheme({url, mobilebaiduConfig = {}}) {
            let scheme = '';
            let objString = null;
            if (mobilebaiduConfig.useEasyBrowser) {
                scheme = 'baiduboxapp://v1/easybrowse/open?';
                objString = format({
                    url
                });
            }
            else {
                // 沉浸式框架打开
                scheme = 'baiduboxapp://v30/immerseBrowser/open?';
                // 具体配置可参考http://icode.baidu.com/repos/baidu/jady/eop-matrix/blob/master:src/native/baidubox/openWebview.ts
                if (isIOS) {
                    scheme = 'baiduboxapp://v30/immerseBrowser/open?params=?';
                    objString = format({
                        forbidautorotate: 1,
                        toolbarposition: 1,
                        url,
                        newbrowser: 1
                    });
                }
                else {
                    scheme = 'baiduboxapp://v1/easybrowse/open?';
                    objString = format({
                        type: 'immerse', // 安卓使用沉浸式框架打开，保证体验一致
                        append: 0,
                        style: '',
                        url,
                        newbrowser: 1,
                        forbidautorotate: 1
                    });
                }
            }
            return scheme + objString;
        },
        appName: 'baiduboxapp'
    }
};

const backflow = ({
    url,
    mobilebaiduConfig = {
        useEasyBrowser: true
    }
}) => {
    // url上用参数c区分哪个端，c=1表示回流到手百
    const c = getUrlQuery('c') || '1';
    const {appName, webviewScheme} = schemeMap[appNameList[c]];
    const scheme = webviewScheme({url, mobilebaiduConfig});

    invokeApp({
        appName,
        scheme
    });
};

export {backflow};
