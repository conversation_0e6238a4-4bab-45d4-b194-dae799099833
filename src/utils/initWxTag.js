/**
 * @file createWxTag
 * <AUTHOR>
 */

const liveTabScheme = 'baiduboxapp://v11/appTab/select?item=home&upgrade=0&params=%7B%22channel%22%3A%2240%22%2C%22tab_info%22%3A%7B%22id%22%3A%2240%22%2C%22name%22%3A%22%E7%9B%B4%E6%92%AD%22%2C%22canDelete%22%3A%220%22%7D%7D';

export const Status = {
    TRY_JUMP: 90001,
    INVOKE_SUCCESS: 10000,
    APPSTORE_SUCCESS: 10001,
    APK_SUCCESS: 10002,
    YYB_SUCCESS: 10003,
    FAILEDURL_SUCCESS: 10004,
    COPY_FAILED: 20004,
    INVOKE_FAIL: 20005,
    NOT_SUPPORT_SYSTEM: 20006,
    INVOKE_APPSTORE_FAIL: 20007,
    INVOKE_ULINK_FAIL: 20008,
    APPNAME_NOT_SUPPORT: 20011,
    NOT_SUPPORT_WX_TAG: 20012,
    PARAMS_ERROR: 21000
};

function insertDom(targetDom, wxTagId, scheme, appid) {
    const wxTag = document.createElement('wx-open-launch-app');
    wxTag.setAttribute('id', wxTagId);
    wxTag.setAttribute('appid', appid);
    wxTag.setAttribute('extinfo', scheme);
    wxTag.setAttribute('style', 'position: absolute; left: 0; right: 0; top: 0; bottom: 0;z-index: 10;');
    const wxScript = document.createElement('script');
    wxScript.setAttribute('type', 'text/wxtag-template');
    const wxBtn = document.createElement('div');
    wxBtn.setAttribute('style', 'width: 100%; height: 100%; position: absolute; bottom: 0px;');
    wxScript.appendChild(wxBtn);
    wxTag.appendChild(wxScript);
    targetDom.appendChild(wxTag);
}

function execCreateWxTag(options) {
    options.scheme = options.scheme || liveTabScheme;
    // 创建标签插入DOM中
    insertDom(options.targetDom, options.wxTagId, options.scheme, 'wx3fcdd8310a136ff8');
    // 创建监听事件
    document.addEventListener('WeixinOpenTagsError', e => {
        if (options.onFailed) {
            options.onFailed({
                status: Status.NOT_SUPPORT_WX_TAG,
                msg: '不支持微信开放标签',
                e,
            });
        }
    });
    const btn = document.querySelector(`#${options.wxTagId}`); // live-wx-btn
    btn.addEventListener('click', () => {
        // 复制口令
        // if (options.token) {
        //     await copyText(options.token);
        // }
        if (options.onClick) {
            options.onClick();
        }
    });
    btn.addEventListener('launch', () => {
        if (options.onSucceed) {
            options.onSucceed({
                status: Status.INVOKE_SUCCESS,
                msg: '调起成功',
            });
        }
    });
    btn.addEventListener('error', (e) => {
        if (options.onFailed) {
            options.onFailed({
                status: Status.INVOKE_FAIL,
                msg: '调起失败',
                e: e.detail,
            });
        }
    });
    // 注入数据
    wx.config({
        debug: options.debug || false,
        appId: options.wxInfo.appId,
        timestamp: options.wxInfo.timestamp,
        nonceStr: options.wxInfo.nonceStr,
        signature: options.wxInfo.signature,
        jsApiList: [
            'onMenuShareWeibo',
            'onMenuShareTimeline', // 分享到朋友圈
            'onMenuShareAppMessage', // 分享给好友
            'onMenuShareQZone', // 分享到QQ空间
            'onMenuShareQQ', // 分享到QQ好友
            'updateAppMessageShareData', // 新版分享给好友
            'updateTimelineShareData', // 新版分享朋友圈
        ],
        openTagList: ['wx-open-launch-app'],
    });
}

export function initWxTag(options) {
    options.appName = options.appName || 'baiduboxapp';
    if (typeof wx !== 'object') {
        const scriptTag = document.createElement('script');
        scriptTag.setAttribute('src', '//res.wx.qq.com/open/js/jweixin-1.6.0.js');
        scriptTag.setAttribute('type', 'text/javascript');
        scriptTag.onload = () => {
            execCreateWxTag(options);
        };
        document.getElementsByTagName('head')[0].appendChild(scriptTag);
    }
    else {
        console.log('[live] 检测到微信JS-SDK已被引入，请确保SDK版本大于等于1.6.0');
        execCreateWxTag(options);
    }
}