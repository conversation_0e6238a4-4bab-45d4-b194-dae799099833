/**
 * @file 判断是否为空
 * <AUTHOR>
 * @date 2020-09-15 18:33:34
 */

export const isEmpty = (target) => {
    // '' null undefined 0
    if (!target) {
        return true;
    }
    // 空数组
    // eslint-disable-next-line no-prototype-builtins
    if (Array.prototype.isPrototypeOf(target) && target.length === 0) {
        return true;
    }
    // 空对象
    // eslint-disable-next-line no-prototype-builtins
    if (Object.prototype.isPrototypeOf(target) && Object.keys(target).length === 0) {
        return true;
    }
    return false;
};

// 与 isEmpty 区别 0不是空  'null'  'undefined' 等字符串是空
export const isSuperEmpty = (a) => {
    if (a === 0) {
        return false;
    }
    if (a === 'null' || a === 'undefined') {
        return true;
    }
    return isEmpty(a);
};
