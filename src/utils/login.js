/**
 * @file login.js
 * @description 登录相关
 * <AUTHOR>
 */
import getBoxVersion from '@baidu/xbox/get-box-version';
import versionCompare from '@baidu/xbox/version_compare';
import openLoginDialog from '@baidu/xbox-native/ui/openLoginDialog';
import {env} from '@/utils/env';

export default (options = {}, type) => {
    const version = getBoxVersion();
    const loginParams = Object.assign({url: location.href}, options);
    // 6.5 第三方登录
    if (versionCompare(version, '6.5') >= 0) {
        loginParams.third_login = '1';
    }
    return new Promise((resolve, reject) => {
        if ((env.isMainBox && versionCompare(version, '5.5') >= 0) || (env.isLiteBox && !env.isBaiduChillin)) {
            loginParams.func = '__login__callback__';
            window.__login__callback__ = res => {
                if (!type) {
                    // 兼容以前页面，保留之前登录逻辑
                    if (options.resolve) {
                        resolve();
                    } else {
                        window.location.reload(true);
                    }
                } else {
                    // 登录逻辑修改，登录成功返回resolve或reload，登录失败/取消登录执行reject
                    let resData = typeof res === 'string' ? JSON.parse(res) : {};
                    let data = resData.data || {};
                    if (data.status === '1') {
                        // 登录成功
                        if (options.resolve) {
                            resolve();
                        } else {
                            window.location.reload(true);
                        }
                    } else {
                        // 取消登录或登录失败
                        reject();
                    }
                }
            };
            openLoginDialog(loginParams);
        } else {
            // 5.5以下版本跳转至baidu passport web页登录 不支持回调
            let passurl = 'http://wappass.baidu.com/?adapter=1&regLink=1';
            if (loginParams.login_type === 'sms') {
                passurl += '&sms=1';
            }
            if (loginParams.subpro) {
                passurl += `&subpro=${loginParams.subpro}`;
            }
            if (loginParams.tpl) {
                passurl += `&tpl=${loginParams.tpl}`;
            }
            window.location.href = `${passurl}&u=${encodeURIComponent(loginParams.url || window.location.href)}`;
        }
    });
};
