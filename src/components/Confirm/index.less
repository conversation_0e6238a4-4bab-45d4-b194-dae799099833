/*
 * <AUTHOR> mengling
 * @File         : 用途
 * @Date         : 2021-05-17 11:44:02
 * @LastEditors  : gaojiajun01
 * @LastEditTime : 2021-05-21 18:50:16
 */
.confirm-wrapper {
    position: fixed;
    left: 0;
    top: 150%;
    width: 100%;
    height: 100%;
    z-index: -9;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    font-size: 48pr;
    transition: all 0.5s;
    opacity: 0;
    &.show-confirm {
        top: 0;
        opacity: 1;
        z-index: 1100;
    }
    .confirm-content {
        width: 926pr;
        height: 991pr;
        // padding: 20pr;
        margin: 0 auto;
        // margin-top: -20pr;
        background: url('./img/confirm_bg.png') no-repeat;
        background-size: 100% 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border-radius: 10pr;
        position: relative;

        .close-icon {
            width: 102pr;
            height: 102pr;
            background: url('./img/close.png') no-repeat;
            background-size: 100% 100%;
            position: absolute;
            right: 41pr;
            top: 43pr;
        }
        .top {
            font-family: Helvetica;
            font-size: 72pr;
            color: #ffffff;
            letter-spacing: 0;
            margin: 0 auto;
            margin-top: 295pr;
            text-align: center;

        }
        .content {
            text-align: center;
            margin-top: 59pr;
            font-family: PingFangSC-Regular;
            font-size: 60pr;
            color: #ffffff;
            letter-spacing: 0;
        }
        .bottom {
            width: 100%;
            height: 100pr;
            display: flex;
            margin-bottom: 130pr;
            justify-content: center;
            align-items: center;
            .action-btn {
                height: 100pr;
                line-height: 100pr;
                text-align: center;
            }
            .left-btn {
                width: 310pr;
                height: 144pr;
                line-height: 144pr;
                background: url('./img/cancel_btn_bg.png') no-repeat;
                background-size: 100% 100%;
                font-family: PingFangSC-Medium;
                font-size: 52pr;
                color: #ffffff;
                letter-spacing: 0;
                text-align: center;
            }
            .right-btn {
                width: 429pr;
                height: 163pr;
                line-height: 163pr;
                background: url('./img/ensure_btn_bg.png') no-repeat;
                background-size: 100% 100%;
                margin-left: 78pr;
                font-family: PingFangSC-Medium;
                font-size: 52pr;
                color: #ffffff;
                letter-spacing: 0;
                text-align: center;
                &.only-btn{
                    margin-left: 0;
                }
            }
        }
    }
}
