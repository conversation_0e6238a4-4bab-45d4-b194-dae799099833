/**
 * <AUTHOR> mengling
 * @File         : confirm 提示框
 * @Date         : 2021-05-08 16:04:50
 * @LastEditors  : mengling
 * @LastEditTime : 2021-05-20 17:09:37
 */
import React, { memo } from 'react';
import PropTypes from 'prop-types';

import './index.less';
/**
 *confirm
 */
class Confirm extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            clickBtn: false,
            _title: '提示',
            _leftText: '取消',
            _rightText: '确认',
            _tipText: '您确定要执行该操作吗？',
        };
    }

    static defaultProps = {
        clickBtnAction: () => {},
        ensureFn: () => {},
        baseConfig: {
            title: '提示',
            leftText: '取消',
            rightText: '确认',
            tipText: '您确定要执行该操作吗？',
            mainStyle: {},
            topStyle: {},
            contentStyle: {},
            bottomStyle: {},
            onlyBtn: false,
        },
    };

    static propTypes = {
        cancelFn: PropTypes.func,
        ensureFn: PropTypes.func,
        baseConfig: PropTypes.object,
    };

    clickBtnAction = idx => {
        const { cancelFn, ensureFn } = this.props;
        this.setState({
            clickBtn: true,
        });
        if (idx) {
            ensureFn();
            return;
        }
        cancelFn();
    };
    render() {
        const { showConfirm, baseConfig } = this.props;
        const { title, leftText, rightText, tipText, mainStyle, topStyle, contentStyle, bottomStyle, onlyBtn } =
            baseConfig;
        const { _title, _leftText, _rightText, _tipText } = this.state;
        return (
            <div className={`confirm-wrapper ${showConfirm ? 'show-confirm' : ''}`} style={mainStyle}>
                <div className='confirm-content'>
                    <div className='close-icon' onClick={() => this.clickBtnAction(0)}></div>
                    <div className='top' style={topStyle}>
                        {title || _title}
                    </div>
                    <div className='content' style={contentStyle}>
                        {tipText || _tipText}
                    </div>
                    <div className='bottom' style={bottomStyle}>
                        {!onlyBtn ? (
                            <div className='action-btn left-btn' onClick={() => this.clickBtnAction(0)}>
                                {leftText || _leftText}
                            </div>
                        ) : null}
                        <div
                            className={`action-btn right-btn ${onlyBtn ? 'only-btn' : ''}`}
                            onClick={() => this.clickBtnAction(1)}
                        >
                            {rightText || _rightText}
                        </div>
                    </div>
                </div>
            </div>
        );
    }
}

export default Confirm;
