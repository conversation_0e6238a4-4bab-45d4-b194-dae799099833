/**
 * @file loading
 * <AUTHOR>
 */

import React, {Component} from 'react';
import './index.less';

class Loading extends Component {
    constructor(props) {
        super(props);
        this.state = {};
    }

    render() {
        const iconLight = require('./imgs/loading-light.png');
        const iconDark = require('./imgs/loading-dark.png');

        return (
            <div className='loading'>
                <img className='theme-light' src={iconLight} />
                <img className='theme-dark' src={iconDark} />
                <p>加载中...</p>
            </div>
        );
    }
}

export default Loading;
