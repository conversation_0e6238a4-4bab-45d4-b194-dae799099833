/**
 * @file 组件样式 熊掌扫光动效
 * <AUTHOR> <<EMAIL>>
 */

@import "~@baidu/nano-theme/index.less";
// 使用 ~ 前缀
@import (reference) "~@baidu/wuji-uikit/index.less";
@prefix-cls: s-flash;

.s-flash {
    display: flex;
    flex-direction: row;
    min-height: 300pr;

    align-items: center;
    flex: 1;
    justify-content: center;
}

// 全屏fixed垂直居中
.s-flash-fullpage {
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 100%;

    align-items: center;
    justify-content: center;
}

.s-flash-logo {
    position: relative;
    overflow: hidden;
    width: 441pr;
    height: 142pr;
    margin: 0 auto;
    background-image: url("./flash-logo.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.s-flash-flash {
    width: 100%;
    height: 100%;
    background-image: url("./flash-light.png");
    background-repeat: no-repeat;
    background-size: auto 100%;
    animation: flash 1.3s infinite;
}

.s-flash-mask {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-position: 50% 50%;
    background-size: 100% 100%;
}

.darkmode,
.nightmode {
    .s-flash-logo {
        background-image: url("./nightmode-flash-logo.png");
    }
    .s-flash-mask {
        background-image: url("./darkmode-flash-mask.png");
    }
}

@keyframes flash {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(100%);
    }
}
