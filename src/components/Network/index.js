/**
 * @file 公共组件 - 网络状态组件 包括加载中、网络异常、暂无数据等
 * <AUTHOR>
 */

import React from 'react';
import './index.less';
import cls from 'classnames';

const Network = props => {
    const {className, onClickLoad, loadingStatus, errmsg} = props;

    function getClass() {
        return cls('loading-wrapper', {
            [className]: !!className,
        });
    }

    function clickFun() {
        if (loadingStatus === 'err') {
            if (onClickLoad) {
                onClickLoad();
            } else {
                location.reload();
            }
        }
    }

    function getMessage() {
        switch (loadingStatus) {
            case 'loading':
                return '加载中……';
            case 'nodata':
                return '暂无数据';
            case 'nostart':
                return '榜单未开启，敬请期待';
            case 'err':
                return '网络异常，点此重试';
            default:
                return '';
        }
    }

    return (
        <div className={getClass()} onClick={clickFun}>
            <p>{errmsg || getMessage()}</p>
        </div>
    );
};

export default Network;