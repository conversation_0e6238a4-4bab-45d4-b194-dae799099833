import React from 'react';
import './index.less';
import {handleStar} from '@/utils/utils';

/**
 * 星级评分
 * @params {Number} number 星级得分
 * @params {Number} full 总分（共需要展示几个星星）默认五颗星
 * @params {String} baseColor 星星默认色值（没有得分时色值） 默认#E0E0E0
 * @params {String} fullColor 得分高亮的色值 默认#FF3333
 * @params {String} fontSize 控制星星宽度 默认20px
 * @params {String} lineHeight 星星高度 默认20px
 * @params {String} margin 星星间距 默认10px
 * @params {Number} step 默认按照0.5处理小数部分星星展示
 * @return {Null}
 */
export default function Star(props) {
    let {
        number = 0,
        fontSize = '20px',
        lineHeight = '20px',
        margin = '10px',
        full = 5,
        baseColor = '#E0E0E0',
        fullColor = '#FF3333',
        step,
    } = props;
    if (step) {
        let remainder = number % 1;
        if (remainder && remainder > 0.5) {
            number = Math.floor(number) + 1;
        } else if (remainder && remainder < 0.5) {
            number = Math.floor(number) + 0.5;
        }
    }
    let defaultStar = {
        color: baseColor,
        lineHeight: lineHeight,
        fontSize: fontSize,
        marginRight: margin,
    };
    let fullStarFun = value => {
        return {
            color: fullColor,
            width: value,
        };
    };
    let clickItemStar = idx => {
        props.clickItemStar && props.clickItemStar(idx + 1);
    }
    return (
        <div className="innerstar-wrapper">
            {handleStar(number, full).map((item, idx) => (
                <span className="org_star" style={defaultStar} key={idx} onClick={() => clickItemStar(idx)}>
                    {item !== 0 && (
                        <span className="full-star" style={fullStarFun(item * 100 + '%')}>
                            &#xe62b;
                        </span>
                    )}
                    &#xe62b;
                </span>
            ))}
        </div>
    );
}
