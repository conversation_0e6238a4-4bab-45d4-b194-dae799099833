@import "~@baidu/nano-theme/index.less";
// 使用 ~ 前缀
@import (reference) "~@baidu/wuji-uikit/index.less";

body.no-scroll {
    position: fixed;
    left: 0;
    width: 100%;
}

// 容器内容居中
.centerfy(@direction: row) {
    display: flex;
    flex-direction: @direction;
    align-items: center;
    justify-content: center;
}

.ellipsis() {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-width: 0;
}

@prefix-cls: s-dialog;
@icon-cls: s-icon;
@camCodeGC11: #Compatible[@GC11];

.@{prefix-cls} {
    overflow: hidden;
    position: relative;
    z-index: 2;
    border-radius: 39pr;
    width: 993pr;
    text-align: center;
    .theme-background(GC10);

    &-wrap {
        position: fixed;
        z-index: 10000;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        .centerfy(column);
        .theme-color(GC1);
    }

    &-header {
        padding: 84pr 81pr 0;
        line-height: 1;
        font-size: 60pr;
        .ellipsis();
        .theme-color(GC1);
    }

    &-content {
        padding: 72pr 81pr 90pr;
        font-size: 48pr;
        line-height: 68pr;
        white-space: pre-wrap;
        .theme-color(GC4);

        &-enhance {
            .theme-color(GC1);
        }
    }

    &-footer {
        display: flex;
        .theme-color(BC35);
        .theme-hairline(top, GC34);
    }

    &-cancel,
    &-confirm {
        flex: 1;
        width: 100%;
        height: 144pr;
        font-size: 54pr;
        .centerfy();

        &:active span {
            opacity: .2;
        }
    }

    &-confirm&-hairline {
        .theme-hairline(left, GC34);
    }

    &-close {
        &:active {
            opacity: .2;
        }

        .@{selector-dark} &,
        .@{selector-night} & {
            opacity: .8;
        }

        &-inside {
            position: absolute;
            top: 21pr;
            right: 21pr;
            display: flex;
            .@{icon-cls} {
                font-size: 78pr;
                line-height: 1;
                width: 78pr;
                height: 78pr;
                color: #ccc;
            }
        }

        &-outside {
            position: relative;
            z-index: 2;
            margin-top: 123pr;

            .@{icon-cls} {
                width: 111pr;
                height: 111pr;
                color: #fff;
            }
        }
    }

    &-mask {
        position: absolute;
        z-index: 1;
        top: 0;
        bottom: 0;
        width: 100%;
        max-width: 750px;
        animation: fade-in .3s linear 1 forwards;
        .theme-background(GC11);

        .@{selector-dark} & {
            animation-name: dark-fade-in;
        }
    }
}

@keyframes fade-in {
    0% {
        background-color: rgba(0, 0, 0, 0);
    }

    100% {
        background-color: #Default[@@camCodeGC11];
    }
}

@keyframes dark-fade-in {
    0% {
        background-color: rgba(0, 0, 0, 0);
    }

    100% {
        background-color: #Dark[@@camCodeGC11];
    }
}
