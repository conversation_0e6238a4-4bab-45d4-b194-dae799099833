/**
 * @file doalog组件
 * <AUTHOR>
 * @date 2021-03-04 15:52:13
 */
import React, {Component} from 'react';
import './index.less';
import {createPortal} from 'react-dom';
import {autobind} from 'core-decorators';

class Dialog extends Component {
    constructor(props) {
        super(props);
        this.state = {
            node: undefined,
        };
    }

    static getDerivedStateFromProps(props, state) {
        const document = window.document;
        if (props.visible) {
            if (state.node) {
                return null;
            }
            const node = document.createElement('div');
            document.body.appendChild(node);

            const scrollTop = document.scrollingElement.scrollTop;
            document.body.classList.add('no-scroll');
            document.body.style.top = `-${scrollTop}px`;
            return {
                node,
            };
        } else {
            if (state.node) {
                document.body.removeChild(state.node);

                document.body.classList.remove('no-scroll');
                document.documentElement.scrollTop = Math.abs(parseInt(document.body.style.top, 10));
                document.body.scrollTop = Math.abs(parseInt(document.body.style.top, 10));
                document.body.style.top = '';
            }
            return {
                node: undefined,
            };
        }
    }

    get contentClass() {
        const title = this.props.title;
        return ['s-dialog-content', !title && 's-dialog-content-enhance'].filter(c => c).join(' ');
    }

    get confirmClass() {
        const showCancel = this.props.showCancel;
        return ['s-dialog-confirm', showCancel && 's-dialog-hairline'].filter(c => c).join(' ');
    }

    get closeClass() {
        const closeType = this.props.closeType;
        return ['s-dialog-close', !!closeType && `s-dialog-close-${closeType}`].filter(c => c).join(' ');
    }

    get hasFooter() {
        const {cancelText, showConfirm, showCancel, confirmText} = this.props;
        return (cancelText && showCancel) || (confirmText && showConfirm);
    }

    get hasClose() {
        const {showClose} = this.props;
        return showClose || !this.hasFooter;
    }

    @autobind
    handleTouchmove(e) {
        e.preventDefault();
    }

    @autobind
    handleMaskClick() {
        const maskClosable = this.props.maskClosable;
        maskClosable && this.handleClose();
    }

    @autobind
    handleClose() {
        this.props.onClose && this.props.onClose();
    }

    @autobind
    handleCancel() {
        this.handleClose();
        this.props.onCancel && this.props.onCancel();
    }

    @autobind
    handleConfirm() {
        this.handleClose();
        this.props.onConfirm && this.props.onConfirm();
    }

    render() {
        const {
            visible,
            title,
            content,
            contentAlign,
            cancelText,
            cancelColor,
            confirmText,
            confirmColor,
            showCancel,
            showConfirm,
            closeType,
            children,
            className,
        } = this.props;
        if (!visible) {
            return null;
        }
        return createPortal(
            <div className="s-dialog-wrap" onTouchMove={this.handleTouchmove}>
                <div className={`s-dialog ${className}`}>
                    {children ? (
                        children
                    ) : (
                        <React.Fragment>
                            {title && <div className="s-dialog-header">{title}</div>}
                            <div className={this.contentClass} style={{textAlign: contentAlign}}>
                                {content}
                            </div>
                            {this.hasFooter && (
                                <div className="s-dialog-footer">
                                    {showCancel && (
                                        <div
                                            className="s-dialog-cancel"
                                            style={{color: cancelColor || ''}}
                                            onClick={this.handleCancel}
                                        >
                                            <span>{cancelText}</span>
                                        </div>
                                    )}
                                    {showConfirm && (
                                        <div
                                            className={this.confirmClass}
                                            style={{color: confirmColor || ''}}
                                            onClick={this.handleConfirm}
                                        >
                                            <span>{confirmText}</span>
                                        </div>
                                    )}
                                </div>
                            )}
                            {this.hasClose && closeType === 'inside' && (
                                <div className={this.closeClass} onClick={this.handleClose}>
                                    <span className="s-icon">x</span>
                                </div>
                            )}
                        </React.Fragment>
                    )}
                </div>
                {this.hasClose && closeType === 'outside' && (
                    <div className={this.closeClass} onClick={this.handleClose}>
                        <img className="s-icon" src={require('./img/close-icon.png')} />
                    </div>
                )}
                <div className="s-dialog-mask" onClick={this.handleMaskClick} />
            </div>,
            this.state.node
        );
    }
}

Dialog.defaultProps = {
    title: '',
    content: '',
    contentAlign: 'center',
    cancelText: '',
    cancelColor: '',
    confirmText: '',
    confirmColor: '',
    showCancel: false,
    showConfirm: true,
    showClose: false,
    closeType: 'inside',
    maskClosable: false,
};

export default Dialog;
