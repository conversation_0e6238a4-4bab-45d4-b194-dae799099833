@keyframes fliploader {
    0% {
        background-color: rgba(58, 57, 57, .2);
        -webkit-transform: rotateY(0);
        transform: rotateY(0);
    }

    40% {
        -webkit-transform: rotateY(90deg);
        transform: rotateY(90deg);
    }

    50% {
        background-color: rgba(58, 57, 57, .4);
        -webkit-transform: rotateY(180deg);
        transform: rotateY(180deg);
    }

    60% {
        -webkit-transform: rotateY(270deg);
        transform: rotateY(270deg);
    }

    to {
        background-color: rgba(58, 57, 57, .2);
        -webkit-transform: rotateY(1turn);
        transform: rotateY(1turn);
    }
}

.data-loading-box {
    display: flex;
    width: 100%;
    height: 180pr;
    align-items: center;
    justify-content: center;

    .data-loading {
        display: flex;
        align-items: center;
        justify-content: center;

        .data-icon-wrap {
            margin: 0 0 0 -20pr;
            -webkit-transform: scale(.5) translateZ(0);
            transform: scale(.5) translateZ(0);

            .data-icon {
                width: 80pr;
                height: 80pr;
                border-radius: 100% 100%;
                background-color: #f5f5f5;
                animation-duration: .8s;
                animation-timing-function: linear;
                animation-iteration-count: infinite;
            }
        }
    }

    p {
        // margin-left: 19pr;
        font-size: 36pr;
        color: #999;
    }
}
