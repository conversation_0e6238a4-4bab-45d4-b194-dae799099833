/**
 * @file 带有loading效果的滚动组件
 * <AUTHOR>
 * @date 2021-03-13 14:46:15
 */
import React from 'react';
import InfiniteScroll from 'react-infinite-scroller';
import './index.less';

function Loading(props) {
    // loadingState 0未开始 1 loading 2 加载完成 3加载错误
    const {retry, loadingState, LoadingText, LoadendText, LoaderrText, animationName} = props;
    return (
        <React.Fragment>
            {!!loadingState && (
                <div className="data-loading-box">
                    {loadingState === 1 && (
                        <div className="data-loading">
                            <div className="data-icon-wrap">
                                <div className="data-icon" style={{animationName}}></div>
                            </div>
                            <p className="data-loading-p">{LoadingText}</p>
                        </div>
                    )}
                    {loadingState === 2 && <p>{LoadendText}</p>}
                    {loadingState === 3 && <p onClick={retry}>{LoaderrText}</p>}
                </div>
            )}
        </React.Fragment>
    );
}

export default function ScrollList(props) {
    const {
        loadMore,
        hasMore,
        loadingState,
        retry = () => {},
        pageStart = 0,
        useWindow = false,
        initialLoad = false,
        LoadingText = '正在加载...',
        LoadendText = '没有更多了',
        LoaderrText = '加载失败 点击重新加载',
        animationName = 'fliploader'
    } = props;
    const loadingOption = {
        retry,
        loadingState,
        LoadingText,
        LoadendText,
        LoaderrText,
        animationName
    };
    return (
        <InfiniteScroll
            loadMore={loadMore}
            hasMore={hasMore}
            pageStart={pageStart}
            initialLoad={initialLoad}
            useWindow={useWindow}
        >
            {props.children}
            <Loading {...loadingOption} />
        </InfiniteScroll>
    );
}
