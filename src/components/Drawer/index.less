.component__drawer--mask {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    height: 100%;
    z-index: 999;
    &.component__drawer--maskShow {
        animation-name: component__drawer--maskFade;
        animation-fill-mode: forwards;
    }
    &.component__drawer--maskHidden {
        animation-name: component__drawer--maskHidden;
        animation-fill-mode: forwards;
    }
}
.component__drawer--children {
    position: fixed;
    width: 100%;
    left: 0;
    bottom: 0;
    z-index: 1000;
    transform: translate3d(0, 100%, 0);
    will-change: transform;
}
.component__drawer--slideUp {
    animation-name: component__drawer-slideUp;
    animation-fill-mode: forwards;
    animation-timing-function: cubic-bezier(.55,0,.55,.2);
}
.component__drawer--slideDown {
    animation-name: component__drawer-slideDown;
    animation-fill-mode: forwards;
    animation-timing-function: cubic-bezier(.55,0,.55,.2);
}
@keyframes component__drawer-slideUp {
    0% { transform: translate3d(0, 100%, 0); }
    100% { transform: translate3d(0, 0, 0); }
}
@keyframes component__drawer-slideDown {
    0% { transform: translate3d(0, 0, 0); }
    100% { transform: translate3d(0, 100%, 0); }
}
@keyframes component__drawer--maskFade {
    0% { opacity: 0; }
    100% { opacity: 1; }
}
@keyframes component__drawer--maskHidden {
    0% { opacity: 1; }
    100% { opacity: 0; }
}
