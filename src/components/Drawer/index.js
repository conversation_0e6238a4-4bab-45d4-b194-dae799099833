/**
 * @File         : 底部呼出抽屉组件
 * <AUTHOR> gaojiajun01
 * @Date         : 2021-04-27 16:36:20
 * @LastEditors  : gaojiajun01
 * @LastEditTime : 2021-05-22 17:49:51
 */
import React, {useEffect, useState, useRef, memo} from 'react';
import {createPortal} from 'react-dom';
import cn from 'classnames';

import './index.less';

const Mask = ({ style, onClose, isOpen }) => {
    return (
        <div
            className={cn('component__drawer--mask', {
                'component__drawer--maskShow': isOpen,
                'component__drawer--maskHidden': !isOpen,
            })}
            style={{
                backgroundColor: style.maskBgColor || 'rgba(0, 0, 0, .4)',
                animationDuration: style.animationDuration || '.3s',
            }}
            onClick={() => {
                if (onClose) {
                    onClose();
                }
            }}
        />
    );
};

const Drawer = ({ open, children, style = {}, onClose, afterClose }) => {
    const [hiddenAnimationEnd, setHiddenAnimationEnd] = useState(false);
    const [isFirstRender, setIsFirstRender] = useState(true);
    const drawerRootDom = useRef();

    useEffect(() => {
        if (open) {
            setIsFirstRender(false);
        }
    }, [open]);

    useEffect(() => {
        if (hiddenAnimationEnd) {
            document.body.removeChild(drawerRootDom.current);
            drawerRootDom.current = null;
            if (afterClose) {
                afterClose();
            }
        }
    }, [hiddenAnimationEnd]);

    const getIfHiddenAnimationEnd = () => {
        if (!open) {
            setHiddenAnimationEnd(true);
        } else {
            setHiddenAnimationEnd(false);
        }
    };
    const getContainer = () => {
        if (drawerRootDom.current) {
            return drawerRootDom.current;
        }
        const container = document.createElement('div');
        container.className = `component__drawer--${Date.now()}`;
        document.body.append(container);
        drawerRootDom.current = container;
        return drawerRootDom.current;
    };

    if (open || !hiddenAnimationEnd && !isFirstRender) {
        const drawerChildren = (
            <React.Fragment>
                <Mask style={style} onClose={onClose} isOpen={open} />
                <div
                    className={cn('component__drawer--children', {
                        'component__drawer--slideUp': open,
                        'component__drawer--slideDown': !open,
                    })}
                    style={{
                        animationDuration: style.animationDuration || '.3s',
                    }}
                    onAnimationEnd={getIfHiddenAnimationEnd}
                >
                    {children}
                </div>
            </React.Fragment>
        );
        return createPortal(drawerChildren, getContainer());
    }
    return null;
};

export default memo(Drawer);
