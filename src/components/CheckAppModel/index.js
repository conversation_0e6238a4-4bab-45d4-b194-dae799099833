/**
 * @file 一个端上验证版本弹窗组件
 * <AUTHOR>
 * @date 2021-03-15 11:30:05
 */

import React, {Component} from 'react';
import Dialog from '@components/Dialog';
import {autobind} from 'core-decorators';
import {checkUpdateApp} from '@/utils/native';
import './index.less';

class CheckAppModel extends Component {
    state = {
        visible: false,
    };

    componentDidMount() {
        this.check();
    }

    @autobind
    clickBtn() {
        if (this.props.openUrl) {
            location.href = this.props.openUrl;
        }
    }

    check() {
        let size = checkUpdateApp({
            checkVersion: this.props.version,
            checkV: this.props.checkV === undefined ? true : this.props.checkV
        });
        if (size === -1) {
            this.setState({visible: true});
        }
    }

    render() {
        const {visible} = this.state;
        const {version, tip} = this.props;
        return (
            <Dialog className="app-check-card" showClose={false} visible={visible}>
                <div className="check-box">
                    <div className="check-title">发现新版本</div>
                    <div className="check-version">V{version}</div>
                    <div className="check-tip">{tip}</div>
                    <button className="check-button" onClick={this.clickBtn}>
                        立即升级
                    </button>
                </div>
            </Dialog>
        );
    }
}

CheckAppModel.defaultProps = {
    openUrl: '',
    version: '12.12',
    tip: '当前版本过低，为领取福利并\n获得更好的体验，请及时升级',
};

export default CheckAppModel;
