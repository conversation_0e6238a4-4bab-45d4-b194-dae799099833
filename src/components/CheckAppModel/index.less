.app-check-card {
    width: 990pr !important;
    overflow: visible !important;
    background-color: transparent !important;
    border-radius: 63pr;

    .check-box {
        border-radius: 63pr;
        width: 990pr;
        height: 1230pr;
        background-image: url(./img/bg.png);
        background-size: 100% 100%;
        background-position: center;
        background-repeat: no-repeat;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-end;

        .check-title {
            font-size: 72pr;
            color: #1F1F1F;
            letter-spacing: 0;
            line-height: 1;
            text-shadow: 0 3pr 7pr rgba(30, 34, 70, .08);
            font-weight: 600;
            margin-bottom: 60pr;
        }

        .check-version {
            font-size: 48pr;
            color: #858585;
            letter-spacing: 0;
            line-height: 1;
            margin-bottom: 20pr;
        }

        .check-tip {
            font-size: 48pr;
            color: #1F1F1F;
            line-height: 60pr;
            white-space: pre-wrap;
            margin-bottom: 167pr;
        }

        .check-button {
            width: 750pr;
            height: 150pr;
            background: #4E6EF2;
            border-radius: 75pr;
            font-size: 60pr;
            color: #FFF;
            letter-spacing: 0;
            font-weight: 500;
            margin-bottom: 89pr;

            &:active {
                opacity: .3;
            }
        }
    }
}
