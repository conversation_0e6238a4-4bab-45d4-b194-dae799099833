/**
 * @File         : 固定间隔&&匀速弹幕组件，设计思想是控制动画时长保证所有弹幕速度相同；不同于其他弹幕组件，多是动画时长相同，速度不同
 * <AUTHOR> gaojiajun01
 * @Date         : 2021-05-10 10:29:38
 * @LastEditors  : gaojiajun01
 * @LastEditTime : 2021-05-22 17:49:23
*/
import React, {useEffect, useRef, Fragment, useMemo, useImperativeHandle, forwardRef, memo} from 'react';
import ReactDOM from 'react-dom';

import './index.less';

const [TRACK_IDLE, TRACK_RUNNING] = [0, 1];

const Danmu = ({
    danmuList,
    danmuConfig = {},
    checkIfGetMoreDanmu,
    danmuRender,
    danmuParentRef
}, ref) => {
    // 弹幕配置
    const danmuDefaultConfig = useMemo(() => ({
        // 弹幕列表轮询频率
        refreshFrequency: 400,
        // 弹幕运行速度45px/s
        speed: 45,
        animateTimeFun: 'linear',
        animateName: 'medialive-danmu-slide',
        // 弹幕间间距
        margin: 75,
        // 轨道高度
        trackHeight: 50,
        trackNumber: 3,
        trackClassName: ''
    }));
    // 实际弹幕配置
    const danmuUseConfig = useMemo(() => ({
        ...danmuDefaultConfig,
        ...danmuConfig
    }));
    // 存储所有弹幕
    const danmuTrackData = useRef([]);
    // 轨道空闲状态
    const danmuTrackStatus =  useMemo(() => Array(danmuUseConfig.trackNumber).fill(TRACK_IDLE));
    const intervalId = useRef(null);
    // 当前操作的弹幕索引
    const nowIndex = useRef(0);
    // 每个轨道临时存放的弹幕dom节点
    const singleDanmuTrack = useMemo(() => Array(danmuUseConfig.trackNumber).fill().map(_ => []), []);
    const parentRef = danmuParentRef && danmuParentRef.current;
    // 父元素的宽度
    const parentContainerWidth =  parentRef && parentRef.offsetWidth || document.body.clientWidth;
    // 父元素在视野中的right距离
    const parentContainerRight =  parentRef && parentRef.getBoundingClientRect().right || document.body.clientWidth;
    const checkIfGetMoreDanmuRef = useRef();
    checkIfGetMoreDanmuRef.current = checkIfGetMoreDanmu;

    // 返回item所在的轨道是否可以插入新的弹幕
    const checkTrackCanUse = (item) => {
        const itemPos = item.getBoundingClientRect();
        // 轨道中最后一个元素尚未完全进入展示区域，不允许下一条弹幕进入
        if (itemPos.right > parentContainerRight - danmuUseConfig.margin) {
            return false;
        }
        // 在同一个轨道内，弹幕速度相同的情况下，不会产生追及问题，下一条弹幕可以进入
        return true;
    };
    // 可以使用的弹幕轨道
    const findCanUseTrack = () => {
        let idx = -1;
        // 先找到第一个空闲的轨道
        for (let i = 0; i < danmuTrackStatus.length; i++) {
            if (danmuTrackStatus[i] === TRACK_IDLE) {
                return i;
            }
        }
        // 没有空闲的轨道，从上到下遍历弹幕轨道，选出可增加弹幕的轨道
        for (let i = 0; i < danmuTrackStatus.length; i++) {
            const len = singleDanmuTrack[i].length;
            if (len) {
                // 当前轨道里最后一个dom元素
                const item = singleDanmuTrack[i][len - 1];
                if (item && checkTrackCanUse(item)) {
                    return i;
                }
            }
        }
        return idx;
    };
    const renderDanmu = (dom, useTrackNumber) => {
        ReactDOM.render(
            danmuRender(danmuTrackData.current[nowIndex.current]),
            dom,
            () => {
                document.getElementById(`medialive-danmu-track__${useTrackNumber}`).appendChild(dom);
                dom.style.animationDuration = (dom.offsetWidth + parentContainerWidth) / danmuUseConfig.speed + 's';
                dom.style.animationName = danmuUseConfig.animateName;
                dom.style.animationTimingFunction = danmuUseConfig.animateTimeFun;
                dom.addEventListener('animationend', () => {
                    // 移除dom节点
                    ReactDOM.unmountComponentAtNode(dom);
                    dom.remove();
                    singleDanmuTrack[useTrackNumber].shift();
                    if (!singleDanmuTrack[useTrackNumber].length) {
                        danmuTrackStatus[useTrackNumber] = TRACK_IDLE;
                    }
                });
            }
        );
    };
    const createDanmuDom = (useTrackNumber) => {
        const dom = document.createElement('div');
        const domId = Math.random().toString(36).substring(2);
        dom.id = domId;
        dom.style.position = 'absolute';
        dom.style.visibility = 'hidden';
        dom.style.willChange = 'transform,visibility';
        dom.style.whiteSpace = 'nowrap';
        dom.style.transform = `translate3d(${parentContainerWidth}px, 0, 0)`;
        singleDanmuTrack[useTrackNumber].push(dom);
        renderDanmu(dom, useTrackNumber);
        danmuTrackStatus[useTrackNumber] = TRACK_RUNNING;
        nowIndex.current += 1;
    };
    const initDanmuPlay = () => {
        intervalId.current = setInterval(() => {
            const danmuTrackDataLength = danmuTrackData.current.length;
            if (danmuTrackDataLength) {
                if (nowIndex.current === danmuTrackDataLength) {
                    // 已有的弹幕都播完了
                } else {
                    const canUseTrack = findCanUseTrack();
                    if (canUseTrack > -1) {
                        // 创建弹幕并插入轨道
                        createDanmuDom(canUseTrack);
                        if (checkIfGetMoreDanmuRef.current) {
                            checkIfGetMoreDanmuRef.current(nowIndex.current, danmuTrackData.current.length);
                        }
                    } else {
                        // 没有空闲轨道等下一次再判断
                        return;
                    }
                }
            }
        }, danmuUseConfig.refreshFrequency);
    };

    useImperativeHandle(ref, () => ({
        insertDanmu(pushDanmu) {
            danmuTrackData.current.splice(nowIndex.current, 0, pushDanmu);
        }
    }));
    useEffect(() => {
        return () => {
            clearInterval(intervalId.current);
            intervalId.current = null;
        };
    }, []);
    useEffect(() => {
        if (danmuList.length) {
            danmuTrackData.current = [...danmuTrackData.current, ...danmuList];
            if (!intervalId.current) {
                initDanmuPlay();
            }
        }
    }, [danmuList]);

    return (
        <Fragment>
            {
                [...Array(danmuUseConfig.trackNumber)].map((_, idx) => {
                    return (
                        <div
                            key={idx}
                            className={
                                `medialive-danmu-track${
                                    danmuUseConfig.trackClassName
                                        ? ` ${danmuUseConfig.trackClassName}`
                                        : ''
                                }`
                            }
                            id={`medialive-danmu-track__${idx}`}
                            style={{
                                height: danmuUseConfig.trackHeight + 'px'
                            }}
                        ></div>
                    );
                })
            }
        </Fragment>
    );
};

export default memo(forwardRef(Danmu));
