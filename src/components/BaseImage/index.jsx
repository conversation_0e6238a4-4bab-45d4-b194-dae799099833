import React from 'react';
import './index.less';

const sizeType = [
    '1:1',
    '2:3', // extra added
    '3:1',
    '3:2',
    '3:4',
    '4:3',
    '5:2',
    '16:9',
    '21:4',
    'full',
];
const sizeMap = {};
const defaultSize = sizeType[0];
sizeType.forEach(size => {
    if (size === 'full') {
        sizeMap[size] = 0;
    } else {
        const sizeVal = size.split(':');
        sizeMap[size] = sizeVal[1] / sizeVal[0];
    }
});

export default function BaseImage(props) {
    let {url, size = defaultSize, circle, isFull, defaultImage} = props;
    let imageStyle = {
        paddingTop: `${sizeMap[size] * 100}%`,
        height: isFull ? '100%' : 'auto',
        backgroundImage: url ? `url(${url})` : '',
        borderRadius: circle ? '50%' : '',
    };
    return (
        <div className={`s-image-wrapper ${props.className ? props.className : ''}`}>
            <div
                className={`s-image-default${circle ? ' s-image-circle' : ''}`}
                style={defaultImage ? defaultImage : {}}
            />
            <div className="s-image" style={imageStyle}>
                {props.children}
            </div>
        </div>
    );
}
