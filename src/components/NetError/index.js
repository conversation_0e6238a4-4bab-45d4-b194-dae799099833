/**
 * @file 网络异常
 * <AUTHOR>
 */

import React, {Component} from 'react';
import './index.less';

class NetError extends Component {
    constructor(props) {
        super(props);
        this.state = {};
    }

    // this.props.reloadPage没传时，默认执行此方法
    netErrReload() {
        location.reload();
    }

    render() {
        const iconLight = require('./imgs/icon-nonet-light.png');
        const iconDark = require('./imgs/icon-nonet-dark.png');

        return (
            <div className='no-net'>
                <img
                    className='theme-light'
                    src={iconLight}
                    onClick={this.props.reloadPage || this.netErrReload} />
                <img
                    className='theme-dark'
                    src={iconDark}
                    onClick={this.props.reloadPage || this.netErrReload} />
                <p>
                    网络不佳，请重试
                </p>
            </div>
        );
    }
}

export default NetError;
