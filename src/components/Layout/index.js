
import React, {Component} from 'react';
import vSpeed from '@baidu/mvideo-vspeed';
import {get} from '@baidu/mvideo-tool';
import './index.less';

class Layout extends Component {
    componentDidMount() {
        if (
            !(
                get(window.vSpeedConfig, 'on', false)
                && get(window.vSpeedConfig, 'autoFMP', false)
            )
        ) {
            return;
        }
        vSpeed.listenFmpByAuto(document.getElementById('root'));
        setTimeout(() => {
            vSpeed.sendCount({group: 'bml_all'});
            vSpeed.sendDist({group: 'bml_all'});
        }, 1000);
    }

    render() {
        return (
            <div className="main">
                {this.props.children}
            </div>
        );
    }
}

export default Layout;
