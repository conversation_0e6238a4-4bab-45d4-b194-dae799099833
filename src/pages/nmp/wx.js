import React from 'react';
// import bmls from '@baidu/bdmedialive-scheme';
import {PageStore} from '../liveshow/service/register';
import {wxLaunchImpl} from '../../utils/wxLaunch';

export default function WX() {
    const {
        solution_support_type = '',
        solution_id = '',
        solution_related_id = ''
    } = PageStore.queryParams;
    window.openWeChatFromJS = function () {
        return wxLaunchImpl({
            solutionId: solution_id,
            solutionRelatedId: solution_related_id,
            from: 'native',
            isOldVersion: solution_support_type === '1'
        });
    };
    if (solution_support_type === '1') {
        window.openWeChatFromJS();
        setTimeout(() => {
            location.href = 'bdmedialive://bdmedialive/common/closeWebview';
        }, 3000);
        return <div className='wx_any_mount_card_ing'>
            跳转添加微信中
        </div>;
    }


    return null;
}
