import React from 'react';
import {hydrate, render} from 'react-dom';
import {registerPageInit} from '@/pages/liveshow/service/register';
import once from 'lodash/once';
import {adCommonShowLog} from '../liveshow/components/AdLiveShow/monitor/ad';
import WX from './wx';

import '../liveshow/assets/index.less';
import './index.less';

// 注册所有初始化逻辑
registerPageInit();
const enterMonitor = once(() => {
    adCommonShowLog({
        // page写死 0: 'live'，中间页无需区分直播状态
        page: '0', type: 'enter', value: 'entry', serverId: '19463', isNewVersion: true
    });
});
function App() {
    enterMonitor();
    return <div className='nmp-app-container'>
        <WX />
    </div>;
}

const containerId = 'nmp-root';
const rootElement = window.document.getElementById(containerId);
if (rootElement) {
    if (rootElement.hasChildNodes()) {
        hydrate(<App />, rootElement);
    }
    else {
        render(<App />, rootElement);
    }
}

export default locals => {
    return Promise.resolve(
        locals.preRender({
            renderer: () => '',
            id: containerId,
            main: App,
            props: {}
        })
    );
};
