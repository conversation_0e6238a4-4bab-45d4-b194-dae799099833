/**
 * @file 打点入口
 * <AUTHOR>
 */

import { getUrlParam } from '@/utils';
import UBC from '@/utils/ubc';
import {isAndroid, isIOS, UA} from '@baidu/boxx/env';
import { env } from '@/utils/env';

const urlSource = decodeURIComponent(getUrlParam('source'));
const source = urlSource.includes('|') ? urlSource.split('|')[1] : urlSource;
const appSource = getUrlParam('app_source');
const roomId = getUrlParam('room_id') || getUrlParam('roomid');
const pageStatus = {
    '-1': 'preview',
    0: 'live',
    2: 'live_end',
    3: 'record',
    4: 'h5_subscribe',
}

const isHarmony = /ArkWeb\//i.test(UA);
let os_system = 'other';
if (isAndroid) {
    os_system = 'android';
} else if (isIOS) {
    os_system = 'ios';
} else if (isHarmony) {
    os_system = 'harmony';
}
const userAgent = navigator.userAgent.toLowerCase();
let ua = '';
if (/\b(?:lite|info) baiduboxapp\b/i.test(userAgent)) {
    ua = 'baiduboxlite';
}
else if (userAgent.includes('baiduboxapp')) {
    ua = 'baiduboxapp';
}
else if (userAgent.includes('haokan')) {
    ua = 'haokan';
}
else if (userAgent.includes('tomas')) {
    ua = 'tomas';
}
else {
    ua = 'baiduboxapp';
}

const ubc = new UBC({
    queryParams: {
        appname: ua,
    }
});
const ubcLog = (params = {}, ext = {}, serverId = '', isTiming = false) => {
    try {
        const res = {
            serverId: serverId,
            config: {
                source: source,
                from: 'liveshow',
                ext: {
                    roomid: roomId,
                    // 取url里的app_source，代表上游分享来源的端，没有则为空
                    app_source: appSource,
                    os_system,
                    browser: env.isWechat ? 'wechat' : 'other',
                    ...ext,
                },
                ...params,
            },
        };

        ubc.send(res, isTiming);
    } catch (err) {
        // console.log(err);
    }
};
export default ubcLog;

// h5直播落地页 通用展示打点
export const commonShowLog = (page, value, nid, screen, serverId, isNewVersion, pos = '') => {
    ubcLog({
        // live_end(直播结束页) live record(回放页)
        page: pageStatus[page],
        type: 'show',
        value,
    }, {
        nid,
        // 1竖全屏 2横半屏
        screen: String(+screen + 1),
        is_new_version: isNewVersion ? '1' : '0',
        pos,
    }, serverId);
}

// h5直播落地页 通用点击打点
export const commonClickLog = (page, value, nid, screen, serverId, isNewVersion, pos = '') => {
    ubcLog({
        // live_end(直播结束页) live record(回放页)
        page: pageStatus[page],
        type: 'click',
        value,
    }, {
        nid,
        // 1竖全屏 2横半屏
        screen: String(+screen + 1),
        is_new_version: isNewVersion ? '1' : '0',
        pos,
    }, serverId);
}
// 新版预约页展示打点
export const NewH5ShowLog = (page, value, serverId,click_btn) => {
    ubcLog({
        // live_end(直播结束页) live record(回放页)
        page: pageStatus[page],
        type: 'show',
        value,
    }, {
        click_btn,
    }, serverId);
}
// 新版预约页点击打点
export const NewH5ClickLog = (page, value, serverId, click_btn = '') => {
    ubcLog({
        // live_end(直播结束页) live record(回放页)
        page: pageStatus[page],
        type: 'click',
        value,
    }, {
        click_btn,
    }, serverId);
}
// 新版预约页时长开始打点
export const NewH5StartLog = (page, screen,value, serverId, click_btn = '') => {
    ubcLog({
        // live_end(直播结束页) live record(回放页)
        page: pageStatus[page],
        type: '',
        value,
    }, {
        click_btn,
        screen: String(+screen + 1),
    }, serverId, false);
}

// h5落地页 调起回流回调打点
export const reflowCallbackLog = (page, value, nid, screen, serverId, isNewVersion, pos = '', reflowErr = '') => {
    ubcLog({
        // live_end(直播结束页) live record(回放页)
        page: pageStatus[page],
        type: 'click',
        value,
    }, {
        nid,
        // 1竖全屏 2横半屏
        screen: String(+screen + 1),
        is_new_version: isNewVersion ? '1' : '0',
        pos,
        reflowErr,
    }, serverId);
}

// h5直播落地页 预约页展示打点
export const previewShowLog = (page, value, nid, screen, serverId, isNewVersion, pos = '') => {
    ubcLog({
        page: pageStatus[page],
        type: 'show',
        value,
    }, {
        nid,
        // 1竖全屏 2横半屏
        screen: String(+screen + 1),
        is_new_version: isNewVersion ? '1' : '0',
        pos,
    }, serverId);
}

// h5直播落地页 通用时长开始打点
export const commonStartTimingLog = (page, value, nid, screen, serverId, isNewVersion) => {
    ubcLog({
        // live_end(直播结束页) live record(回放页)
        page: pageStatus[page],
        type: '',
        value,
    }, {
        nid,
        // 接口返回screen为0(竖屏)和1(横屏)；打点传递得值为 1竖全屏 2横半屏
        screen: String(+screen + 1),
        is_new_version: isNewVersion ? '1' : '0',
    }, serverId, true);
}

// h5直播落地页 通用时长结束打点
export const commonEndTimingLog = (serverId) => {
    ubc.endTimingLog(serverId, false);
}

// h5直播落地页 CNY活动展示打点
export const cnyShowLog = (live_type, serverId) => {
    ubcLog({
        from: 'liveshow',
        page: 'live',
        type: 'show',
        value: 'h5_page',
    },{
        live_type: live_type,
    },
    serverId);
}

// h5直播落地页 CNY活动展示打点
export const cnyClickLog = (btn_pos, serverId) => {
    ubcLog({
        from: 'liveshow',
        page: 'live',
        type: 'click',
        value: 'h5_page',
    },{
        btn_pos,
    },
    serverId);
}

// 黄山音乐节预约页 展现打点
export const huangshanShowLog = (value, ext) => {
    ubcLog({
        from: 'liveshow',
        page: 'h5_subscribe',
        type: 'show',
        value,
    },{
        host_suzhu: ua,
        ...ext
    },
    15263);
}

// 黄山音乐节预约页 展现打点
export const huangshanClickLog = (value, ext) => {
    ubcLog({
        from: 'liveshow',
        page: 'h5_subscribe',
        type: 'click',
        value,
    },{
        host_suzhu: ua,
        ...ext
    },
    15263);
}

// 小经典展示打点
export const xiaoJingDianShowLog = (page, value, serverId, click_btn, live_status) => {
    ubcLog({
        // live_end(直播结束页) live record(回放页)
        page,
        type: 'show',
        value,
    }, {
        click_btn,
        live_status
    }, serverId);
}
// 小经典点击打点
export const xiaoJingDianClickLog = (page, value, serverId, click_btn = '', live_status) => {
    ubcLog({
        // live_end(直播结束页) live record(回放页)
        page,
        type: 'click',
        value,
    }, {
        click_btn,
        live_status
    }, serverId);
}

// 小经典时长开始打点
// eslint-disable-next-line max-params, max-len
export const xiaoJingDianStartLog = (page, screen, value, serverId, live_status, type, duration = 0) => {
    ubcLog({
        // live_end(直播结束页) live record(回放页)
        page,
        type,
        value,
    }, {
        duration: String(duration),
        live_status,
        screen: String(+screen + 1),
    }, serverId, false);
}

// 小经典滑动打点
export const xiaoJingDianSlideLog = (page, value, serverId, screen, live_status) => {
    ubcLog({
        // live_end(直播结束页) live record(回放页)
        page,
        type: 'show',
        value,
    }, {
        live_status,
        screen: String(+screen + 1),
    }, serverId);
}

// 武侠歌会展示打点
export const wuXiaShowLog = (value, serverId, ext) => {
    ubcLog({
        // live_end(直播结束页) live record(回放页)
        page: 'h5_subscribe',
        type: 'show',
        value,
    }, {
        ext
    }, serverId);
}
// 武侠歌会点击打点
export const wuXiaClickLog = (value, serverId, ext) => {
    ubcLog({
        // live_end(直播结束页) live record(回放页)
        page: 'h5_subscribe',
        type: 'click',
        value,
    }, {
        ext
    }, serverId);
}

// 通用歌会展示打点
export const activityShowLog = (value, serverId, ext) => {
    ubcLog({
        page: 'h5_subscribe',
        type: 'show',
        value,
    }, {
        ...ext
    }, serverId);
}
// 通用歌会点击打点
export const activityClickLog = (value, serverId, ext) => {
    ubcLog({
        page: 'h5_subscribe',
        type: 'click',
        value,
    }, {
        ...ext
    }, serverId);
}
