import React, {Component} from 'react';
import {BoundaryConfigProvider} from 'react-suspense-boundary';
import {weirwood} from './utils/errorMonitor';

class WeirwoodErrorBoundary extends Component {

    constructor(props) {
        super(props);
        this.state = {hasError: false};
    }

    static getDerivedStateFromError() {
        return {hasError: true};
    }

    componentDidCatch(error) {
        weirwood.error.captureException(error);
    }

    render() {
        if (this.state.hasError) {
            return <div>页面发生错误，请稍后重试~</div>;
        }
        return this.props.children;
    }
}

const onErrorCaught = (error) => {
    weirwood.error.captureException(error);
};

export const WithWeirwoodCaughtBoundaryConfigProvider = ({children}) => {
    return (
        <WeirwoodErrorBoundary>
            <BoundaryConfigProvider
                onErrorCaught={onErrorCaught}
                pendingFallback={null}
                renderError={() => null}
            >
                {children}
            </BoundaryConfigProvider>
        </WeirwoodErrorBoundary>
    );
};
