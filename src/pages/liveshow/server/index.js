/* eslint-disable import/unambiguous */
/* eslint-disable import/no-commonjs */

/**
 * @file index.js
 * @description SSR FC函数，用于SSR渲染页面
 * <AUTHOR>
 */
// 必需参数，参数对应的值不能为空
const requiredQuery = ['room_id'];

class Page {
    /**
     * 获取服务接口
     * 1. 按照接口规范定义
     * 2. 字定义根据ctxRequest参数处理
     */
    async getServiceProps(ctx) { // ctxRequest, fetch
        const ctxRequest = ctx.request;

        const HOST = 'https://mbd.baidu.com';

        // 参数校验，如果有缺失，返回空，走CSR渲染
        const queryMissing = this.queryRequiredCheck(ctxRequest.query);
        if (queryMissing) {
            return {};
        }

        const data = {
            data: {
                room_id: ctxRequest.query.room_id,
                device_id: '6b086cea103671e597ab3cceb7a37a00',
                source_type: 0,
                osname: 'baiduboxapp' // 外层osname rd反馈取不到具体值 统一被赋值为baiduboxapp了
            }
        };

        const options = {
            cmd: 371,
            action: 'star',
            service: 'bdbox',
            osname: 'baiduboxapp',
            data: JSON.stringify(data)
        };

        const serviceProps = {
            mainRes: {
                cache: true, // 无需给接口加redis缓存（true：default is 300s）
                api: `${HOST}/searchbox?${this.buildParam(options)}`
            }
        };

        console.log('serviceProps:', JSON.stringify(serviceProps));

        return serviceProps;
    }

    buildParam = data => {
        if (typeof data === 'object') {
            return Object.keys(data)
                .map(key => {
                    let value = data[key] === undefined ? '' : encodeURIComponent(data[key]);
                    return `${key}=${value}`;
                })
                .join('&');
        }
        return '';
    };

    /**
     * 检测必选参数是否缺失
     * @param {*} query
     * @returns
     */
    queryRequiredCheck = query => {
        const queryKeys = Object.keys(query);
        return requiredQuery.some((key) => !queryKeys.includes(key) || query[key] === '');
    }

    /**
     * 是否需要将页面APP在Node环境下SSR转为HTML字符串
     * @param {*} staticContext 通过getServiceProps获取的Server接口数据
     * @returns
     */
    static shouldAppToHtmlString() {
        return false;
    }

    /**
     * 页面HTML合成后，对页面进行自定义处理
     * @param {*} $ 页面模板
     * @param {*} staticContext 通过getServiceProps获取的Server接口数据
     * @returns
     */
    // static afterTemplateExecution($, staticContext) { // $, staticContext
    //     // eslint-disable-next-line no-console
    //     console.log(staticContext);
    //     return $;
    // }
}

module.exports = Page;
