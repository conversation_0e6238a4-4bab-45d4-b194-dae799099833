/**
 * @file 变量定义
 * <AUTHOR>
 * @date 2021-11-10 21:22:51
 */

import {getAppSchemeHead, getUrlParam, getAppSource} from '@/pages/liveshow/utils/getUrlParam';
import {matrixAppSchemeHeaderList} from '@/pages/liveshow/utils/reflow';
import {env} from '@/utils/env';

// 新版预约页打点文案
 export const logTxtMap = {
    '-1':'subscribe',
    '0':'to_live',
    '3':'to_record'
}
// 小经典打点文案

export const jdlogTxtMap = {
    '-1':'preview',
    '0':'live',
    '3':'record'
}
// 呱呱付费跳转
export const ForbiddenUrl = 'https://live.baidu.com/m/media/activity/guagua/forbidden.html';

// 语音房业务跳转
export const ClubhouseUrl = 'https://live.baidu.com/m/media/multipage/clubhouse/index.html';

// 付费拼团提示升级
export const GroupBuyUrl = 'https://activity.baidu.com/mbox/4a80ab9c62/matrixInvokePage';

// 直播tab scheme
export const appLiveScheme = 'baiduboxapp://v11/appTab/select?item=home&upgrade=0&params=%7B%22channel%22%3A%2240%22%2C%22tab_info%22%3A%7B%22id%22%3A%2240%22%2C%22name%22%3A%22%E7%9B%B4%E6%92%AD%22%2C%22canDelete%22%3A%220%22%7D%7D';

// video最多加载的时间(ms)，超出则提示错误引导用户刷新
export const VIDEO_MAX_WAIT = 15000;

export const TemplateEnum = {
    Preview: 'preview', // 预约模板
    Live: 'live', // 直播模板 (直播中 回放 直播结束没有回放)
};

// 直播状态
export const LiveStatus = {
    NOT_START: -1, // 直播未开始
    LIVEING: 0, // 直播中
    END_NO_REVIEW: 2, // 直播已结束 没有回看
    END_HAS_REVIEW: 3, //  直播已结束 有回看
};

// 直播状态 返回liveing/review/preview（非直播meta状态）
export const LiveStatusObj = {
    '-1': 'preview', // 直播未开始
    '0': 'living', // 直播中
    '2': 'review', // 回放
    '3': 'review', // 回放
};

// 根据直播状态获取 scheme所需的status
// 0直播状态 2关闭状态等待回放生成 3关闭且有回放 4预告 20直播间终结态
export const LiveStatusByScheme = {
    '-1': '4', // 直播未开始
    '0': '0', // 直播中
    '2': '2', // 回放
    '3': '3', // 回放
    '20': '20', // 直播已删除
};

// 关注状态
export const FollowEnum = {
    UNFOLLOW: 0,
    FOLLOWED: 1,
    PENDING: 2,
    NOT_INIT: -1,
};

// 页面错误类型
export const PageErrorStatus = {
    NO_CHAT: 33,
    NEW_NO_CHAT: 34,
    NOT_START: -1,
    ERROR_END: 1012,
    CROWDED: 1046,
    TIEBA_END: 9999,
    TEXT_LIVING: 20, // 图文直播 or 贴吧直播结束
};

// mcp 场景信息
export const APP_NAME_SCENE = {
    'baiduboxapp': 'sharepage',
    'lite': 'sharepagelite', // 新增scene
    'haokan': 'sharepagehaokan', // 新增scene
    '': 'sharepage'
};

// 目前调起位标识整合
export const invokeKeys = ['morelive', 'part', 'whole'];

// 武侠歌会按钮文案
export const numImgMap = {
    0: 'https://mpics.bdstatic.com/qm/202407/pic_tNVQ8O_1720431568.png',
    1: 'https://mpics.bdstatic.com/qm/202407/pic_e40EZj_1720430424.png',
    2: 'https://mpics.bdstatic.com/qm/202407/pic_eOTNmc_1720436262.png',
    3: 'https://mpics.bdstatic.com/qm/202407/pic_6SLWTa_1720436362.png',
}

const appSchemeHead = getAppSchemeHead();
const isMatrixApp = matrixAppSchemeHeaderList.includes(appSchemeHead) || (env.isIOS && env.isLiteBox);
// 回流渠道
const source = getAppSource() || getUrlParam('source') || '';
// 支持mcp调起
export const supportMcpInvoke = !isMatrixApp || ['lite', 'haokan'].indexOf(source) > -1;
// 请求MCP信息场景 非主板&回流主板 || 非极速&回流极速 || 非好看&回流好看
export const needGetMcpInfo =
    (!env.isMainBox && !isMatrixApp)
    || (!env.isLiteBox && source === 'lite')
    || (!env.isHaokan && source === 'haokan');
