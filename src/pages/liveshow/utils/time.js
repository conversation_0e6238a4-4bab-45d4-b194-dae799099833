/**
 * @file 时间相关处理模块
 * <AUTHOR>
 * @date 2021-11-11 15:26:36
 */
const getFormatTime = (times) => {
    if (times === null) {
        return null;
    }
    const time = new Date(+times * 1000);
    const month = time.getMonth() + 1;
    const date = time.getDate();
    let hour = time.getHours();
    let minutes = time.getMinutes();
    if (hour < 10) {
        hour = `0${hour}`;
    }
    if (minutes < 10) {
        minutes = `0${minutes}`;
    }
    return {
        month,
        date,
        hour,
        minutes,
    };
};

export const getStartTimeStr = (timestamp = 0, endtimestamp = 0) => {
    // 新增 endtime 表示开播得时间宽度 大于一天完整显示，例如：1月28日23:00-1月29日 02:00 小于一天1月28日23:00-23:10
    if (timestamp === null) {
        return '---';
    }
    const startTime = getFormatTime(timestamp);
    let currentTime = `${startTime.month}月${startTime.date}日 ${startTime.hour}:${startTime.minutes}`;
    if (endtimestamp) {
        const endTime = getFormatTime(endtimestamp);
        if (endTime.month - startTime.month > 0 || endTime.date - startTime.date > 0) {
            currentTime += `-${getStartTimeStr(endtimestamp)}`;
        } else {
            currentTime += `-${endTime.hour}:${endTime.minutes}`;
        }
    }
    return currentTime;
};
