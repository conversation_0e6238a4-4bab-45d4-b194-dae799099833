/**
 * @file 此文件处理所有需要过滤reflow的逻辑
 * <AUTHOR>
 * @date 2021-06-17 15:27:54
 */

//  目前 oppo机型 和  source为huawei 的来源 过滤所有跳转逻辑
import { env, isOppoBrowser } from '@/utils/env';
import { getUrlParam } from './getUrlParam';

export const huaweiFeed = getUrlParam('source') === 'huawei';
export const oppo = isOppoBrowser();

const ignore = () => {
    if (env.isBox || env.isLiteBox) {
        return false;
    }
    return huaweiFeed || oppo;
};
export default ignore();
