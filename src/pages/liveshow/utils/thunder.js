/**
 * @file  打点相关
 * <AUTHOR>
 * @date 2020-12-11 16:04:57
 */

import Thunder from '@/utils/thunder';
import {appProtoName} from '@baidu/boxx/env';
import {env} from '@/utils/env';
import {TemplateEnum, LiveStatus} from '@/pages/liveshow/config/const';
import {PageStore} from '@/pages/liveshow/service/register';

const log = Thunder.getInstance({});

let firstInit = true;
export const initLogInfo = (info) => {
    const type = info.template;
    const roomId = PageStore.roomId;
    const source = PageStore.queryParams.source;
    log.clearBaseParams();
    if (type === TemplateEnum.Live) {
        log.extendBase({
            logExtra: {
                nid: roomId,
                roomid: roomId,
                browser: env.browserType,
                os: env.isIOS ? 'ios' : env.isAndroid ? 'android' : '',
                source: source,
                type: info.status === LiveStatus.LIVEING ? 'live' : 'review',
            },
            cst: 2,
            ct: 3,
            logFrom: 'feed_landing_share',
            logInfo: 'live_broadcast',
        });
        if (firstInit) {
            const share_uid = PageStore.queryParams.share_uid || '';
            log.sendLog({
                tid: 11063,
                cst: 1,
                logExtra: share_uid
                    ? {
                          share_uid: share_uid,
                          zb_tag: PageStore.queryParams.zb_tag || '',
                      }
                    : '',
            });

            const isPaid = +info.has_pay_service === 1;
            if (isPaid) {
                log.sendLog({tid: 11168, cst: 1});
            }

            // 离开页面打点
            const startTime = Date.now();
            let isEnd = false;
            window.addEventListener('beforeunload', () => {
                if (isEnd) {
                    return;
                }
                isEnd = true;
                const endTime = Date.now();
                const duration = endTime - startTime;
                const option = {
                    cst: 8,
                    tid: 11067,
                    duration,
                    startTime: startTime,
                    endTime: endTime,
                };
                log.sendLog(option);
            });
        }
    } else if (type === TemplateEnum.Preview) {
        const cuid = PageStore.cuid || '';
        log.extendBase({
            logExtra: {
                title: info.title,
                authorID: info.follow_id,
                roomid: roomId,
                source: source,
                appProtoName,
            },
            ssid: '',
            cua: '',
            cut: '',
            sid: '',
            logid: '',
            cfrom: '',
            cuid: cuid,
            ct: 3,
            logFrom: 'feed_landing',
        });

        if (firstInit) {
            // 预约页面展现打点
            log.sendLog({
                cst: 1,
                tid: 10862,
                logInfo: 'live_broadcast_show',
            });

            // 离开页面打点
            const startTime = Date.now();
            let isEnd = false;
            window.addEventListener('beforeunload', () => {
                if (isEnd) {
                    return;
                }
                isEnd = true;
                const endTime = Date.now();
                const duration = endTime - startTime;
                const option = {
                    cst: 8,
                    tid: 10863,
                    logInfo: 'live_broadcast_time',
                    startTime: startTime,
                    duration,
                    endTime: endTime,
                };
                log.sendLog(option);
            });
        }
    }

    firstInit = false;
};

export const logItemClick = (data) => {
    const options = {
        cst: 2,
        tid: 10864,
        logInfo: 'live_broadcast_click',
        logExtra: {
            status: data.status,
            type: data.type,
        },
    };

    log.sendLog(options);
};

export const logHeadClick = (data) => {
    const options = {
        cst: 2,
        tid: 11070,
        logInfo: 'live_broadcast_click',
        logExtra: {
            click_type: data.click_type,
            page: data.page,
            value: data.value,
            id: data.id,
        },
    };

    log.sendLog(options);
};

export const logPurchaseClick = () => {
    const options = {
        cst: 2,
        tid: 11159,
        logInfo: 'live_preview',
    };

    log.sendLog(options);
};

export const logPurchaseShow = () => {
    const options = {
        cst: 1,
        tid: 11157,
        logInfo: 'live_preview',
    };

    log.sendLog(options);
};

export const logDialogShow = () => {
    const options = {
        cst: 2,
        tid: 11160,
        logInfo: 'live_preview',
    };

    log.sendLog(options);
};

export const logDialogClick = () => {
    const options = {
        cst: 2,
        tid: 11161,
        logInfo: 'live_preview',
    };

    log.sendLog(options);
};

export const logLiveSeriesShow = (data) => {
    const {id, reportSuccess, compilation_id = '', compilation_list} = data;
    const extra = [];
    const rid = [];
    id.forEach((ele, index) => {
        if (compilation_list[index] !== undefined) {
            const cur = compilation_list[index];
            extra.push(cur);
            rid.push(cur.data.room_id);
        }
    });

    const options = {
        cst: 1,
        tid: 11238,
        logInfo: 'live_series_show',
        logExtra: {
            pos: id,
            extra,
            rid,
            topic_id: compilation_id,
        },
    };

    log.sendLog(options, '', reportSuccess);
};

export const logLiveSeriesClick = (data) => {
    const {compilation_id = '', compilation_list, pos} = data;
    if (compilation_list && compilation_list.length) {
        const extra = compilation_list[pos] || '';
        if (extra && extra.data && extra.data.room_id) {
            const options = {
                cst: 2,
                tid: 11239,
                logInfo: 'live_series_click',
                logExtra: {
                    pos: pos,
                    extra,
                    rid: extra.data.room_id,
                    topic_id: compilation_id,
                },
            };

            log.sendLog(options);
        }
    }
};

export const logModelChange = (res) => {
    let {show, type, from = '', value = ''} = res;

    // 打点
    const info = {
        cst: show ? 1 : 2,
        logExtra: {
            from,
            value,
        },
    };

    if (type === 'pay') {
        if (show) {
            info.tid = 11166;
        } else {
            info.tid = 11170;
        }
    } else {
        if (show) {
            info.tid = 11065;
        } else {
            info.tid = 11066;
        }
    }

    log.sendLog(info);
};

export default log;
