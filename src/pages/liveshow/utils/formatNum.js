// 截取小数点后n位
export const cutDecimal = (num, n, twoDecimal = false) => {
    // 最多保留三位小数，小于0.001默认返回0.001
    if (num === 0) {
        return 0;
    }

    if (n === 3 && num <= 100) {
        return 0.001;
    }

    if (n === 5 && num <= 1) {
        return 0.00001;
    }

    const realNum = (num / 100000).toString();
    const pointIndex = realNum.includes('.') ? realNum.indexOf('.') : 0;
    if (twoDecimal && (!realNum.includes('.') || realNum.length - pointIndex <= 3)) {
        return Number(realNum).toFixed(2);
    }
    if (realNum.includes('.')) {
        const isMoreTwo = realNum.length - pointIndex > 3;
        if (isMoreTwo) {
            return Number(realNum.slice(0, n + pointIndex + 1));
        }
        return Number(realNum);

    }
    return Number(realNum);
};

export const handleDecimalNum = (num, minLen, maxLen) => {
    const value = +num === 0 ? 0 : Math.max(+num, 1 / Math.pow(10, maxLen));
    const totalLen =  +value?.toString().length;
    const pointPos = +value?.toString().indexOf('.') + 1;
    const decimalLen = pointPos > 0 ? totalLen - pointPos : 0;
    const showDecimalLen = Math.max(minLen, Math.min(maxLen, decimalLen));

    return {
        originValue: +num,
        showDecimalLen,
        numStr: decimalLen >= maxLen
            ? Number.parseFloat(+value, 10)?.toString().slice(0, pointPos + maxLen)
            : Number.parseFloat(+value, 10)?.toFixed(showDecimalLen)
    };
};
