/**
 * @file 用于端内情况下,注册各种数据通道回调
 * <AUTHOR>
 * @date 2021-11-12 12:35:55
 */

import {TemplateEnum} from '@/pages/liveshow/config/const';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';

const callbackMap = {};

export const registerCallback = (callback, page) => {
    callbackMap[page] = callback;
};

let registerCallbackId = 0;
const registerDataChannel = (param, page) => {
    try {
        const callbackName = `register_channel_callback_${registerCallbackId++}`;
        boxjsUtils.event.on({
            ...param,
            jscallback: callbackName
        });
        window[callbackName] = function (action, data) {
            if (typeof data === 'string') {
                data = JSON.parse(data);
                if (!data) {
                    return;
                }
                let callback = callbackMap[page];
                callback && callback(data);
            }
        };
        const unRegisterCallback = () => {
            boxjsUtils.event.off(param);
        };
        window.addEventListener('unload', unRegisterCallback);
        window.addEventListener('beforeunload', unRegisterCallback);
    }
    catch (e) {
        console.log('channel error', e);
    }
};

const initLiveDataChannel = () => {
    //
};

const initPreviewDataChannel = () => {
    const param = {
        page: 'h5_live_preview',
        action: 'com.baidu.channel.feed.assistmessage'
    };

    registerDataChannel(param, 'h5_live_preview');
};

export default (info) => {
    if (!boxjsUtils.platformInfo.isBox) {
        return;
    }
    const type = info.template;
    if (type === TemplateEnum.Live) {
        initLiveDataChannel();
    }
    else if (type === TemplateEnum.Preview) {
        initPreviewDataChannel();
    }
};
