/**
 * @file 关注模块
 * <AUTHOR>
 * @date 2021-11-11 15:30:58
 */

import concern from '@baidu/xbox-concern/dist/concern.min';
import {FollowEnum} from '@/pages/liveshow/config/const';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import {logHeadClick} from '@/pages/liveshow/utils/thunder';

const FollowAction = 'com.baidu.channel.foundation.followchanged';
const EventPages = ['newsdetails', 'paymentProfile'];

const getFollowStatus = (data) => {
    const {type, sfrom, third_id} = data;
    const initConcern = concern({
        id: third_id,
        ops: {
            type,
            isAladin: 0,
            onLine: true
        }
    });
    return new Promise((resolve, reject) => {
        initConcern.relateAPI(
            {
                type,
                third_id: [third_id],
                sfrom
            },
            (res) => {
                if (res && res.data && res.data.items && res.data.items.length > 0) {
                    resolve(FollowEnum.FOLLOWED);
                }
                else {
                    resolve(FollowEnum.UNFOLLOW);
                }
            },
            () => {
                resolve(FollowEnum.UNFOLLOW);
            }
        );
    });
};

const clickFollow = (data) => {
    const {type, status, third_id, template} = data;
    const nextStatus = +status === FollowEnum.FOLLOWED ? FollowEnum.UNFOLLOW : FollowEnum.FOLLOWED;
    const toastText = +status === FollowEnum.FOLLOWED ? '取消关注成功' : '关注成功';

    logHeadClick({
        value: nextStatus,
        id: third_id,
        click_type: 'follow',
        page: template
    });

    const initConcern = concern({
        id: third_id,
        ops: {
            type,
            isAladin: 0,
            onLine: true
        }
    });

    return new Promise((resolve, reject) => {
        initConcern.subscribe(
            {
                type,
                op_type: +status === 1 ? 'cancel' : 'add',
                third_id,
                sfrom: 'sbox',
                source: 'live_h5'
            },
            () => {
                initConcern.toast({
                    type: 'tips',
                    text: toastText
                });
                resolve(+nextStatus);
            },
            () => {
                initConcern.toast({
                    type: 'tips',
                    text: '操作失败，请稍后再试'
                });
                resolve(+status);
            },
            false
        );
    });
};

/**
 * 初始化关注通信管道
 * 用于不同页面之间关注触发的通知
 * 主要用于：页面跳转后能够保持状态同步
 *
 * @param {String} follow_id 传入参数
 * @returns {Function} 返回值
 */
const initFollowChannel = (follow_id, callback) => {
    if (!boxjsUtils.platformInfo.isBox) {
        return;
    }
    const callbackName = '__follow__change__callback__';
    window[callbackName] = (action, data) => {
        if (typeof data === 'string') {
            try {
                data = JSON.parse(data).data;
                const getThirdId = data[0].third_id;
                const followStatus = +data[0].is_follow === 1 ? FollowEnum.FOLLOWED : FollowEnum.UNFOLLOW;
                if (`${getThirdId}` === `${follow_id}`) {
                    callback && callback(followStatus);
                }
            }
            catch (error) {
                console.error('error: follow initFollowChannel', error);
            }
        }
    };

    EventPages.forEach((page) => {
        boxjsUtils.event.on({
            action: FollowAction,
            page: page,
            jscallback: callbackName
        });
    });

    const unregistFollow = () => {
        EventPages.forEach((page) => {
            boxjsUtils.event.off({
                action: FollowAction,
                page: page
            });
        });
    };
    window.addEventListener('unload', unregistFollow);
    window.addEventListener('beforeunload', unregistFollow);
};

export {
    //
    initFollowChannel,
    getFollowStatus,
    clickFollow
};
