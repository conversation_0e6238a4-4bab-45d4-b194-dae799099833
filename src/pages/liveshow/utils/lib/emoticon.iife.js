/**
 * @baidu/xbox-emoticon v1.0.5
 * global xbox.emoticon
 * Created at 2021-01-07 12:03:07
 */

/* eslint-disable */

export default function (exports) {
  'use strict';

  function _typeof(obj) {
    "@babel/helpers - typeof";

    if (typeof Symbol === "function" && typeof Symbol.iterator === "symbol") {
      _typeof = function (obj) {
        return typeof obj;
      };
    } else {
      _typeof = function (obj) {
        return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj;
      };
    }

    return _typeof(obj);
  }

  function _slicedToArray(arr, i) {
    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();
  }

  function _arrayWithHoles(arr) {
    if (Array.isArray(arr)) return arr;
  }

  function _iterableToArrayLimit(arr, i) {
    if (typeof Symbol === "undefined" || !(Symbol.iterator in Object(arr))) return;
    var _arr = [];
    var _n = true;
    var _d = false;
    var _e = undefined;

    try {
      for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {
        _arr.push(_s.value);

        if (i && _arr.length === i) break;
      }
    } catch (err) {
      _d = true;
      _e = err;
    } finally {
      try {
        if (!_n && _i["return"] != null) _i["return"]();
      } finally {
        if (_d) throw _e;
      }
    }

    return _arr;
  }

  function _unsupportedIterableToArray(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _arrayLikeToArray(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(o);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);
  }

  function _arrayLikeToArray(arr, len) {
    if (len == null || len > arr.length) len = arr.length;

    for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];

    return arr2;
  }

  function _nonIterableRest() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }

  /**
   * @file Created on Mon Nov 26 2018
   * <AUTHOR> <<EMAIL>>
   */

  /**
   * 返回当前浏览器 UserAgent
   */
  var UA = (typeof window === "undefined" ? "undefined" : _typeof(window)) === 'object' && window.self === window ? navigator.userAgent : '';

  /**
   * 这里是精简版本的 is
   * @module os
   * @file Created on Mon Nov 26 2018
   * <AUTHOR> <<EMAIL>>
   */
  /**
   * 是否是ios
   * @function isIOS
   * @param {string} ua - ua 默认是 userAgent
   */

  function isIOS() {
    var ua = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : UA;
    return /(iPhone|iPod|iPad)/.test(ua);
  }

  /**
   * @file Created on Mon Nov 26 2018
   * <AUTHOR> <<EMAIL>>
   */

  /**
   * @function type
   * @param {any} obj - 被判断的对象
   * @return {string} 返回类型：array/date/string/object/undefined/function/boolean/null/regexp/math 等
   */
  var getType = function getType(obj) {
    return Object.prototype.toString.call(obj).slice(8, -1).toLowerCase();
  };
  /**
   * 根据传入的类型使用 Object.toString 进行判断
   * @function isType
   * @param {any} obj - 需要判断的类型
   * @param { string} type - 需要判断的类型字符串
   * @return {boolean} 返回判断结果
   * @example
   * isType('', 'String') // true
   * isType(new Date, 'Date') // true
   * isType('', 'Array') // false
   */


  function isType(obj, type) {
    return getType(obj) === type.toLowerCase();
  }

  /**
   * @file 定义类型错误
   * <AUTHOR> <<EMAIL>>
   */

  /**
   * 获取错误类型的对象
   * @function typeError
   * @param {string} name - 展现的参数名
   * @param {string} wanted - 需要的类型字符串
   * @param {any} obj - 错误的类型对象
   * @return {TypeError} 返回错误类型
   */
  var typeError = (function (name, wanted, obj) {
    return new TypeError('Expected `' + name + '` to be of type `' + wanted + '`, got `' + _typeof(obj) + '`');
  });

  /**
   * @file 日期格式化
   * <AUTHOR> <<EMAIL>>
   */
  /**
   * 日期格式化
   * <AUTHOR>
   * @function dateFormat
   * @param {Date} d - date 对象
   * @param {string} [pattern = 'yyyy-MM-dd'] - 字符串
   * @return {string} 处理后的字符串
   * @example
   *	var d = new Date();
   *  dateFormat(d," yyyy年M月d日\n yyyy-MM-dd\n MM-dd-yy\n yyyy-MM-dd hh:mm:ss")
   *  //2018年11月10日\n 2018-01-01\n 01-01-18\n 2018-11-12 12:01:02
   */

  var dateFormat = (function (d) {
    var pattern = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'yyyy-MM-dd';

    if (!isType(d, 'date')) {
      throw typeError('d', 'Date', d);
    }

    var y = d.getFullYear().toString();
    var o = {
      M: d.getMonth() + 1,
      // month
      d: d.getDate(),
      // day
      h: d.getHours(),
      // hour
      m: d.getMinutes(),
      // minute
      s: d.getSeconds() // second

    };
    pattern = pattern.replace(/(y+)/gi, function (a, b) {
      return y.substr(4 - Math.min(4, b.length));
    });
    Object.keys(o).forEach(function (i) {
      pattern = pattern.replace(new RegExp('(' + i + '+)', 'g'), function (a, b) {
        return o[i] < 10 && b.length > 1 ? '0' + o[i] : o[i];
      });
    });
    return pattern;
  });

  /**
   * @file 表情map列表 openjs/emoticon/config.js
   * <AUTHOR>
   * @changeBy jiaowenhua <<EMAIL>>
   */
  const config = {
    'version': '1',
    'imgpath': 'https://baidubox-emoji.cdn.bcebos.com/imgs/',
    'packages': [{
        'id': '1',
        'name': 'emoji',
        'package_icon': 'package_01.png',
        'version': '2.7.0',
        'blacklist': [],
        'emoticons': [
            {
                'id': '001001',
                'text': '[微笑]',
                'icon': '%5B%E5%BE%AE%E7%AC%91%5D.png'
            },
            {
                'id': '001002',
                'text': '[开心]',
                'icon': '%5B%E5%BC%80%E5%BF%83%5D.png'
            },
            {
                'id': '001003',
                'text': '[期待]',
                'icon': '%5B%E6%9C%9F%E5%BE%85%5D.png'
            },
            {
                'id': '001004',
                'text': '[大笑]',
                'icon': '%5B%E5%A4%A7%E7%AC%91%5D.png'
            },
            {
                'id': '001005',
                'text': '[鼓掌]',
                'icon': '%5B%E9%BC%93%E6%8E%8C%5D.png'
            },
            {
                'id': '001006',
                'text': '[悠闲]',
                'icon': '%5B%E6%82%A0%E9%97%B2%5D.png'
            },
            {
                'id': '001007',
                'text': '[笑哭]',
                'icon': '%5B%E7%AC%91%E5%93%AD%5D.png'
            },
            {
                'id': '001008',
                'text': '[不要啊]',
                'icon': '%5B%E4%B8%8D%E8%A6%81%E5%95%8A%5D.png'
            },
            {
                'id': '001009',
                'text': '[啊]',
                'icon': '%5B%E5%95%8A%5D.png'
            },
            {
                'id': '001010',
                'text': '[哟]',
                'icon': '%5B%E5%93%9F%5D.png'
            },
            {
                'id': '001011',
                'text': '[汗]',
                'icon': '%5B%E6%B1%97%5D.png'
            },
            {
                'id': '001012',
                'text': '[抠鼻]',
                'icon': '%5B%E6%8A%A0%E9%BC%BB%5D.png'
            },
            {
                'id': '001013',
                'text': '[哼]',
                'icon': '%5B%E5%93%BC%5D.png'
            },
            {
                'id': '001014',
                'text': '[发怒]',
                'icon': '%5B%E5%8F%91%E6%80%92%5D.png'
            },
            {
                'id': '001015',
                'text': '[委屈]',
                'icon': '%5B%E5%A7%94%E5%B1%88%5D.png'
            },
            {
                'id': '001016',
                'text': '[不高兴]',
                'icon': '%5B%E4%B8%8D%E9%AB%98%E5%85%B4%5D.png'
            },
            {
                'id': '001017',
                'text': '[囧]',
                'icon': '%5B%E5%9B%A7%5D.png'
            },
            {
                'id': '001018',
                'text': '[惊哭]',
                'icon': '%5B%E6%83%8A%E5%93%AD%5D.png'
            },
            {
                'id': '001019',
                'text': '[大哭]',
                'icon': '%5B%E5%A4%A7%E5%93%AD%5D.png'
            },
            {
                'id': '001020',
                'text': '[流泪]',
                'icon': '%5B%E6%B5%81%E6%B3%AA%5D.png'
            },
            {
                'id': '001021',
                'text': '[害羞]',
                'icon': '%5B%E5%AE%B3%E7%BE%9E%5D.png'
            },
            {
                'id': '001022',
                'text': '[亲亲]',
                'icon': '%5B%E4%BA%B2%E4%BA%B2%5D.png'
            },
            {
                'id': '001023',
                'text': '[色]',
                'icon': '%5B%E8%89%B2%5D.png'
            },
            {
                'id': '001024',
                'text': '[舔屏]',
                'icon': '%5B%E8%88%94%E5%B1%8F%5D.png'
            },
            {
                'id': '001025',
                'text': '[得意]',
                'icon': '%5B%E5%BE%97%E6%84%8F%5D.png'
            },
            {
                'id': '001026',
                'text': '[疑问]',
                'icon': '%5B%E7%96%91%E9%97%AE%5D.png'
            },
            {
                'id': '001027',
                'text': '[晕]',
                'icon': '%5B%E6%99%95%5D.png'
            },
            {
                'id': '001028',
                'text': '[大哈]',
                'icon': '%5B%E5%A4%A7%E5%93%88%5D.png'
            },
            {
                'id': '001029',
                'text': '[二哈]',
                'icon': '%5B%E4%BA%8C%E5%93%88%5D.png'
            },
            {
                'id': '001030',
                'text': '[三哈]',
                'icon': '%5B%E4%B8%89%E5%93%88%5D.png'
            },
            {
                'id': '001031',
                'text': '[白眼]',
                'icon': '%5B%E7%99%BD%E7%9C%BC%5D.png'
            },
            {
                'id': '001032',
                'text': '[阴险]',
                'icon': '%5B%E9%98%B4%E9%99%A9%5D.png'
            },
            {
                'id': '001033',
                'text': '[你懂的]',
                'icon': '%5B%E4%BD%A0%E6%87%82%E7%9A%84%5D.png'
            },
            {
                'id': '001034',
                'text': '[偷笑]',
                'icon': '%5B%E5%81%B7%E7%AC%91%5D.png'
            },
            {
                'id': '001035',
                'text': '[睡觉]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E7%9D%A1%E8%A7%89%5D.png'
            },
            {
                'id': '001036',
                'text': '[哈欠]',
                'icon': '%5B%E5%93%88%E6%AC%A0%5D.png'
            },
            {
                'id': '001037',
                'text': '[再见]',
                'icon': '%5B%E5%86%8D%E8%A7%81%5D.png'
            },
            {
                'id': '001038',
                'text': '[鄙视]',
                'icon': '%5B%E9%84%99%E8%A7%86%5D.png'
            },
            {
                'id': '001039',
                'text': '[抓狂]',
                'icon': '%5B%E6%8A%93%E7%8B%82%5D.png'
            },
            {
                'id': '001040',
                'text': '[咒骂]',
                'icon': '%5B%E5%92%92%E9%AA%82%5D.png'
            },
            {
                'id': '001041',
                'text': '[衰]',
                'icon': '%5B%E8%A1%B0%5D.png'
            },
            {
                'id': '001042',
                'text': '[骷髅]',
                'icon': '%5B%E9%AA%B7%E9%AB%85%5D.png'
            },
            {
                'id': '001043',
                'text': '[嘘]',
                'icon': '%5B%E5%98%98%5D.png'
            },
            {
                'id': '001044',
                'text': '[闭嘴]',
                'icon': '%5B%E9%97%AD%E5%98%B4%5D.png'
            },
            {
                'id': '001045',
                'text': '[呆]',
                'icon': '%5B%E5%91%86%5D.png'
            },
            {
                'id': '001046',
                'text': '[什么鬼]',
                'icon': '%5B%E4%BB%80%E4%B9%88%E9%AC%BC%5D.png'
            },
            {
                'id': '001047',
                'text': '[吐]',
                'icon': '%5B%E5%90%90%5D.png'
            },
            {
                'id': '001048',
                'text': '[已阅]',
                'icon': '%5B%E5%B7%B2%E9%98%85%5D.png'
            },
            {
                'id': '001049',
                'text': '[同上]',
                'icon': '%5B%E5%90%8C%E4%B8%8A%5D.png'
            },
            {
                'id': '001050',
                'text': '[友军]',
                'icon': '%5B%E5%8F%8B%E5%86%9B%5D.png'
            },
            {
                'id': '001051',
                'text': '[爱钱]',
                'icon': '%5B%E7%88%B1%E9%92%B1%5D.png'
            },
            {
                'id': '001053',
                'text': '[国宝]',
                'icon': '%5B%E5%9B%BD%E5%AE%9D%5D.png'
            },
            {
                'id': '001054',
                'text': '[羊驼]',
                'icon': '%5B%E7%BE%8A%E9%A9%BC%5D.png'
            },
            {
                'id': '001055',
                'text': '[鲜花]',
                'icon': '%5B%E9%B2%9C%E8%8A%B1%5D.png'
            },
            {
                'id': '001056',
                'text': '[中国加油]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E4%B8%AD%E5%9B%BD%E5%8A%A0%E6%B2%B9%5D.png'
            },
            {
                'id': '001057',
                'text': '[庆祝]',
                'icon': '%5B%E5%BA%86%E7%A5%9D%5D.png'
            },
            {
                'id': '001058',
                'text': '[生日蛋糕]',
                'icon': '%5B%E7%94%9F%E6%97%A5%E8%9B%8B%E7%B3%95%5D.png'
            },
            {
                'id': '001059',
                'text': '[MicDrop]',
                'hideInEmojiKeyboard': true,
                'icon': '%5BMicDrop%5D.png'
            },
            {
                'id': '001060',
                'text': '[赞同]',
                'icon': '%5B%E8%B5%9E%E5%90%8C%5D.png'
            },
            {
                'id': '001061',
                'text': '[药丸]',
                'icon': '%5B%E8%8D%AF%E4%B8%B8%5D.png'
            },
            {
                'id': '001062',
                'text': '[蜡烛]',
                'icon': '%5B%E8%9C%A1%E7%83%9B%5D.png'
            },
            {
                'id': '001063',
                'text': '[鸡蛋]',
                'icon': '%5B%E9%B8%A1%E8%9B%8B%5D.png'
            },
            {
                'id': '001064',
                'text': '[浪]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E6%B5%AA%5D.png'
            },
            {
                'id': '001065',
                'text': '[打call]',
                'icon': '%5B%E6%89%93call%5D.png'
            },
            {
                'id': '001066',
                'text': '[尬笑]',
                'icon': '%5B%E5%B0%AC%E7%AC%91%5D.png'
            },
            {
                'id': '001067',
                'text': '[坏笑]',
                'icon': '%5B%E5%9D%8F%E7%AC%91%5D.png'
            },
            {
                'id': '001068',
                'text': '[没眼看]',
                'icon': '%5B%E6%B2%A1%E7%9C%BC%E7%9C%8B%5D.png'
            },
            {
                'id': '001069',
                'text': '[嘿哈]',
                'icon': '%5B%E5%98%BF%E5%93%88%5D.png'
            },
            {
                'id': '001070',
                'text': '[前面的别走]',
                'icon': '%5B%E5%89%8D%E9%9D%A2%E7%9A%84%E5%88%AB%E8%B5%B0%5D.png'
            },
            {
                'id': '001071',
                'text': '[滑稽]',
                'icon': '%5B%E6%BB%91%E7%A8%BD%5D.png'
            },
            {
                'id': '001072',
                'text': '[捂脸]',
                'icon': '%5B%E6%8D%82%E8%84%B8%5D.png'
            },
            {
                'id': '001073',
                'text': '[左捂脸]',
                'icon': '%5B%E5%B7%A6%E6%8D%82%E8%84%B8%5D.png'
            },
            {
                'id': '001074',
                'text': '[666]',
                'icon': '%5B666%5D.png'
            },
            {
                'id': '001075',
                'text': '[2018]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B2018%5D.png'
            },
            {
                'id': '001076',
                'text': '[福]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E7%A6%8F%5D.png'
            },
            {
                'id': '001077',
                'text': '[红包]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E7%BA%A2%E5%8C%85%5D.png'
            },
            {
                'id': '001078',
                'text': '[鞭炮]',
                'icon': '%5B%E9%9E%AD%E7%82%AE%5D.png'
            },
            {
                'id': '001079',
                'text': '[财神]',
                'icon': '%5B%E8%B4%A2%E7%A5%9E%5D.png'
            },
            {
                'id': '001080',
                'text': '[饺子]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E9%A5%BA%E5%AD%90%5D.png'
            },
            {
                'id': '001081',
                'text': '[车票]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E8%BD%A6%E7%A5%A8%5D.png'
            },
            {
                'id': '001082',
                'text': '[火车]',
                'icon': '%5B%E7%81%AB%E8%BD%A6%5D.png'
            },
            {
                'id': '001083',
                'text': '[飞机]',
                'icon': '%5B%E9%A3%9E%E6%9C%BA%5D.png'
            },
            {
                'id': '001084',
                'text': '[射门]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E5%B0%84%E9%97%A8%5D.png'
            },
            {
                'id': '001085',
                'text': '[红牌]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E7%BA%A2%E7%89%8C%5D.png'
            },
            {
                'id': '001086',
                'text': '[黄牌]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E9%BB%84%E7%89%8C%5D.png'
            },
            {
                'id': '001087',
                'text': '[哨子]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E5%93%A8%E5%AD%90%5D.png'
            },
            {
                'id': '001088',
                'text': '[比分]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E6%AF%94%E5%88%86%5D.png'
            },
            {
                'id': '001089',
                'text': '[啤酒]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E5%95%A4%E9%85%92%5D.png'
            },
            {
                'id': '001090',
                'text': '[足球]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E8%B6%B3%E7%90%83%5D.png'
            },
            {
                'id': '001091',
                'text': '[大力神杯]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E5%A4%A7%E5%8A%9B%E7%A5%9E%E6%9D%AF%5D.png'
            },
            {
                'id': '001092',
                'text': '[锦鲤]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E9%94%A6%E9%B2%A4%5D.png'
            },
            {
                'id': '001095',
                'text': '[双手鼓掌]',
                'icon': '%5B%E5%8F%8C%E6%89%8B%E9%BC%93%E6%8E%8C%5D.png'
            },
            {
                'id': '001096',
                'text': '[火焰]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E7%81%AB%E7%84%B0%5D.png'
            },
            {
                'id': '001097',
                'text': '[祈福]',
                'icon': '%5B%E7%A5%88%E7%A6%8F%5D.png'
            },
            {
                'id': '001098',
                'text': '[亲吻]',
                'icon': '%5B%E4%BA%B2%E5%90%BB%5D.png'
            },
            {
                'id': '001099',
                'text': '[天使]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E5%A4%A9%E4%BD%BF%5D.png'
            },
            {
                'id': '001100',
                'text': '[樱花]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E6%A8%B1%E8%8A%B1%5D.png'
            },
            {
                'id': '001101',
                'text': '[加油]',
                'icon': '%5B%E5%8A%A0%E6%B2%B9%5D.png'
            },
            {
                'id': '001102',
                'text': '[泡泡枪]',
                'icon': '%5B%E6%B3%A1%E6%B3%A1%E6%9E%AA%5D.png'
            },
            {
                'id': '001103',
                'text': '[气球]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E6%B0%94%E7%90%83%5D.png'
            },
            {
                'id': '001104',
                'text': '[棒棒糖]',
                'icon': '%5B%E6%A3%92%E6%A3%92%E7%B3%96%5D.png'
            },
            {
                'id': '001105',
                'text': '[小黄鸭]',
                'icon': '%5B%E5%B0%8F%E9%BB%84%E9%B8%AD%5D.png'
            },
            {
                'id': '001106',
                'text': '[粽子]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E7%B2%BD%E5%AD%90%5D.png'
            },
            {
                'id': '001107',
                'text': '[70周年]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B70%E5%91%A8%E5%B9%B4%5D.png'
            },
            {
                'id': '001108',
                'text': '[国庆]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E5%9B%BD%E5%BA%86%5D.png'
            },
            {
                'id': '001109',
                'text': '[国庆快乐]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E5%9B%BD%E5%BA%86%E5%BF%AB%E4%B9%90%5D.png'
            },
            {
                'id': '001110',
                'text': '[圣诞老人]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E5%9C%A3%E8%AF%9E%E8%80%81%E4%BA%BA%5D.png'
            },
            {
                'id': '001111',
                'text': '[圣诞树]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E5%9C%A3%E8%AF%9E%E6%A0%91%5D.png'
            },
            {
                'id': '001112',
                'text': '[圣诞袜]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E5%9C%A3%E8%AF%9E%E8%A2%9C%5D.png'
            },
            {
                'id': '001113',
                'text': '[铃铛]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E9%93%83%E9%93%9B%5D.png'
            },
            {
                'id': '001114',
                'text': '[小老鼠]',
                'icon': '%5B%E5%B0%8F%E8%80%81%E9%BC%A0%5D.png'
            },
            {
                'id': '001115',
                'text': '[2020]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B2020%5D.png'
            },
            {
                'id': '001117',
                'text': '[灯笼]',
                'icon': '%5B%E7%81%AF%E7%AC%BC%5D.png'
            },
            {
                'id': '001118',
                'text': '[钱袋]',
                'icon': '%5B%E9%92%B1%E8%A2%8B%5D.png'
            },
            {
                'id': '001119',
                'text': '[鼠年大吉]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E9%BC%A0%E5%B9%B4%E5%A4%A7%E5%90%89%5D.png'
            },
            {
                'id': '001120',
                'text': '[舞狮]',
                'icon': '%5B%E8%88%9E%E7%8B%AE%5D.png'
            },
            {
                'id': '001121',
                'text': '[戴口罩]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E6%88%B4%E5%8F%A3%E7%BD%A9%5D.png'
            },
            {
                'id': '001122',
                'text': '[加油啊]',
                'icon': '%5B%E5%8A%A0%E6%B2%B9%E5%95%8A%5D.png'
            },
            {
                'id': '001123',
                'text': '[勤洗手]',
                'icon': '%5B%E5%8B%A4%E6%B4%97%E6%89%8B%5D.png'
            },
            {
                'id': '001124',
                'text': '[中国]',
                'icon': '%5B%E4%B8%AD%E5%9B%BD%5D.png'
            },
            {
                'id': '001125',
                'text': '[n95口罩]',
                'icon': '%5Bn95%E5%8F%A3%E7%BD%A9%5D.png'
            },
            {
                'id': '001126',
                'text': '[月亮]',
                'icon': '%5B%E6%9C%88%E4%BA%AE%5D.png'
            },
            {
                'id': '001127',
                'text': '[→_→]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E2%86%92_%E2%86%92%5D.png'
            },
            {
                'id': '001128',
                'text': '[⚡]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E2%9A%A1%5D.png'
            },
            {
                'id': '001129',
                'text': '[🍉]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%F0%9F%8D%89%5D.png'
            },
            {
                'id': '001130',
                'text': '[🍗]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%F0%9F%8D%97%5D.png'
            },
            {
                'id': '001131',
                'text': '[🎄]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%F0%9F%8E%84%5D.png'
            },
            {
                'id': '001132',
                'text': '[👏]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%F0%9F%91%8F%5D.png'
            },
            {
                'id': '001133',
                'text': '[👻]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%F0%9F%91%BB%5D.png'
            },
            {
                'id': '001134',
                'text': '[💊]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%F0%9F%92%8A%5D.png'
            },
            {
                'id': '001135',
                'text': '[💣]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%F0%9F%92%A3%5D.png'
            },
            {
                'id': '001136',
                'text': '[💩]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%F0%9F%92%A9%5D.png'
            },
            {
                'id': '001137',
                'text': '[🤓]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%F0%9F%A4%93%5D.png'
            },
            {
                'id': '001138',
                'text': '[🤗]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%F0%9F%A4%97%5D.png'
            },
            {
                'id': '001139',
                'text': '[👿]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%F0%9F%91%BF%5D.png'
            },
            {
                'id': '001140',
                'text': '[😱]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%F0%9F%98%B1%5D.png'
            },
            {
                'id': '001141',
                'text': '[🙃]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%F0%9F%99%83%5D.png'
            },
            {
                'id': '001142',
                'text': '[🙅]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%F0%9F%99%85%5D.png'
            },
            {
                'id': '001143',
                'text': '[🙈]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%F0%9F%99%88%5D.png'
            },
            {
                'id': '001144',
                'text': '[🙉]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%F0%9F%99%89%5D.png'
            },
            {
                'id': '001145',
                'text': '[🙊]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%F0%9F%99%8A%5D.png'
            },
            {
                'id': '001146',
                'text': '[🙋]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%F0%9F%99%8B%5D.png'
            },
            {
                'id': '001147',
                'text': '[🙏]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%F0%9F%99%8F%5D.png'
            },
            {
                'id': '001148',
                'text': '[爱你]',
                'icon': '%5B%E7%88%B1%E4%BD%A0%5D.png'
            },
            {
                'id': '001149',
                'text': '[奥特曼]',
                'icon': '%5B%E5%A5%A5%E7%89%B9%E6%9B%BC%5D.png'
            },
            {
                'id': '001150',
                'text': '[旅行]',
                'icon': '%5B%E6%97%85%E8%A1%8C%5D.png'
            },
            {
                'id': '001151',
                'text': '[点亮平安灯]',
                'icon': '%5B%E7%82%B9%E4%BA%AE%E5%B9%B3%E5%AE%89%E7%81%AF%5D.png'
            },
            {
                'id': '001152',
                'text': '[肥皂]',
                'icon': '%5B%E8%82%A5%E7%9A%82%5D.png'
            },
            {
                'id': '001153',
                'text': '[浮云]',
                'icon': '%5B%E6%B5%AE%E4%BA%91%5D.png'
            },
            {
                'id': '001154',
                'text': '[感冒]',
                'icon': '%5B%E6%84%9F%E5%86%92%5D.png'
            },
            {
                'id': '001155',
                'text': '[干杯]',
                'icon': '%5B%E5%B9%B2%E6%9D%AF%5D.png'
            },
            {
                'id': '001156',
                'text': '[给力]',
                'icon': '%5B%E7%BB%99%E5%8A%9B%5D.png'
            },
            {
                'id': '001157',
                'text': '[给你小心心]',
                'icon': '%5B%E7%BB%99%E4%BD%A0%E5%B0%8F%E5%BF%83%E5%BF%83%5D.png'
            },
            {
                'id': '001158',
                'text': '[话筒]',
                'icon': '%5B%E8%AF%9D%E7%AD%92%5D.png'
            },
            {
                'id': '001159',
                'text': '[挤眼]',
                'icon': '%5B%E6%8C%A4%E7%9C%BC%5D.png'
            },
            {
                'id': '001160',
                'text': '[礼物]',
                'icon': '%5B%E7%A4%BC%E7%89%A9%5D.png'
            },
            {
                'id': '001161',
                'text': '[绿丝带]',
                'icon': '%5B%E7%BB%BF%E4%B8%9D%E5%B8%A6%5D.png'
            },
            {
                'id': '001162',
                'text': '[男孩儿]',
                'icon': '%5B%E7%94%B7%E5%AD%A9%E5%84%BF%5D.png'
            },
            {
                'id': '001163',
                'text': '[女孩儿]',
                'icon': '%5B%E5%A5%B3%E5%AD%A9%E5%84%BF%5D.png'
            },
            {
                'id': '001164',
                'text': '[沙尘暴]',
                'icon': '%5B%E6%B2%99%E5%B0%98%E6%9A%B4%5D.png'
            },
            {
                'id': '001165',
                'text': '[伤心]',
                'icon': '%5B%E4%BC%A4%E5%BF%83%5D.png'
            },
            {
                'id': '001166',
                'text': '[生病]',
                'icon': '%5B%E7%94%9F%E7%97%85%5D.png'
            },
            {
                'id': '001167',
                'text': '[酸]',
                'icon': '%5B%E9%85%B8%5D.png'
            },
            {
                'id': '001168',
                'text': '[太开心]',
                'icon': '%5B%E5%A4%AA%E5%BC%80%E5%BF%83%5D.png'
            },
            {
                'id': '001169',
                'text': '[太阳]',
                'icon': '%5B%E5%A4%AA%E9%98%B3%5D.png'
            },
            {
                'id': '001170',
                'text': '[兔子]',
                'icon': '%5B%E5%85%94%E5%AD%90%5D.png'
            },
            {
                'id': '001171',
                'text': '[威武]',
                'icon': '%5B%E5%A8%81%E6%AD%A6%5D.png'
            },
            {
                'id': '001172',
                'text': '[微风]',
                'icon': '%5B%E5%BE%AE%E9%A3%8E%5D.png'
            },
            {
                'id': '001173',
                'text': '[围脖]',
                'icon': '%5B%E5%9B%B4%E8%84%96%5D.png'
            },
            {
                'id': '001174',
                'text': '[围观]',
                'icon': '%5B%E5%9B%B4%E8%A7%82%5D.png'
            },
            {
                'id': '001175',
                'text': '[武汉加油]',
                'icon': '%5B%E6%AD%A6%E6%B1%89%E5%8A%A0%E6%B2%B9%5D.png'
            },
            {
                'id': '001176',
                'text': '[喜]',
                'icon': '%5B%E5%96%9C%5D.png'
            },
            {
                'id': '001177',
                'text': '[下雨]',
                'icon': '%5B%E4%B8%8B%E9%9B%A8%5D.png'
            },
            {
                'id': '001178',
                'text': '[音乐]',
                'icon': '%5B%E9%9F%B3%E4%B9%90%5D.png'
            },
            {
                'id': '001179',
                'text': '[赞啊]',
                'icon': '%5B%E8%B5%9E%E5%95%8A%5D.png'
            },
            {
                'id': '001180',
                'text': '[炸鸡腿]',
                'icon': '%5B%E7%82%B8%E9%B8%A1%E8%85%BF%5D.png'
            },
            {
                'id': '001181',
                'text': '[照相机]',
                'icon': '%5B%E7%85%A7%E7%9B%B8%E6%9C%BA%5D.png'
            },
            {
                'id': '001182',
                'text': '[钟]',
                'icon': '%5B%E9%92%9F%5D.png'
            },
            {
                'id': '001183',
                'text': '[猪头]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E7%8C%AA%E5%A4%B4%5D.png'
            },
            {
                'id': '001184',
                'text': '[good]',
                'icon': '%5Bgood%5D.png'
            },
            {
                'id': '001185',
                'text': '[抱抱]',
                'icon': '%5B%E6%8A%B1%E6%8A%B1%5D.png'
            },
            {
                'id': '001186',
                'text': '[悲伤]',
                'icon': '%5B%E6%82%B2%E4%BC%A4%5D.png'
            },
            {
                'id': '001187',
                'text': '[并不简单]',
                'icon': '%5B%E5%B9%B6%E4%B8%8D%E7%AE%80%E5%8D%95%5D.png'
            },
            {
                'id': '001188',
                'text': '[馋嘴]',
                'icon': '%5B%E9%A6%8B%E5%98%B4%5D.png'
            },
            {
                'id': '001189',
                'text': '[吃瓜]',
                'icon': '%5B%E5%90%83%E7%93%9C%5D.png'
            },
            {
                'id': '001190',
                'text': '[打脸]',
                'icon': '%5B%E6%89%93%E8%84%B8%5D.png'
            },
            {
                'id': '001191',
                'text': '[顶]',
                'icon': '%5B%E9%A1%B6%5D.png'
            },
            {
                'id': '001192',
                'text': '[费解]',
                'icon': '%5B%E8%B4%B9%E8%A7%A3%5D.png'
            },
            {
                'id': '001193',
                'text': '[跪了]',
                'icon': '%5B%E8%B7%AA%E4%BA%86%5D.png'
            },
            {
                'id': '001194',
                'text': '[黑线]',
                'icon': '%5B%E9%BB%91%E7%BA%BF%5D.png'
            },
            {
                'id': '001195',
                'text': '[互粉]',
                'icon': '%5B%E4%BA%92%E7%B2%89%5D.png'
            },
            {
                'id': '001196',
                'text': '[可怜]',
                'icon': '%5B%E5%8F%AF%E6%80%9C%5D.png'
            },
            {
                'id': '001197',
                'text': '[困]',
                'icon': '%5B%E5%9B%B0%5D.png'
            },
            {
                'id': '001198',
                'text': '[来]',
                'icon': '%5B%E6%9D%A5%5D.png'
            },
            {
                'id': '001199',
                'text': '[喵喵]',
                'icon': '%5B%E5%96%B5%E5%96%B5%5D.png'
            },
            {
                'id': '001200',
                'text': '[拳头]',
                'icon': '%5B%E6%8B%B3%E5%A4%B4%5D.png'
            },
            {
                'id': '001201',
                'text': '[弱]',
                'icon': '%5B%E5%BC%B1%5D.png'
            },
            {
                'id': '001202',
                'text': '[失望]',
                'icon': '%5B%E5%A4%B1%E6%9C%9B%5D.png'
            },
            {
                'id': '001203',
                'text': '[思考]',
                'icon': '%5B%E6%80%9D%E8%80%83%5D.png'
            },
            {
                'id': '001204',
                'text': '[摊手]',
                'icon': '%5B%E6%91%8A%E6%89%8B%5D.png'
            },
            {
                'id': '001205',
                'text': '[握手]',
                'icon': '%5B%E6%8F%A1%E6%89%8B%5D.png'
            },
            {
                'id': '001206',
                'text': '[嘻嘻]',
                'icon': '%5B%E5%98%BB%E5%98%BB%5D.png'
            },
            {
                'id': '001207',
                'text': '[心]',
                'icon': '%5B%E5%BF%83%5D.png'
            },
            {
                'id': '001208',
                'text': '[耶]',
                'icon': '%5B%E8%80%B6%5D.png'
            },
            {
                'id': '001209',
                'text': '[右哼哼]',
                'icon': '%5B%E5%8F%B3%E5%93%BC%E5%93%BC%5D.png'
            },
            {
                'id': '001210',
                'text': '[左哼哼]',
                'icon': '%5B%E5%B7%A6%E5%93%BC%E5%93%BC%5D.png'
            },
            {
                'id': '001211',
                'text': '[作揖]',
                'icon': '%5B%E4%BD%9C%E6%8F%96%5D.png'
            },
            {
                'id': '001212',
                'text': '[haha]',
                'icon': '%5Bhaha%5D.png'
            },
            {
                'id': '001213',
                'text': '[NO]',
                'icon': '%5BNO%5D.png'
            },
            {
                'id': '001214',
                'text': '[ok]',
                'icon': '%5Bok%5D.png'
            },
            {
                'id': '001215',
                'text': '[欧耶]',
                'icon': '%5B%E6%AC%A7%E8%80%B6%5D.png'
            },
            {
                'id': '001216',
                'text': '[嫌弃]',
                'icon': '%5B%E5%AB%8C%E5%BC%83%5D.png'
            },
            {
                'id': '001217',
                'text': '[惊恐]',
                'icon': '%5B%E6%83%8A%E6%81%90%5D.png'
            },
            {
                'id': '001218',
                'text': '[社会]',
                'icon': '%5B%E7%A4%BE%E4%BC%9A%5D.png'
            },
            {
                'id': '001219',
                'text': '[ok啊]',
                'icon': '%5Bok%E5%95%8A%5D.png'
            },
            {
                'id': '001220',
                'text': '[耶耶耶]',
                'icon': '%5B%E8%80%B6%E8%80%B6%E8%80%B6%5D.png'
            },
            {
                'id': '001221',
                'text': '[撇嘴]',
                'icon': '%5B%E6%92%87%E5%98%B4%5D.png'
            },
            {
                'id': '001222',
                'text': '[emmm]',
                'icon': '%5Bemmm%5D.png'
            },
            {
                'id': '001223',
                'text': '[暗中观察]',
                'icon': '%5B%E6%9A%97%E4%B8%AD%E8%A7%82%E5%AF%9F%5D.png'
            },
            {
                'id': '001224',
                'text': '[拒绝]',
                'icon': '%5B%E6%8B%92%E7%BB%9D%5D.png'
            },
            {
                'id': '001225',
                'text': '[观望]',
                'icon': '%5B%E8%A7%82%E6%9C%9B%5D.png'
            },
            {
                'id': '001226',
                'text': '[苦思冥想]',
                'icon': '%5B%E8%8B%A6%E6%80%9D%E5%86%A5%E6%83%B3%5D.png'
            },
            {
                'id': '001227',
                'text': '[奥利给]',
                'icon': '%5B%E5%A5%A5%E5%88%A9%E7%BB%99%5D.png'
            },
            {
                'id': '001228',
                'text': '[挠头]',
                'icon': '%5B%E6%8C%A0%E5%A4%B4%5D.png'
            },
            {
                'id': '001229',
                'text': '[凝视]',
                'icon': '%5B%E5%87%9D%E8%A7%86%5D.png'
            },
            {
                'id': '001230',
                'text': '[无所谓]',
                'icon': '%5B%E6%97%A0%E6%89%80%E8%B0%93%5D.png'
            },
            {
                'id': '001231',
                'text': '[摸头]',
                'icon': '%5B%E6%91%B8%E5%A4%B4%5D.png'
            },
            {
                'id': '001232',
                'text': '[nonono]',
                'icon': '%5Bnonono%5D.png'
            },
            {
                'id': '001233',
                'text': '[握草]',
                'icon': '%5B%E6%8F%A1%E8%8D%89%5D.png'
            },
            {
                'id': '001234',
                'text': '[苦笑]',
                'icon': '%5B%E8%8B%A6%E7%AC%91%5D.png'
            },
            {
                'id': '001235',
                'text': '[饮酒醉]',
                'icon': '%5B%E9%A5%AE%E9%85%92%E9%86%89%5D.png'
            },
            {
                'id': '001236',
                'text': '[走你]',
                'icon': '%5B%E8%B5%B0%E4%BD%A0%5D.png'
            },
            {
                'id': '001237',
                'text': '[戳脸]',
                'icon': '%5B%E6%88%B3%E8%84%B8%5D.png'
            },
            {
                'id': '001238',
                'text': '[呕]',
                'icon': '%5B%E5%91%95%5D.png'
            },
            {
                'id': '001239',
                'text': '[泪奔]',
                'icon': '%5B%E6%B3%AA%E5%A5%94%5D.png'
            },
            {
                'id': '001240',
                'text': '[额]',
                'icon': '%5B%E9%A2%9D%5D.png'
            },
            {
                'id': '001241',
                'text': '[石化]',
                'icon': '%5B%E7%9F%B3%E5%8C%96%5D.png'
            },
            {
                'id': '001242',
                'text': '[工人]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E5%B7%A5%E4%BA%BA%5D.png'
            },
            {
                'id': '001243',
                'text': '[农民]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E5%86%9C%E6%B0%91%5D.png'
            },
            {
                'id': '001244',
                'text': '[稻草人]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E7%A8%BB%E8%8D%89%E4%BA%BA%5D.png'
            },
            {
                'id': '001245',
                'text': '[锦旗]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E9%94%A6%E6%97%97%5D.png'
            },
            {
                'id': '001246',
                'text': '[大红花]',
                'hideInEmojiKeyboard': true,
                'icon': '%5B%E5%A4%A7%E7%BA%A2%E8%8A%B1%5D.png'
            },
            {
                'id': '001247',
                'text': '[雪花]',
                'icon': '%5B%E9%9B%AA%E8%8A%B1%5D.png'
            },
            {
                'id': '001248',
                'text': '[2021]',
                'icon': '%5B2021%5D.png'
            },
            {
                'id': '001250',
                'text': '[牛年大吉]',
                'icon': '%5B%E7%89%9B%E5%B9%B4%E5%A4%A7%E5%90%89%5D.png'
            },
            {
                'id': '001251',
                'text': '[牛福到]',
                'icon': '%5B%E7%89%9B%E7%A6%8F%E5%88%B0%5D.png'
            },
            {
                'id': '001252',
                'text': '[云红包]',
                'icon': '%5B%E4%BA%91%E7%BA%A2%E5%8C%85%5D.png'
            },
            {
                'id': '001253',
                'text': '[牛洋洋]',
                'icon': '%5B%E7%89%9B%E6%B4%8B%E6%B4%8B%5D.png'
            },
            {
                'id': '001254',
                'text': '[牛斯拉]',
                'icon': '%5B%E7%89%9B%E6%96%AF%E6%8B%89%5D.png'
            },
            {
                'id': '001255',
                'text': '[云喝酒]',
                'icon': '%5B%E4%BA%91%E5%96%9D%E9%85%92%5D.png'
            },
            {
                'id': '001256',
                'text': '[温暖]',
                'icon': '%5B%E6%B8%A9%E6%9A%96%5D.png'
            },
            {
                'id': '001257',
                'text': '[Salute]',
                'icon': '%5BSalute%5D.png'
            },
            {
                'id': '001258',
                'text': '[叹气]',
                'icon': '%5B%E5%8F%B9%E6%B0%94%5D.png'
            },
            {
                'id': '001259',
                'text': '[裂开]',
                'icon': '%5B%E8%A3%82%E5%BC%80%5D.png'
            },
            {
                'id': '001260',
                'text': '[厉害]',
                'icon': '%5B%E5%8E%89%E5%AE%B3%5D.png'
            },
            {
                'id': '001261',
                'text': '[吃鲸]',
                'icon': '%5B%E5%90%83%E9%B2%B8%5D.png'
            },
            {
                'id': '001262',
                'text': '[比心]',
                'icon': '%5B%E6%AF%94%E5%BF%83%5D.png'
            },
            {
                'id': '001263',
                'text': '[AWSL]',
                'icon': '%5BAWSL%5D.png'
            },
            {
                'id': '001264',
                'text': '[打工人]',
                'icon': '%5B%E6%89%93%E5%B7%A5%E4%BA%BA%5D.png'
            },
            {
                'id': '001265',
                'text': '[小红花]',
                'icon': '%5B%E5%B0%8F%E7%BA%A2%E8%8A%B1%5D.png'
            },
            {
                'id': '001266',
                'text': '[金牌]',
                'icon': '%5B%E9%87%91%E7%89%8C%5D.png'
            },
            {
                'id': '001267',
                'text': '[牛啤]',
                'icon': '%5B%E7%89%9B%E5%95%A4%5D.png'
            },
            {
                'id': '001268',
                'text': '[星星]',
                'icon': '%5B%E6%98%9F%E6%98%9F%5D.png'
            }, {
                'id': '001269',
                'text': '[半星]',
                'icon': '%5B%E5%8D%8A%E6%98%9F%5D.png'
            }, {
                'id': '001270',
                'text': '[2022]',
                'icon': '%5B2022%5D.png'
            }, {
                'id': '001271',
                'text': '[虎年大吉]',
                'icon': '%5B%E8%99%8E%E5%B9%B4%E5%A4%A7%E5%90%89%5D.png'
            }, {
                'id': '001272',
                'text': '[虎妞妞]',
                'icon': '%5B%E8%99%8E%E5%A6%9E%E5%A6%9E%5D.png'
            }, {
                'id': '001273',
                'text': '[小脑斧]',
                'icon': '%5B%E5%B0%8F%E8%84%91%E6%96%A7%5D.png'
            }, {
                'id': '001274',
                'text': '[银牌]',
                'icon': '%5B%E9%93%B6%E7%89%8C%5D.png'
            }, {
                'id': '001275',
                'text': '[铜牌]',
                'icon': '%5B%E9%93%9C%E7%89%8C%5D.png'
            }, {
                'id': '001276',
                'text': '[滑雪]',
                'icon': '%5B%E6%BB%91%E9%9B%AA%5D.png'
            }, {
                'id': '001277',
                'text': '[高考加油]',
                'icon': '%5B%E9%AB%98%E8%80%83%E5%8A%A0%E6%B2%B9%5D.png'
            }, {
                'id': '001278',
                'text': '[金榜题名]',
                'icon': '%5B%E9%87%91%E6%A6%9C%E9%A2%98%E5%90%8D%5D.png'
            }, {
                'id': '001279',
                'text': '[高考必胜]',
                'icon': '%5B%E9%AB%98%E8%80%83%E5%BF%85%E8%83%9C%5D.png'
            }, {
                'id': '001280',
                'text': '[锦鲤附体]',
                'icon': '%5B%E9%94%A6%E9%B2%A4%E9%99%84%E4%BD%93%5D.png'
            }, {
                'id': '001281',
                'text': '[考的全会]',
                'icon': '%5B%E8%80%83%E7%9A%84%E5%85%A8%E4%BC%9A%5D.png'
            }, {
                'id': '001310',
                'text': '[雪糕]',
                'icon': '%5B%E9%9B%AA%E7%B3%95%5D.png'
            }, {
                'id': '001311',
                'text': '[可乐]',
                'icon': '%5B%E5%8F%AF%E4%B9%90%5D.png'
            }, {
                'id': '001312',
                'text': '[西瓜]',
                'icon': '%5B%E8%A5%BF%E7%93%9C%5D.png'
            }, {
                'id': '001313',
                'text': '[发]',
                'icon': '%5B%E5%8F%91%5D.png'
            }, {
                'id': '001314',
                'text': '[游戏手柄]',
                'icon': '%5B%E6%B8%B8%E6%88%8F%E6%89%8B%E6%9F%84%5D.png'
            }, {
                'id': '001315',
                'text': '[奖杯]',
                'icon': '%5B%E5%A5%96%E6%9D%AF%5D.png'
            }, {
                'id': '001316',
                'text': '[中国必胜]',
                'icon': '%5B%E4%B8%AD%E5%9B%BD%E5%BF%85%E8%83%9C%5D.png'
            }, {
                'id': '001317',
                'text': '[中国队加油]',
                'icon': '%5B%E4%B8%AD%E5%9B%BD%E9%98%9F%E5%8A%A0%E6%B2%B9%5D.png'
            },
        ]
    }]
  };

  var _config$packages = _slicedToArray(config.packages, 1),
      packages = _config$packages[0],
      imgpath = config.imgpath;

  var emoticonsList = packages.emoticons;
  var imgStyle = isIOS() ? 'height:1.15em; display:inline-block; margin:0 0.06em -0.205em 0.06em;' : 'height:1.12em; display:inline-block; margin:0 0.03em -0.205em 0.03em;';
  /**
   * 表情替换，例如输入text为'[疑问]'， 返回为<img>标签
   * @function replace
   * @param {String} str  - 处理如'[疑问]'
   * @return {String} img标签
   */

  var replace = function replace(str) {
    var reg = /\[.+?\]/ig;
    var result = str.replace(reg, function (text) {
      // text 格式如 '[疑问]'
      var icon = getIconByText(text);

      if (!icon) {
        return text;
      } // 替换成 <img> 标签


      return getImgTag(text, icon);
    });
    return result;
  };
  /**
   * 判断输入文本是否可以转为表情，返回true or false
   * @function query
   * @param {String} text  - 处理如'[疑问]'
   * @return {Boolean}
   */

  var query = function query(text) {
    var reg = /^\[.+?\]$/;

    if (!reg.test(text)) {
      // 不符合 '[微笑]' 这种格式
      return false;
    }

    var icon = replace(text);

    if (icon === text) {
      // 未替换成功
      return false;
    }

    return true;
  };
  /**
   * 将文本转换为img标签
   * @function getImgTag
   * @param {String} text  - 处理如'[疑问]'
   * @param {String} icon  - 图片名字，如'face_01.png'
   * @return {String} 图片对应的标签
   */

  var getImgTag = function getImgTag(text, icon) {
    // 注意要加随机数定期清理缓存
    var random = dateFormat(new Date(), 'yyyyMMddhh');
    return "<img data-text=\"".concat(text, "\" src=\"").concat(imgpath).concat(icon, "?r=").concat(random, "\" style=\"").concat(imgStyle, "\"/>");
  };
  /**
   * 通过文本获取config中的表情名，例如输入text为'[微笑]' ，返回'face_01.png'
   * @function getIconByText
   * @param {String} text  - 处理如'[微笑]'
   * @return {String} 返回config中对应的icon，如'face_01.png'
   */

  var getIconByText = function getIconByText(text) {
    var icon = '';
    var item;

    for (var i = 0; i < emoticonsList.length; i++) {
      item = emoticonsList[i];

      if (item.text === text) {
        icon = item.icon;
        break;
      }
    }

    return icon;
  };

  exports.getIconByText = getIconByText;
  exports.getImgTag = getImgTag;
  exports.query = query;
  exports.replace = replace;

  return exports;

}
