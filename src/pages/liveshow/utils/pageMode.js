/**
 * @file 注册设置暗黑模式模块
 * <AUTHOR>
 * @date 2021-11-09 20:37:48
 */

import nativePageMode from '@baidu/xbox-native/data/getNightMode';
import { invokeP } from '@baidu/xbox-native/invoke';
import { getAppSchemeHead, getUrlParam, getAppSource} from '@/pages/liveshow/utils/getUrlParam';
import { env } from '@/utils/env';

// 夜间模式/日间模式/暗黑模式enum
const pageModeEnum = {
    LIGHT: 0,
    NIGHT: 1,
    DARK: 2,
};

/**
 * @name getPageMode
 * @description 获取页面模式 nightmode/lightmode/darkmode，获取失败默认lightmode
 * @returns {Promise} 获取完成后的回调，返回内容是pageModeEnum里的值
 */
export const getPageMode = () =>
    new Promise((resolve) => {
        let theme = new RegExp(encodeURIComponent('Theme/dark'));
        let head = getAppSchemeHead();
        if (theme.test(navigator.userAgent)) {
            resolve(pageModeEnum.DARK);
            return;
        }

        invokeP('theme/getTheme', {}, head).then(
            (res) => {
                if (res && res.theme === 'dark') {
                    resolve(pageModeEnum.DARK);
                } else {
                    nativePageMode()
                        .then((res) => {
                            if (res && res.isNightMode) {
                                resolve(pageModeEnum.NIGHT);
                            } else {
                                resolve(pageModeEnum.LIGHT);
                            }
                        })
                        .catch((_) => resolve(pageModeEnum.LIGHT));
                }
            },
            (_) => resolve(pageModeEnum.LIGHT)
        );
    });

/**
 * @name setPageMode
 * @description 在html上设置页面模式：nightmode/lightmode/darkmode; body上设置系统：ios/android，矩阵：is-tomas
 * @returns {Promise} 执行完毕的毁掉
 */
export const setPageMode = () => {
    // 添加html类名
    getPageMode().then(mode => {
        const modeClassMap = {
            [pageModeEnum.LIGHT]: 'lightmode',
            [pageModeEnum.NIGHT]: 'nightmode',
            [pageModeEnum.DARK]: 'darkmode',
        };

        try {
            const modeClassName = modeClassMap[mode] || modeClassMap[pageModeEnum.LIGHT];
            const html = document.querySelector('html');
            html.classList.remove(modeClassName);
            html.classList.add(modeClassName);
        } catch (e) {
            console.error(e);
        }
    });

    // 添加body类名
    const bodyClassMap = {
        'ios': env.isIOS,
        'android': env.isAndroid,
        'is-tomas': env.isTomas || getAppSource() === 'tomas',
    };
    document.body.classList.add(...Object.keys(bodyClassMap).filter(item => bodyClassMap[item]));
}
