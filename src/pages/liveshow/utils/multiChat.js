/**
 * @file IM消息模块（非单例）
 * <AUTHOR> <EMAIL>
 * @date 2021-11-11 21:51:00 ，2025-02-14
 */
import {EventEmitter} from 'eventemitter3';
import {getHistoryComment} from '@/pages/liveshow/service/api';
import {LiveStatus} from '@/pages/liveshow/config/const';
import {format} from 'timeago.js';
import Cookies from 'js-cookie';
import M3u8text from '@/utils/m3u8text';
import IM from '@baidu/im-jssdk/dist/im.min';

export const MessageType = {
    MESSAGE: 'MESSAGE',
    TOTAL_USER: 'TOTAL_USER',
    LIVE_END: 'LIVE_END',
    LIVE_CHANGE_FLOW: 'LIVE_CHANGE_FLOW',
    LIVE_START: 'LIVE_START'
};

class MultiChat extends EventEmitter {
    constructor() {
        super();
        this.option = {
            delay: 5000, // 默认轮询间隔 5s
            m3u8Link: ''
        };
        this.status = LiveStatus.NOT_START;
        this.totaluser = 0;
        this.msgIdMap = {};
    }

    async start(option = {}) {
        this.msgIdMap = {};
        this.totaluser = 0;
        Object.assign(this.option, option);
        this.status = option.status;
        this.addDefaultMsg = option.addDefaultMsg;
        this.chatID = option.chatID;
        this.isLogin = option.isLogin;

        // 初始化历史消息
        await this.historyMessage();

        if ('WebSocket' in window) {
            this.initIM();
        }
        else {
            this.initM3U8Message();
        }
    }

    historyMessage() {
        return getHistoryComment().then((list) => {
            if (list && list.length) {
                list.forEach((item) => {
                    try {
                        const content = JSON.parse(item.content);
                        const text = JSON.parse(content.text);
                        // 只有type 0 是聊天消息，其他需要过滤下
                        if (+item.type === 0) {
                            let msg = this.handleMsg({
                                content: text,
                                msgId: item.msgid,
                                createTime: item.create_time,
                                checkRemoveRepeat: false
                            });
                            if (!this.msgIdMap[item.msgid] && msg) {
                                this.msgIdMap[item.msgid] = msg;
                            }
                        }
                    }
                    catch (e) {
                        console.log(e, item);
                    }
                });
            }

            this.addChatItem();
        });
    }

    handleMsg(data) {
        let {content = {}, msgId, createTime, checkRemoveRepeat = true} = data;
        let word = '';
        // 添加评论的时候去重
        if (checkRemoveRepeat && this.msgIdMap[msgId]) {
            return;
        }
        content.createTime = createTime;
        content.timestr = format(createTime * 1e3, 'zh_CN');
        // 根据content的内容处理消息，有部分是旧版有，但是新版去掉了相应UI设计，这部分消息过滤掉不显示
        switch (`${content.message_type}`) {
            case '0':
                // 文本消息
                word = content.content || content.message_body.txt.word;
                break;
            case '3': // 文字链接，需要退化为文本消息
                word = content.message_body.link.title;
                break;
            case '5': // 语音消息，需要退化为文本消息，提示不支持
                word = '[语音] 不支持消息类型';
                break;
            default:
                if (content.content) {
                    word = content.content;
                }
                break;
        }
        // 是否是回复的消息
        if (
            content.at_name
            && +content.at_message_type === 0
            && content.at_message_body
            && content.at_message_body.txt
            && content.at_message_body.txt.word
            && content.message_body
            && content.message_body.txt
            && content.message_body.txt.word
        ) {
            word = content.message_body.txt.word;
            content.responseUser = content.at_name;
        }
        word = word.trim();
        if (word === '') {
            return;
        }
        const d = {
            ...content,
            word,
            msgId
        };
        return d;
    }

    addChatItem() {
        let list = Object.values(this.msgIdMap);

        // 根据时间重新排序
        list.sort((a, b) => a.createTime - b.createTime);
        let len = list.length;

        // 如果聊天列表超过一定数量，去掉头部，将页面元素数量控制一下
        if (len > 50) {
            list = list.slice(len - 50, len);
        }

        if (this.addDefaultMsg) {
            // 添加默认消息
            list.unshift(
                {
                    msgId: 'defaultMsg',
                    // word: '',
                    /* eslint-disable max-len */
                    word: '官方提醒：欢迎来到直播间！我们提倡绿色文明直播，禁止未成年人直播或充值消费。如直播间内出现反动、违法违规、色情低俗、吸烟酗酒等内容，经举报或巡查发现将严肃处理。如主播在直播过程中以推广服务，交友等方式诱导打赏、私下交易，请谨慎判断，以防人身或财产损失。'
                    /* eslint-enable max-len */
                }
            );
        }

        this.emit(MessageType.MESSAGE, list);
    }

    addOneItem(item) {
        const d = this.handleMsg(item);
        if (d && !this.msgIdMap[d.msgId]) {
            this.msgIdMap[d.msgId] = d;
            this.addChatItem();
        }
    }

    // 初始化IM消息模块
    async initIM() {
        const baiduUid = Cookies.get('BAIDUID');
        const client = new IM({
            appId: ********, // 移动开发者中心申请的APPID
            deviceType: 3, // web：3
            accountType: this.isLogin ? 7 : 10, // 登陆态：7 未登陆：10 仅柠檬未登录传6
            appVersion: 'h5', // 默认写死
            sdkVersion: '7450001', // 默认写死
            BAIDUID: baiduUid, // cookie：BAIDUID
            // 测试：http://rd-im-server.bcc-szth.baidu.com:8111； 上线时更换为 https://pim.baidu.com
            domain: 'https://pim.baidu.com',
            // 测试：http://rd-im-server.bcc-szth.baidu.com:8089； 上线时更换为 https://pim.baidu.com
            lcpDomain: 'https://pim.baidu.com'
        }, {
            chat: true,
            session: false,
            signaling: false,
            roomChat: true
        });
        window.imChat = client;

        client.onConnect(async () => {
            // IM初始化是异步的，初始化成功进行下一步操作
            client.enterLiveChatRoom({
                mcast_id: +this.chatID
            }).then((data) => {
                window.console.log('加入房间：-----', data);
            }).catch(_error => {
                window.console.log('_error_error_error', _error);
            });
        });

        client.onRoomMessage(data => {
            try {
                const {normal, system} = data;
                if (normal && normal.length > 0) {
                    this.initMessage(normal);
                }
                if (system && system.length > 0) {
                    this.initMessage(system);
                }
            // eslint-disable-next-line no-empty
            }
            catch (error) {}
        });
    }

    initM3U8Message() {
        const {m3u8Link, delay} = this.option;
        if (!m3u8Link) {
            return;
        }
        const m3u8player = new M3u8text({
            file: m3u8Link,
            tsSpaceTime: delay,
            minSpaceTime: delay
        });
        m3u8player.on('resolveMessage', (message) => {
            this.handleMessage(message);
        });
    }

    // 消息初始化
    initMessage(data) {
        if (Array.isArray(data)) {
            data.forEach(item => {
                this.handleMessage(item);
            });
        }
    }

    // 统一抽离消息处理方法
    handleMessage(message) {
        if (+message.type === 0) {
            try {
                let content = typeof message.content === 'object' ? message.content : JSON.parse(message.content);
                content = typeof content.text === 'object' ? content.text : JSON.parse(content.text);
                let msgId = message.msgid;
                const isLive
                    = this.status !== LiveStatus.END_NO_REVIEW && this.status !== LiveStatus.END_HAS_REVIEW;
                const isPreview = +this.status === LiveStatus.NOT_START; // -1
                switch (+content.type) {
                    case 0:
                        this.addOneItem({
                            content,
                            msgId,
                            createTime: message.create_time
                        });
                        break;
                    case 101:
                        // 直播数据消息，用于刷新观众数、新加入观众、关众列表、点赞等
                        // 回放状态不处理101消息
                        if (isLive) {
                            if (content.data && content.data.totaluser) {
                                const totaluser = +content.data.totaluser;
                                if (this.totaluser !== totaluser) {
                                    this.totaluser = totaluser;
                                    this.emit(MessageType.TOTAL_USER, this.totaluser);
                                }
                            }
                        }
                        break;
                    case 102:
                        // 直播结束消息
                        if (isLive) {
                            this.status = LiveStatus.END_NO_REVIEW;
                            this.emit(MessageType.LIVE_END, this.status);
                        }
                        break;
                    case 104:
                        // 直播切流
                        if (isLive) {
                            this.emit(MessageType.LIVE_CHANGE_FLOW, content.data.live_hls_url);
                        }
                        break;
                    case 107:
                        // 直播开始消息（分享页预约态用）
                        if (isPreview && content.data
                            && content.data?.service_info?.content?.content_type === 'preview_trans_2_living'
                        ) {
                            window.console.log('直播开始消息');
                            this.emit(MessageType.LIVE_START);
                        }
                        break;
                    default:
                        break;
                }
            }
            catch (e) {
                console.log(e, message);
            }
        }
    }
}

export default MultiChat;
