/**
 * @file 回流相关封装
 * <AUTHOR>
 * @date 2021-11-22 22:00:17
 */
import { invokeApp } from '@baidu/ug-invoke-app';
import { env } from '@/utils/env';
import { PageStore } from '@/pages/liveshow/service/register';
import { getAppSchemeHead, jsonToQuery, getAppSource} from './getUrlParam';
import getSchemeParams, {getReflowSchemeSource} from './getSchemeParams';
import { reflowCallbackLog } from '@/pages/liveshow/log';

export const matrixAppSchemeHeaderList = [
    // 好看视频
    'baiduhaokan',
    // 贴吧
    'bdtiebalive',
    // 有驾
    'youjia',
    // 汉语APP
    'baidudict',
    // 古物潮玩
    'fortunecat',
    // 百度汉语
    'bdhiphop',
    // 百度健康
    'bdhealthapp',
    // 百度大字版
    'tomas',
    // 百度极速版
    'baiduboxlite',
    // 荣耀浏览器
    'bdhonorbrowser'
];

export const schemeHeader = getAppSchemeHead();

export const isMatrix = matrixAppSchemeHeaderList.includes(schemeHeader);

// 分享页已经接入口令的 端： 百度/极速/好看
const supportTokenArray = !isMatrix || ['haokan', 'lite'].indexOf(getAppSource()) !== -1;

// 回流失败函数
const failReflow = protocol => {
    location.href = `market://details?packagename=${protocol}`;
};

// 回流跳转函数
export const refolwForConfig = (config, type = 'room', status = 0) => {
    if (!config) {
        return;
    }
    return invokeApp(config)
        .then((res) => {
            // 仅在非预约直播间调起回调打点 && 新增状态
            if (type === 'room') {
                reflowCallbackLog(status, 'reflow_then', '', 0, '15263', true, '', res);
            }
            if (+res.status >= 20000) {
                failReflow(config.appName);
            }
        })
        .catch(err => {
            // 仅在非预约直播间调起失败打点 && 新增状态
            if (type === 'room') {
                reflowCallbackLog(status, 'reflow_catch', '', 0, '15263', true, '', err);
            }
        });
};

/**
 * @name matrixAppReflow
 * @description 回流到矩阵逻辑
 * @param {Number/String} roomId 需要回流的直播间id，必填
 */
const matrixAppReflow = (roomId, queryParams, isPre = true) => {

    // 好看场景 接入口令
    if (roomId === PageStore.roomId
        && supportTokenArray
        && PageStore.schemaInfo
        && PageStore.schemaInfo.enter_cmd
    ) {
        return {
            appName: schemeHeader,
            scheme: PageStore.schemaInfo.enter_cmd,
            token: PageStore.schemaInfo.open_token
        }
    }

    const source = PageStore.queryParams.source;
    const supplementParams = !queryParams
        ? { contour: '', deep: '' }
        : {
            contour: `&shareTaskInfo=${encodeURIComponent(JSON.stringify(queryParams))}`,
            deep: `, "shareTaskInfo":${JSON.stringify(queryParams)}`,
        };
    const finalSource = getReflowSchemeSource(null, isPre ? 'preview' : 'liveRoom');
    if (schemeHeader === 'baidudict') {
        // 汉语 APP baidudict://com.baidu.dict?page=live&params={"roomId":"直播roomid", "source":”来源“}
        return {
            appName: 'baiduhanyu',
            scheme: `baidudict://com.baidu.dict?page=live&params={"roomId":"${roomId}", "source":"${source}"${supplementParams.deep}}`,
        };
    }
    if (schemeHeader === 'baiduhaokan') {
        return {
            appName: 'baiduhaokan',
            scheme: `baiduhaokan://video/mixlive?room_id=${roomId}&source=${finalSource}${supplementParams.contour}`,
        };
    }
    if (schemeHeader === 'fortunecat') {
        // 汉语 APP baidudict://com.baidu.dict?page=live&params={"roomId":"直播roomid", "source":”来源“}
        const encodeParams = encodeURIComponent(
            JSON.stringify({
                roomId: `${roomId}`,
                source: '',
                extRequest: {},
            }),
        );
        return {
            appName: 'fortunecat',
            scheme: `fortunecat://live?p=${encodeParams}&roomId=${roomId}${supplementParams.contour}`,
        };
    }
    if (schemeHeader === 'youjia') {
        // youjia://app/live?roomid=**********
        return {
            appName: 'youjia',
            scheme: `youjia://app/live?roomid=${roomId}${supplementParams.contour}`,
        };
    }
    if (schemeHeader === 'bdhiphop') {
        // bdhiphop://live/media/joinRoom?roomId=
        return {
            appName: 'rap',
            scheme: `bdhiphop://live/media/joinRoom?roomId=${roomId}${supplementParams.contour}`,
        };
    }
    if (schemeHeader === 'bdhealthapp') {
        // bdhiphop://live/media/joinRoom?roomId=
        const encodePrama = encodeURIComponent(
            JSON.stringify({
                roomId: `${roomId}`
            }),
        );
        return {
            appName: 'bdhealthapp',
            scheme: `bdhealthapp://app/live/room?params=${encodePrama}`,
        };
    }
    if(schemeHeader === 'tomas') {
        const encodePrama = encodeURIComponent(
            JSON.stringify({
                roomId: `${roomId}`,
                source: 'tomas',
            }),
        );
        return {
            appName: 'tomas',
            scheme: `tomas://v11/live/enterStreamRoom?params=` + encodePrama,
        };
    }
    return {
        appName: schemeHeader === 'bdtiebalive' ? 'tieba' : schemeHeader,
        scheme: `${schemeHeader}://video/mixlive?room_id=${roomId}&source=${finalSource}${supplementParams.contour}`,
    };
};

/**
 * @name openReflowByUrl
 * @description 直播预约页回流，为什么使用enterBookRoom可参考http://wiki.baidu.com/pages/viewpage.action?pageId=**********
 * @param {String} [url] backupScheme参数
 * @param {Array} [toolids] backupScheme参数
 */
export const openReflowByUrl = (
        url = window.location.href,
        toolids = ['2', '3'],
        isAutoInvoke = false,
        justRetScheme = false
    ) => {
    const isMatrixApp = matrixAppSchemeHeaderList.includes(schemeHeader);
    let config = null;

    // 支持url参数判断不执行回流
    if (PageStore.queryParams.noreflow === 'all') {
        return;
    }

    if (isMatrixApp && PageStore.source !== 'lite') {
        config = matrixAppReflow(PageStore.roomId);
    } else {
        const failUrl = osProtocol === 'baiduboxvision'
            ? 'https://activity.baidu.com/mbox/4a81ad9961/ulinkFailPage?appName=baiduboxvision'
            : '';
        const osProtocol = getAppSchemeHead();

        // 获取跳转scheme的backup参数
        const backupSchemeParams = {
            opentype: 1,
            sfrom: 'feed',
            newbrowser: 1,
            rbtnstyle: 0,
            isBdboxShare: 1,
            isla: 0,
            menumode: 2,
            openurl: encodeURIComponent(url),
            toolbaricons: encodeURIComponent(JSON.stringify({ toolids })),
        };
        const backupScheme = `${osProtocol}://easybrowse?${jsonToQuery(backupSchemeParams)}`;

        // 获取跳转scheme所有参数
        const schemeParams = getSchemeParams({
            roomId: PageStore.roomId,
            type: 'preview',
            outerOthers: { backup: encodeURIComponent(backupScheme) },
        });

        const scheme = `${osProtocol}://v11/live/enterBookRoom?${jsonToQuery(schemeParams)}`;

        if (supportTokenArray && PageStore.schemaInfo && PageStore.schemaInfo.enter_cmd) {
            config = {
                appName: osProtocol,
                failUrl,
                scheme: PageStore.schemaInfo.enter_cmd,
                token: PageStore.schemaInfo.open_token,
                toStore: isAutoInvoke ? false : true,
            }
        }
        else {
            config = {
                appName: osProtocol,
                failUrl,
                scheme,
                toStore: isAutoInvoke ? false : true,
            };
        }
    }

    // 预约场景，仅返回调起scheme, 不执行调起行为
    if (justRetScheme) {
        return config.scheme;
    }

    return refolwForConfig(config, 'url');
};

/**
 * @name reflow
 * @description 直播落地页回流
 * @param {Number/String} roomId 回流的直播间id，必填
 * @param {String} [tiebaCmd] tiebaCmd
 * @param {Boolean} [isPaid] 是否是购买
 * @param {Object} [queryParams] 矩阵app需要的参数
 */
export const reflow = ({
    roomId = PageStore.roomId,
    tiebaCmd = '',
    isPaid = false,
    queryParams,
    outerSchemeParams = {},
    status = 0
}, isAutoInvoke = false, justRetScheme = false) => {
    const isMatrixApp = matrixAppSchemeHeaderList.includes(schemeHeader);
    let config = null;

    // 支持url参数判断不执行回流
    if (PageStore.queryParams.noreflow === 'all') {
        return;
    }

    if (isMatrixApp && PageStore.source !== 'lite') {
        config = matrixAppReflow(roomId, queryParams, false);
    } else {
        const osname = getAppSchemeHead(tiebaCmd && 'tieba');
        config = {
            appName: osname,
            scheme: '',
            toStore: true,
            useDeeplink: true
        };

        if (tiebaCmd) {
            const feedTabsScheme = osname + '://v11/appTab/select?item=home&upgrade=0&delaytime=0.2';
            config.scheme = feedTabsScheme + '&next=' + encodeURIComponent(tiebaCmd);
        } else {
            // 百度看看处理失败默认页
            if (osname === 'baiduboxvision') {
                config.failUrl = 'https://activity.baidu.com/mbox/4a81ad9961/ulinkFailPage?appName=baiduboxvision';
                config.ulink = '';
            }

            let outerOthers = {};
            if (env.isIOS) {
                Object.assign(outerOthers, {
                    action: 'enterRoom',
                    minver: isPaid ? '11.22' : '8.0.0.0',
                });
            } else {
                Object.assign(outerOthers, {
                    upgrade: 1,
                });
            }
            let schemeParams = getSchemeParams({
                roomId,
                outerOthers,
                outerParams: outerSchemeParams,
                schemeType: 'enterRoom',
            });

            // 百度 / lite场景
            if (roomId === PageStore.roomId
                && supportTokenArray
                && PageStore.schemaInfo
                && PageStore.schemaInfo.enter_cmd
            ) {
                config.scheme = PageStore.schemaInfo.enter_cmd;
                config.token = PageStore.schemaInfo.open_token;
            }
            else {
                config.scheme = `${osname}://v14/live/enterRoom?${jsonToQuery(schemeParams)}`;
            }

            // 非微信 回流手百场景 自动调起 失败停留在当前页面
            if (isAutoInvoke) {
                config.toStore = false;
            }

        }
    }

    // 除预约场景，仅返回调起scheme, 不执行调起行为
    if (justRetScheme) {
        return config.scheme;
    }

    // 回流直播间
    return refolwForConfig(config, 'room', status);
};
