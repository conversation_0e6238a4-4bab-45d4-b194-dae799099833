import {PageStore} from '@/pages/liveshow/service/register';
import {env} from '@/utils/env';

/**
 * @file 此文件处理所有与url相关的逻辑
 * <AUTHOR>
 * @date 2021-11-09 20:37:48
 */

// 导流 APP来源
const APP_NAME_PARAM = {
    // a6: '百度看看',
    // i6: '百度看看',
    baiduboxvision: '百度看看',
    haokan: '好看视频',
    default: '百度',
    lite: '百度极速版',
    tieba: '百度贴吧',
    youjia: '有驾',
    baidudict: '百度汉语',
    fortunecat: '古物潮玩',
    hiphop: '音磁',
    baidujiankangapp: '百度健康',
    tomas: '百度大字版'
};
const APP_NAME_SCHEME = {
    // a6: 'baiduboxvision',
    // i6: 'baiduboxvision',
    baiduboxvision: 'baiduboxvision',
    // 好看视频
    haokan: 'baiduhaokan',
    // 百度App
    default: 'baiduboxapp',
    // 百度极速版
    lite: 'baiduboxlite',
    // 贴吧
    tieba: 'bdtiebalive',
    // 有驾
    youjia: 'youjia',
    // 汉语App
    baidudict: 'baidudict',
    // 古物潮玩
    fortunecat: 'fortunecat',
    // 音磁
    hiphop: 'bdhiphop',
    // 百度健康
    baidujiankangapp: 'bdhealthapp',
    // 百度大字版
    tomas: 'tomas',
    // 荣耀浏览器
    bdhonorbrowser: 'bdhonorbrowser'
};

export const IS_APP_MAP = {
    'haokan': env.isHaokan,
    'lite': env.isLiteBox,
    'tomas': env.isTomas
};

const APP_ICONS = {
    'baiduboxapp': 'https://ala-gift.cdn.bcebos.com/gift/2025-2/1740049756459/live_baidu_icon1.png',
    'baiduboxlite': 'https://b.bdstatic.com/searchbox/icms/searchbox/img/lite-icon1.png',
    'baiduhaokan': 'https://b.bdstatic.com/searchbox/icms/searchbox/img/haokan-icon1.png',
    'bdtiebalive': 'https://b.bdstatic.com/searchbox/icms/searchbox/img/tieba-icon1.png',
    'duxiaoshi': 'https://b.bdstatic.com/searchbox/icms/searchbox/img/duxiaoshi-icon1.png',
    'fortunecat': 'https://b.bdstatic.com/searchbox/icms/searchbox/img/fortunecat-icon1.png',
    'baidudict': 'https://b.bdstatic.com/searchbox/icms/searchbox/img/hanyu-icon1.png',
    'bdhealthapp': 'https://b.bdstatic.com/searchbox/icms/searchbox/img/jiankang-icon1.png',
    'youjia': 'https://b.bdstatic.com/searchbox/icms/searchbox/img/youjia-icon1.png',
    'tomas': 'https://ala-gift.cdn.bcebos.com/gift/2022-8/1660728061573/tomas-icon1.png'
};
const APP_ICONS_HAS_WORD = {
    'baiduboxapp': 'https://ala-gift.cdn.bcebos.com/gift/2025-2/1739259327077/live_baidu_icon2.png',
    'baiduboxlite': 'https://b.bdstatic.com/searchbox/icms/searchbox/img/lite-icon2.png',
    'baiduhaokan': 'https://b.bdstatic.com/searchbox/icms/searchbox/img/haokan-icon2.png',
    'bdtiebalive': 'https://b.bdstatic.com/searchbox/icms/searchbox/img/tieba-icon2.png',
    'duxiaoshi': 'https://b.bdstatic.com/searchbox/icms/searchbox/img/duxiaoshi-icon2.png',
    'fortunecat': 'https://b.bdstatic.com/searchbox/icms/searchbox/img/fortunecat-icon2.png',
    'baidudict': 'https://b.bdstatic.com/searchbox/icms/searchbox/img/hanyu-icon2.png',
    'bdhealthapp': 'https://b.bdstatic.com/searchbox/icms/searchbox/img/jiankang-icon2.png',
    'youjia': 'https://b.bdstatic.com/searchbox/icms/searchbox/img/youjia-icon2.png',
    'tomas': 'https://ala-gift.cdn.bcebos.com/gift/2022-8/1660726727883/bdtomas-icon2.png'
};

export const getUrlAllParam = (link = '') => {
    let paramStr = '';
    if (link && typeof link === 'string') {
        paramStr = link.substring(link.indexOf('?') + 1, link.length).replace(/#\/$/, '');
    }
    else {
        const hash = window.location.hash;
        paramStr = window.location.search.substr(1) || hash.substring(hash.indexOf('?') + 1, hash.length);
    }
    const urlList = paramStr.split('&');
    const urlObject = {};
    urlList.forEach((item) => {
        const urlItem = item.split('=');
        if (urlItem[1]) {
            urlObject[urlItem[0]] = decodeURIComponent(urlItem[1]);
        }
    });
    return urlObject;
};

export const getUrlParam = (name) => {
    let reg = new RegExp(`(^|(&|/?))${name}=([^&]*)(&|$)`, 'i');
    let r = window.location.search.substr(1).match(reg);
    if (r !== null) {
        return r[3];
    }
    return '';
};

export const jsonToQuery = (json) => {
    if (typeof json === 'string') {
        return json;
    }
    let arr = [];
    Object.keys(json).forEach((i) => {
        arr.push(`${i}=${json[i]}`);
    });
    return arr.join('&');
};

export const checkHasValue = (value) => {
    if (value) {
        let ignore = ['nil', 'null', 'nullb', 'undefined', '0'];
        let check = true;
        ignore.forEach((item) => {
            if (item === value) {
                check = false;
            }
        });
        return check;
    }
    return false;
};

export const replaceProtocol = (url) => {
    if (typeof url !== 'string') {
        return;
    }
    const protocol = location.protocol.indexOf('https') >= 0 ? 'https' : 'http';
    return url.replace(/^(https|http)/, protocol);
};

// appointSource 指定source值获取appName，如未传，则根据url source来源获取appName
export const getAppName = appointSource => {
    let source = getAppSource(appointSource) || appointSource;
    let appName = APP_NAME_PARAM[source] || APP_NAME_PARAM.default;
    return appName;
};

export const getAppSource = appointSource => {
    const urlSrc = decodeURIComponent(getUrlParam('source')) || '';
    let defaultSource = urlSrc;
    if (urlSrc.includes('|')) {
        defaultSource = urlSrc.split('|')[0];
    }
    Object.keys(IS_APP_MAP).forEach(key => {
        if (IS_APP_MAP[key]) {
            defaultSource = key;
        }
    });
    let source = appointSource || defaultSource;
    return source;
};

// appointSource 指定source值获取scheme头，如未传，则根据url source来源获取scheme头
export const getAppSchemeHead = appointSource => {
    let source = getAppSource(appointSource) || appointSource;
    let scheme = APP_NAME_SCHEME[source] || APP_NAME_SCHEME.default;
    return scheme;
};

export const h5UrlForRoomId = (roomId = PageStore.roomId) => {
    const source = getUrlParam('source') || 'h5pre';
    return `https://live.baidu.com/m/media/multipage/liveshow/index.html?room_id=${roomId}&source=${source}`;
};

// 根据source来源获取app icon
export const getAppIcons = (type = 1) => {
    const ICONS = type === 1 ? APP_ICONS : APP_ICONS_HAS_WORD;
    const appSchemeHead = getAppSchemeHead();

    return ICONS[appSchemeHead] || '';
};
