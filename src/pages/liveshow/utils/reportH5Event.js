/**
 * @file 在线人数上报模块
 * <AUTHOR>
 * @date 2021-11-10 21:45:11
 */

import {pcUsersReport} from '@/pages/liveshow/service/api';
import {registerPageHidden} from '@/utils/index';

const interval = 3e4;
let errCount = 3;
let timeOut = null;

const run = () => {
    pcUsersReport()
        .then(() => {
            console.log('pcUsersReport: suc');
            timeOut = setTimeout(() => {
                run();
            }, interval);
        })
        .catch((err) => {
            console.error('pcUsersReport err:', err);
            errCount--;
            if (errCount > 0) {
                run();
            }
        });
};

const pageHidden = () => {
    registerPageHidden(
        () => {
            console.log('page hidden pcUsersReport stop');
            timeOut && clearTimeout(timeOut);
        },
        () => {
            console.log('page show pcUsersReport run');
            run();
        }
    );
};

export default () => {
    run();
    pageHidden();
};
