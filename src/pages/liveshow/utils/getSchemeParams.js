/**
 * @file 获取scheme参数，回流和端内跳转都会用到
 * <AUTHOR>
 * @date 2022-4-13 15:00:17
 */
import { env } from '@/utils/env';
import { checkHasValue } from './getUrlParam';
import { PageStore } from '@/pages/liveshow/service/register';
import { versionCompare } from '@baidu/boxx/env';
import { weirwood } from '@/pages/liveshow/utils/errorMonitor';

/**
 * @name getReflowSchemeSource
 * @description 获取跳转NA时需要的source值（返回值为json时，NA取json中livesource）
 * @param {String} schemeType enterRoom/enterStreamRoom/enterBookRoom
 * type=enterRoom：支持json
 * type=enterstreamroom/enterBookRoom：>=13.2支持json及字符串，<13.2支持字符串；
 * @returns {Object/String}
 */
export const getReflowSchemeSource = (schemeType, type) => {
    let source = PageStore.queryParams.source;
    const shareFrom = PageStore.queryParams.share_from || ''; // 自新版任务系统上线，新增标识na分享
    // 保留历史遗留的参数
    let bdVid = PageStore.queryParams.bd_vid;
    if (bdVid) {
        // return source || 'bd_ads';
        source = source || 'bd_ads'; // 更新在品专场景的source信息
    }

    // 剥离手百直播间分享场景
    if (shareFrom && !source) {
        source = shareFrom;
    }

    // （整体结构：url上自带source + 直播状态 + 浏览器）
    const browser = env.isWechat ? 'wechat' : 'other';
    const sourceSuffix = type === 'preview' ? `h5_preview_${browser}` : `liveh5_${browser}`;
    const finalSource = source ? `${source}_${sourceSuffix}` : sourceSuffix;
    if (
        schemeType === 'enterRoom'
        || env.isMainBox && versionCompare(env.boxVersion, '13.2') >= 0
    ) {
        return {
            livefrom: 'liveh5',
            livesource: finalSource,
            liveinvoke: '0',
        };
    }

    return finalSource;
};

/**
 * @name createConfig
 * @description 获取跳转NA时scheme所需的params参数
 * @param {Number/String} roomId 需要回流的直播间id
 * @param {Boolean/String/Number} [hasVideo]
 * @returns {Object} {}
 */
const createConfig = (roomId, hasVideo, schemeType, type) => {
    try {
        const reflowSchemeSource = getReflowSchemeSource(schemeType, type);
        const bdVid = PageStore.queryParams.bd_vid || '';
        const zb_tag = PageStore.queryParams.zb_tag || '';
        const shareUid = PageStore.queryParams.share_uid || '';
        // 粉丝团任务回流用的参数
        const shareTaskInfo = [
            'cUk',
            'bUk',
            'taskId',
            'fansGroupId',
            'activityType',
            'room_id',
            'shareSource',
            'lotteryActivityId',
            'shareExt'
        ].reduce((pre, cur) => {
            // tempValue为decode一次后的
            const tempValue = PageStore.queryParams[cur];
            if (cur === 'shareExt') {
                try {
                    pre[cur] = JSON.parse(tempValue);
                } catch (e) {
                    pre[cur] = tempValue;
                }
            } else {
                pre[cur] = tempValue;
            }
            return pre;
        }, {});
        const ext = shareUid
            ? {
                share_uid: shareUid,
                zb_tag: zb_tag,
            }
            : {
                source: reflowSchemeSource,
                bd_vid: bdVid,
            };
        let opts = {
            roomId,
            screen: '0',
            source: reflowSchemeSource,
            extLog: {
                bd_vid: bdVid,
            },
            ext: {
                ext: ext,
            },
            shareTaskInfo,
        };
        //  新增进入直播带货精彩片段schma字段
        let nId = PageStore.queryParams.nid || '';
        if (checkHasValue(nId)) {
            opts.hlReplay = {
                nid: nId,
            };
        }

        if (!hasVideo) {
            opts.screen = '0';
            opts.status = '0';
        }

        return opts;
    } catch (err) {
        weirwood.error.captureException(err);
        return {};
    }
};

/**
 * @name getThroughSchemeExtParams
 * @description 获取跳转NA时透传的ext_params字段
 * @returns {Object} {}
 */
export const getThroughSchemeExtParams = (extParamsStr) => {
    try {
        return JSON.parse(decodeURIComponent(extParamsStr));
    } catch (e) {
        weirwood.error.captureException(e);
        // console.log(e);
    }

    return false;
};

/**
 * @name getThroughSchemeParams
 * @description 获取跳转NA时需要透传的所有字段，url上通过携带scheme_params参数可以代替ext_params，因为scheme_params中包含ext_params
 * @returns {Object} {}
 */
export const getThroughSchemeParams = (schemeParamsStr) => {
    try {
        let throughSchemeParams = JSON.parse(decodeURIComponent(schemeParamsStr));
        let throughParams = JSON.parse(JSON.stringify(throughSchemeParams.params));
        delete throughSchemeParams.params;

        return {
            throughParams,
            throughOthers: throughSchemeParams,
        };
    } catch (e) {
        weirwood.error.captureException(e);
        // console.log(e);
    }

    return {};
};

/**
 * @name getSchemeParams
 * @description 返回跳转NA时scheme所需所有参数
 * 例如：baiduboxapp://live/enterStreamRoom?params={'roomId':'4600899323','source':'home'}&upgrade=1&source=1
 * 返回：{
 *  params: encodeURIComponent(JSON.stringify({'roomId':'4600899323','source':'home'}),
 *  upgrade: 1,
 *  source: 1
 * }
 * @param {Number/String} roomId 需要回流的直播间id
 * @param {Object} [outerParams] 需要合并的params参数，指scheme中的params对象
 * @param {Object} [outerOthers] 需要合并的其他参数，和scheme中params同级
 * @param {String} [type] 需要跳转的NA页的类型，liveRoom（直播间）/preview（预约页）
 * @param {Boolean/String/Number} [hasVideo] type = 'liveRoom'时用到
 * @returns {Object} {}
 */
const getSchemeParams = ({
    roomId,
    outerParams = {},
    outerOthers = {},
    type = 'liveRoom',
    hasVideo,
    schemeType,
}) => {
    try {
        const curRoomId = PageStore.queryParams.room_id || '';
        const isGetCurRoomScheme = +roomId === +curRoomId;

        // 处理scheme参数中的params对象
        let params = {};
        // 处理除params对象外的其他参数
        let others = {
            ...outerOthers,
        };

        if (isGetCurRoomScheme) {
            // 以下逻辑：获取当前roomid对应的scheme参数
            const opts = createConfig(roomId, hasVideo, schemeType, type);
            Object.assign(params, {
                ...opts,
                ...outerParams,
                roomId,
            });

            // 透传scheme_params
            const urlSchemeParams = PageStore.queryParams.scheme_params || '';
            if (urlSchemeParams) {
                // 透传scheme_params参数到scheme，可能会覆盖h5逻辑中生成的参数
                let { throughParams, throughOthers } = getThroughSchemeParams(urlSchemeParams);
                if (throughParams) {
                    Object.assign(params, throughParams);
                }
                if (throughOthers) {
                    Object.assign(others, throughOthers);
                }
            }

            // 透传ext_params参数
            const urlExtParams = PageStore.queryParams.ext_params || '';
            if (urlExtParams) {
                // 透传ext_params参数到scheme，可能会覆盖h5逻辑中生成的参数
                const extParams = getThroughSchemeExtParams(urlExtParams);
                if (extParams) {
                    params.extParams = extParams;
                }
            }

            // 代表是从直播间分享出来的，打开NA直播间需要携带参数
            if (PageStore.queryParams.share_from === 'na') {
                if (Object.prototype.toString.call(params.commonShareInfo) === '[object Object]') {
                    params.commonShareInfo.room_id = roomId;
                } else {
                    params.commonShareInfo = {
                        room_id: roomId,
                    };
                }
            }
        } else {
            // 以下逻辑：获取其他roomid对应的scheme参数
            const reflowSchemeSource = getReflowSchemeSource(schemeType, type);
            Object.assign(params, {
                roomId,
                source: reflowSchemeSource,
                ext: {
                    ext: {
                        source: reflowSchemeSource,
                    },
                },
                ...outerParams,
            });
        }

        // 删除params中空值
        for(let key in params) {
            if (params[key] === '' || params[key] === undefined || params[key] === null) {
                delete params[key];
            }
        }

        // 返回scheme所需所有参数
        const schemeParams = {
            ...others,
            params: encodeURIComponent(JSON.stringify(params)),
        };

        return schemeParams;
    } catch (err) {
        weirwood.error.captureException(err);
        return {
            params: encodeURIComponent(JSON.stringify({ roomId })),
        };
    }
}

export default getSchemeParams;
