/**
 * @File         : 异常监控
 * <AUTHOR> gaojiajun01
 * @Date         : 2022-06-09 16:04:30
 * @LastEditors  : gaojiajun01
 * @LastEditTime : 2024-06-21 14:27:47
*/

// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/WWW6pX-lL3/60incnF1Zd/vYuE1Dfm4TzD_D
import * as Weirwood from '@baidu/weirwood-sdk';
import WeirwoodOptions from '@/../weirwood.json';

const options = {
    common: {
        // 只支持如下字符：英文字母(大小写)，数字(0-9)，下划线(_)，中划线(-)，点(.)，@符号
        buildid: WeirwoodOptions.buildid,
        token: WeirwoodOptions.token,
        ignoreUrls: [
            // 本地开发时屏蔽发送，请按需修改
            'localhost',
            '127.0.0.1',
            /.*\.baidu\.com:\d+/
        ]
    },
    error: {
        collectWindowErrors: true,
        collectUnhandledRejections: true,
        // 静态资源加载异常
        collectResourceLoadErrors: true,
        ignoreErrors: [
            'websocket is closed'
        ]
    },
    perf: {
        // 抽取pv 50%上报
        sampleRate: 0.5,
        spa: true,
        history: true,
        ignoreXhrs: [
            '/ztbox',
            '/liveshowstatic',
            /.*\.baidu\.com:\d+/
        ],
        initiatorTypes: ['spa_hard', 'spa']

    }
};

// 兼容预渲染环境
const weirwood = window.isServer ? {
    error: {
        captureException() { }
    }
} : Weirwood.init(options);
export {weirwood};
