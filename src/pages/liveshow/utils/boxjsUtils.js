/**
 * @file 端能力交互封装
 * <AUTHOR>
 * @date 2021-11-22 22:00:17
 */

import device from '@baidu/boxx/device';
import account from '@baidu/boxx/account';
import event from '@baidu/boxx/event';
import cuid from '@baidu/xbox-native/device/cuid';
import easybrowse from '@baidu/xbox-native/view/easybrowse';
import {invokeP} from '@baidu/xbox-native/invoke';
import {Toast} from '@baidu/nano-react';
import {env} from '@/utils/env';
import {appProtoName, boxVersion, isAndroid, isBox, isIOS, versionCompare, UA} from '@baidu/boxx/env';
import outsideLogin from '@/utils/login';
import {getAppSchemeHead} from './getUrlParam';

// 更新协议头，兼容荣耀白盘
const appProtoHeader = env.isHonorBrowser ? 'bdhonorbrowser' : appProtoName;

const ADD_VIP_TAG_SCHEME = `${appProtoHeader}://v15/feed/tabopt?params=%7B%22upgrade%22%3A1%2C%22tab_id%22%3A%22129%22%2C%22action%22%3A%22save%22%2C%22tabinfo%22%3A%7B%22id%22%3A%22129%22%2C%22name%22%3A%22VIP%22%2C%22canDelete%22%3A%221%22%2C%22canDegrade%22%3A%220%22%2C%22canTTS%22%3A%220%22%2C%22newTip%22%3A%7B%22tip%22%3A%220%22%2C%22start%22%3A%220%22%2C%22end%22%3A%220%22%7D%2C%22rnInfo%22%3A%7B%22bundleId%22%3A%22box.rnplugin.feedhn%22%2C%22moduleName%22%3A%22FeedHN%22%2C%22bundleVersion%22%3A%227%22%7D%7D%7D`;

const BOOK_LIST_SCHEME = `${appProtoHeader}://v1/easybrowse/open?type=tags&url=https%3a%2f%2fmbd.baidu.com%2fnewspage%2fdata%2fmdpage%3ftag%3d31%26method%3dSubscribeList%26getTPL%3d1&append=0&newbrowser=0&forbidautorotate=1&upgrade=1`;

// 平台信息
const platformInfo = {
    isIOS,
    isAndroid,
    isBox: isBox || appProtoName === 'baiduboxvision',
    boxVersion,
    isKanKan: appProtoName === 'baiduboxvision',
    isLiteBox: / ((lite|info) baiduboxapp\/)|baiduboxvision\//i.test(navigator.userAgent.toLowerCase()),
    isHarmonyBox: / baiduboxapp\//i.test(UA) && /ArkWeb\//i.test(UA)
};

/**
 * schema跳转, 安卓,ios都好使, 安卓8.4以上
 *
 * @param {Object} params 组合参数{schema: 拼接的schema}
 * @return {Promise} 调用的返回
 */
const invokeSchema = (params = {}) => {
    const {schema = ''} = params;
    return new Promise((res, rej) => {
        if (!schema) {
            rej('schema should not be empty');
            return;
        }
        let $node = document.createElement('iframe');
        $node.style.display = 'none';
        $node.src = schema;
        const body = document.body || document.getElementsByTagName('body')[0];
        body.appendChild($node);
        setTimeout(() => {
            body.removeChild($node);
            $node = null;
        }, 0);
    });
};

const openEasyBrowse = (url, browserType) => {
    const newbrowser = browserType === 0 ? browserType : 1;
    easybrowse({
        name: 'easybrowse',
        type: 'h5',
        newbrowser: newbrowser,
        data: {
            url
        }
    });
};

/**
 * 轻框沉浸式
 * backlocation：返回按钮位置0:上；1:下
 * @return {Promise} 调用的返回
 */
const openImmerseBrowser = (url) => {
    let scheme = '';
    let objString = null;
    scheme = env.isLiteBox ? 'baiduboxlite://v1/easybrowse/open?' : 'baiduboxapp://v1/easybrowse/open?';
    objString = format({
        type: 'immerse', // 用沉浸式框架打开，保证体验一致
        append: 0,
        style: '',
        url,
        backlocation: 1,
        newbrowser: 1,
        forbidautorotate: 1
    });
    invokeSchema({
        schema: scheme + objString
    });
};

function format(obj) {
    let str = '';
    Object.keys(obj).forEach(item => {
        if (typeof obj[item] === 'object') {
            str += `${item}=${encodeURIComponent(JSON.stringify(obj[item]))}&`;
        }
        else {
            str += `${item}=${encodeURIComponent(obj[item])}&`;
        }
    });
    return str.slice(0, -1);
}

/**
 * 获得设备网络信息
 * @return {Promise} 调用的返回
 */
const getNetInfo = () => {
    return new Promise((resolve) => {
        if (platformInfo.isBox) {
            device.getNetworkType({
                success(res) {
                    if (res.status === 0) {
                        resolve(res.data.networkType);
                    }
                }
            });
        }
        else {
            resolve('');
        }
    });
};

/**
 * 通用toast
 */
const showToast = (msg = '') => {
    return !!msg && Toast(msg);
};

/**
 * 获得设备硬件cuid信息
 * @return {Promise} 调用的返回
 */
const getCuid = () => {
    return new Promise((resolve) => {
        // isHarmonyBox
        if (window?.Bdbox_harmonyos_jsbridge?.dispatch) {
            const callbackName = `_box_cuid_callback_${Date.now()}`;
            window[callbackName] = function (res) {
                resolve(res.unique_id);
            };
            window.Bdbox_harmonyos_jsbridge.dispatch(`baiduboxapp://utils/getCuid?callback=${callbackName}`);
        }
        else if (platformInfo.isBox) {
            try {
                cuid().then(res => {
                    resolve(res.unique_id || res.data);
                }).catch(err => {
                    resolve('');
                });
            }
            catch (e) {
                resolve('');
            }
        }
        else {
            resolve('');
        }
    });
};

const getAuthorUrl = (cmdData) => {
    let {id, isUgc} = cmdData;
    const isHaokanApp = getAppSchemeHead() === 'baiduhaokan';
    if (isHaokanApp) {
        return `https://haokan.baidu.com/author/${id}`;
    }
    let url = 'https://author.baidu.com/home?context=';
    let data = {
        clicktime: +new Date(),
        from: 'live_h5'
    };
    let key = 'app_id';
    if (isUgc) {
        key = 'uk';
    }
    data[key] = id;
    let params = JSON.stringify(data);
    return url + params;
};

/**
 * 打开个人主页
 * @param {String/Number} thirdId thirdId
 * @param {Boolean} isUgc 是否ugc
 */
const toAuthorPage = (thirdId, isUgc = false) => {
    const h5Url = getAuthorUrl({id: thirdId, isUgc});
    if (versionCompare(platformInfo.boxVersion, '9.0') >= 0 || platformInfo.isLiteBox || platformInfo.isKanKan) {
        invokeP('v1/easybrowse/hybrid', {
            upgrade: 1,
            type: 'hybrid',
            tplpath: 'profile',
            tpl_id: 'profile.html',
            context: {
                app_id: thirdId,
                clicktime: +new Date(),
                from: 'live_h5',
                tab: 'main' // 无跳专栏的硬性要求
            },
            style: {
                toolbaricons: {
                    toolids: ['3']
                },
                menumode: '2'
            },
            slog: {
                from: 'live_h5'
            },
            newbrowser: 1,
            backup: {
                url: h5Url,
                version: ''
            }
        });
    }
    else {
        window.location.href = h5Url;
    }
};

let bookShelfCallbackId = 0;
// http://es.baidu-int.com/#/apidetail/958
const invokeBookshelf = (shelf_info = {}) => {
    return new Promise((resolve, reject) => {
        const params = {
            item: {
                data: {
                    own_type: shelf_info.data.own_type,
                    read_time: shelf_info.data.read_time,
                    source: shelf_info.data.source,
                    last: shelf_info.data.last
                },
                type: shelf_info.type,
                tplid: shelf_info.tplid,
                third_id: shelf_info.third_id,
                time: shelf_info.time
            },
            ubc: {
                src: 'feed',
                page: 'feedimg_page',
                from: 'feedimg'
            }
        };
        const callbackName = `_bookshelf_callback_${bookShelfCallbackId++}`;
        const schema = `${appProtoHeader}://paywall/addToPayWall?params=${encodeURIComponent(
            JSON.stringify(params)
        )}&callback=${callbackName}`;

        const timeout = setTimeout(() => {
            reject('网络不给力，请稍后再试');
        }, 5000);

        window[callbackName] = (res) => {
            res = JSON.parse(res);
            clearTimeout(timeout);
            if (res && +res.status === 0 && res.data && +res.data.succ === 1) {
                resolve(res);
            }
            else {
                reject(res && res.data && res.data.error);
            }
            delete window[callbackName];
        };

        invokeSchema({schema});
    });
};

const purchase = ({id = '', type = ''}) => {
    invokeSchema({
        schema: `${appProtoHeader}://feed/commonPayment/pay?params=${encodeURIComponent(
            JSON.stringify({
                type,
                resId: id,
                ext: {
                    source: 'live_preview'
                }
            })
        )}`
    });
};

// 调用登录端能力
const login = () => {
    return new Promise((resolve) => {
        if (platformInfo.isBox) {
            account.login({
                loginType: 'username',
                showThirdLogin: '1',
                loginSource: '1001',
                normalizeAccount: '0',
                success: () => {
                    resolve();
                }
            });
        }
        else {
            outsideLogin({
                resolve: true
            }).then(resolve);
        }
    });
};

let systemNotifyCallbackId = 0;
// 调起系统通知端能力
const systemNotify = (source) => {
    return new Promise((resolve, reject) => {
        const callbackName = `__system_notify_${systemNotifyCallbackId++}`;
        const paramsString = encodeURIComponent(JSON.stringify({source}));

        setTimeout(() => {
            reject({});
        }, 5000);
        window[callbackName] = (result) => {
            delete window[callbackName];
            let res = {};
            try {
                res = JSON.parse(result);
            }
            catch (e) {
                res = {};
            }
            if (+res.status === 0) {
                resolve({res});
            }
            else {
                reject({res});
            }
        };

        invokeSchema({
            schema: `${appProtoHeader}://v35/message/openGuidePush?params=${paramsString}&callback=${callbackName}`
        });
    });
};

const closeCallbackId = 0;
// 关闭当前页面
const closeWindow = () => {
    return new Promise((resolve) => {
        try {
            const callbackName = `__close_callback_${closeCallbackId}`;
            window[callbackName] = (res) => {
                resolve(res);
            };
            window.setTimeout(() => {
                resolve();
            }, 3000);
            const schema = `${appProtoHeader}://v11/browser/closeWindow?callback=${callbackName}`;
            invokeSchema({schema});
        }
        catch (e) {
            resolve();
        }
    });
};

// 2021.8.17 兼容 12.5 ios 版本找不到 event方法bug
if (!event.on) {
    event.on = () => { };
}
if (!event.off) {
    event.off = () => { };
}

export default {
    platformInfo,
    event,
    ADD_VIP_TAG_SCHEME,
    BOOK_LIST_SCHEME,
    invokeSchema,
    openEasyBrowse,
    getNetInfo,
    showToast,
    getCuid,
    toAuthorPage,
    invokeBookshelf,
    purchase,
    login,
    systemNotify,
    closeWindow,
    openImmerseBrowser
};
