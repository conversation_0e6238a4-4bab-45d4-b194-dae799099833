/**
 * @file 跳转NA相关封装
 * <AUTHOR>
 * @date 2022-4-13 15:00:17
 */
import {versionCompare} from '@baidu/xbox-native/detect';
import {appProtoName} from '@baidu/boxx/env';
import {Toast as toast} from '@baidu/nano-react';
import {env} from '@/utils/env';
import {PageStore} from '@/pages/liveshow/service/register';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import {isAppRedirectBlocked} from '../components/AdLiveShow/utils/app';
import {getAppName, jsonToQuery} from './getUrlParam';
import getSchemeParams from './getSchemeParams';

const schemePathList = {
    'enterStreamRoom': '://live/enterStreamRoom',
    'enterRoom': '://live/enterRoom'
};

/**
 * @name closeAndGoNativePre
 * @description【仅百度内】调起NA预约页，baiduboxapp://live/enterStreamRoom?params={'roomId':'4600899323','invokePop':{'pop_name':'pay_live_payment_plugin'},'source':'home'}
 * @param {Number/String} roomId 需要打开的直播间id，必填
 * @param {Object} params 参数
 */
export const closeAndGoNativePre = (roomId, params = {}) => {
    let schemeParams = getSchemeParams({
        roomId,
        type: 'preview',
        outerParams: {
            ...params
        }
    });

    boxjsUtils.closeWindow().then(() => {
        goNativePage('enterStreamRoom', schemeParams);
    });
};

/**
 * @name goNativeLiveRoom
 * @description【仅百度内】调起NA直播间
 * @param {Number/String} roomId 需要打开的直播间id，必填
 * @param {Boolean/String/Number} [hasVideo]
 * @param {Object} tiebaCmd 贴吧url
 * @param {Boolean} isPaid 是否是购买
 */
export const goNativeLiveRoom = ({
    roomId,
    hasVideo,
    tiebaCmd,
    isPaid,
    outerSchemeParams,
    trans_type
}, isFromList = false) => {
    // ! 商业H5特殊逻辑：**手百内**有is_jump_app=0时不跳转NA
    if (isAppRedirectBlocked() && env.isBox) {
        return;
    }
    if (tiebaCmd) {
        if (versionCompare(env.boxVersion, '10.12') >= 0) {
            location.href = tiebaCmd;
        }
        else {
            toast(`请升级${getAppName()}app`);
        }
        return;
    }

    let isUpper11255 = versionCompare(env.boxVersion, '11.25.5') >= 0;
    let schemeParams = getSchemeParams({
        trans_type,
        roomId,
        hasVideo,
        outerOthers: {
            upgrade: 0
        },
        outerParams: outerSchemeParams,
        schemeType: isUpper11255
            ? 'enterStreamRoom'
            : 'enterRoom'
    });
    // 兼容荣耀浏览器自动调NA
    if (env.isHonorBrowser || (env.isAndroid && env.isLiteBox)) {
        goNativePage('enterStreamRoom', schemeParams);
        boxjsUtils.closeWindow().then(() => {
            // 在荣耀白牌搜索视频页 无正常回调 导致跳转NA失败
        });
        return;
    }

    if (isPaid) {
        if (versionCompare(env.boxVersion, '11.22') >= 0) {
            goNativePage(isUpper11255 ? 'enterStreamRoom' : 'enterRoom', schemeParams, isFromList);
        }
        else {
            toast(`该版本不支持购买，请升级${getAppName()}app`);
        }
        return;
    }

    // 切片 NA直播间12.18及以上才支持
    if (PageStore.queryParams.nid && versionCompare(env.boxVersion, '12.18') < 0) {
        return;
    }

    if (versionCompare(env.boxVersion, '9.3') >= 0) {
        goNativePage(isUpper11255 ? 'enterStreamRoom' : 'enterRoom', schemeParams, isFromList);
        return;
    }
    window.location.href = 'https://mo.baidu.com/mo/home?from=1019151i';
};


/**
 * @name goNativePage
 * @description 执行调起NA页
 * @param {String} type scheme路径，参考schemePathList
 * @param {Object} schemeParams scheme参数
 * @param {String} [schemeHead] scheme头
 */
const goNativePage = (type, schemeParams, isFromList = false, schemeHead = appProtoName) => {
    if (!schemePathList[type]) {
        return;
    }

    // 百度内调起NA直播间scheme，直接跳转接口返回scheme (预约 / 非预约), 非列表项跳转（计划后期支持）
    // if (type === 'enterStreamRoom'
    // && PageStore.schemaInfo
    // && PageStore.schemaInfo.enter_cmd
    // && !isFromList) {
    // boxjsUtils.invokeSchema({schema: PageStore.schemaInfo.enter_cmd});
    // }
    // else {
    //     const schemeParamsStr = schemeParams ? ('?' + jsonToQuery(schemeParams)) : '';
    //     const schema = `${schemeHead}${schemePathList[type]}${schemeParamsStr}`;
    //     console.log({schema}, 'schemaschema2');
    // boxjsUtils.invokeSchema({schema});
    // }
    try {
        const schemeParamsStr = schemeParams ? ('?' + jsonToQuery(schemeParams)) : '';
        const schema = `${schemeHead}${schemePathList[type]}${schemeParamsStr}`;
        boxjsUtils.invokeSchema({schema});
    }
    catch (error) {
        return;
    }
};

export default goNativePage;
