/**
 * @file 抽离预约页接入MCP调起逻辑
 * （小春晚、黄山、小经典2.0)
 * 截止目前已接入:
 * 主板、极速、好看、畅听 四端，
 * 同时还兼顾 ext_sid 矩阵调起渠道，
 * 如: 贴吧、地图、网盘调起手百
 * https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/kVI24OE6uV/WnHOIjVElQ/QO8EPcgX78-VM_
 *
 * <AUTHOR>
 */

import {env} from '@/utils/env';
import {PageStore} from '@/pages/liveshow/service/register';
import {getAppSchemeHead} from '@/pages/liveshow/utils/getUrlParam';
import {matrixAppSchemeHeaderList, refolwForConfig} from '@/pages/liveshow/utils/reflow';
import {execInvoke, getInvokeInfo, setCommonParams, createMcpWxTag} from '@baidu/mcp-sdk';

const APP_NAME_SCENE = {
    'baiduboxapp': 'sharepage',
    'lite': 'sharepagelite',
    'haokan': 'sharepagehaokan', // 新增scene
    'tomas': 'sharepagetomas', // 新增scene
    '': 'sharepage'
};

// 调起位
const posKey = 'pos_part';

// url上原始source
const decodeSource = decodeURIComponent(PageStore.queryParams.source || '');

// 如果从矩阵端内分享出来会通过|拼接
const source = decodeSource.includes('|') ? decodeSource.split('|')[0] : decodeSource;

// 端内协议头
const appSchemeHead = getAppSchemeHead();

// 回流矩阵场景
const isMatrixApp = matrixAppSchemeHeaderList.includes(appSchemeHead);

// 仅在live.baidu.com 域名下安卓回手百新增微信开放标签
const isLiveDomain = location.host === 'live.baidu.com';

let invokeInfo = {}; // mcp调起信息

// 获取MCP信息
export const getMcpData = async (schema, btnClassName = 'main-btn-jd') => {
    if (!schema) {
        return;
    }

    // 设置通用调起信息
    setCommonParams({
        app: 'wise',
        scene: APP_NAME_SCENE[source] || 'sharepage',
        ext_sid: decodeSource // 如：贴吧、网盘、地图 回流手百
    });

    // 获取调起位详细信息
    const info = await getInvokeInfo({
        'invoke_info': {
            [posKey]: [{
                'share_scheme': schema
            }]
        }
    });
    invokeInfo = info;

    window.console.log('invokeInfo111', invokeInfo);
    // 存在调起信息 && 安卓 && 微信 && 回流主板 新增微信开放标签校验 && isLiveDomain
    if (!isMatrixApp && env.isAndroid && env.isWechat && info.action_rule && info) {
        createWxTags(info, btnClassName);
    }

    return invokeInfo;
};

// 创建微信开放标签
export const createWxTags = (info, btnClassName) => {
    const {action_rule: actionRule, log_id: logId} = info;
    const liveShareInfo = actionRule[posKey][0];
    const shareContainer = document.querySelector(`.${btnClassName}`);
    window.console.log('liveShareInfo', liveShareInfo);
    window.console.log('shareContainer', shareContainer);
    if (shareContainer && liveShareInfo) {
        createMcpWxTag({targetDom: shareContainer}, posKey, liveShareInfo, logId);
    }
};

 // mcp 调起
export const invokeApp = async (new_visual, invokeInfo, pos = 'pos_part') => {
    const invokeConfig = {
        appName: appSchemeHead,
        toStore: true,
        scheme: new_visual.enter_cmd,
        token: new_visual.share_token
    };
    const invokeInfoForPos = invokeInfo && invokeInfo.action_rule && invokeInfo.action_rule[pos];
    window.console.log('invokeInfoForPos', invokeInfoForPos);
    if (!invokeInfoForPos || !Array.isArray(invokeInfoForPos) || !invokeInfoForPos[0]) {
        window.console.log('fail111');
        // 执行默认调起 兼容接口返回异常情况
        refolwForConfig(invokeConfig);
        return;
    }

    try {
        window.console.log('suc111');
        const res = await execInvoke(
            pos,
            invokeInfoForPos[0],
            invokeInfoForPos[0].log_id
        );
        window.console.log('suc111');
    }
    catch (e) {
        window.console.log('fail222');
        // 执行默认调起行为
        refolwForConfig(invokeConfig);
    }
};



