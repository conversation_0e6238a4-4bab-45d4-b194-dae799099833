/**
 * @file 处理xss与emoji替换
 * <AUTHOR> <<EMAIL>>
 */

import xss from 'xss';
// import emoticon from './lib/emoticon.iife';
import {fuzzyReplace} from '@baidu/xbox-emoticon';

// const {replace}  = emoticon({});

const filterXss = new xss.FilterXSS({
    whiteList: {},
});

export default text => {
    let str = filterXss.process(text);
    try {
        str = fuzzyReplace(str);
    // eslint-disable-next-line no-empty
    } catch (error) {}
    return str;
};
