/**
 * @file 分享相关初始化设置
 * <AUTHOR>
 * @date 2020-12-11 16:02:55
 */
import {TemplateEnum} from '@/pages/liveshow/config/const';
import {getAppName} from './getUrlParam';
import {getStartTimeStr} from './time';

// const icon = 'http://m.baidu.com/static/search/logo300.png';
const icon = 'https://b.bdstatic.com/searchbox/icms/searchbox/img/po/act/newuserredpack/box_logo.png';
const appName = getAppName();

const execWxShare = (info, wxInfo) => {
    const type = info.template;

    const serverTitle = (info && info.share && info.share.main_title) || '';
    const serverDesc = (info && info.share && info.share.sub_title) || '';
    const serverCover = (info && info.share && info.share.cover) || '';

    // 微信二次分享 更新为Server下发信息
    let opts = {
        title: serverTitle || info.pageTitle || `${appName}App视频直播`,
        desc: serverDesc || '精彩直播正在进行，邀请你速来围观',
        link: location.href,
        imgUrl: serverCover || info.hostAvatar || icon,
        success: function () {
            // opt?.success?.();
        },
        cancel: function () {
            // opt?.cancel?.();
        }
    };
    // 预约态更新分享信息
    if (type === TemplateEnum.Preview) {
        opts = {
            title: serverTitle || `'【直播预告】'${info.title}`,
            desc: serverDesc || `${getStartTimeStr(info.start_time)}开播`,
            link: location.href,
            imgUrl: serverCover || info.hostAvatar || icon,
            success: function () {
                // opt?.success?.();
            },
            cancel: function () {
                // opt?.cancel?.();
            }
        };
    }

    // 注入数据
    wx.config({
        debug: location.search.indexOf('wx_debug=1') > 0 || false,
        appId: wxInfo.appId,
        timestamp: wxInfo.timestamp,
        nonceStr: wxInfo.nonceStr,
        signature: wxInfo.signature,
        jsApiList: [
            'onMenuShareWeibo',
            'onMenuShareTimeline', // 分享到朋友圈
            'onMenuShareAppMessage', // 分享给好友
            'onMenuShareQZone', // 分享到QQ空间
            'onMenuShareQQ', // 分享到QQ好友
            'updateAppMessageShareData', // 新版分享给好友
            'updateTimelineShareData' // 新版分享朋友圈
        ]
    });

    // 需在用户可能点击分享按钮前就先调用
    wx.ready(function () {
        // 新版分享给好友
        wx.updateAppMessageShareData(opts);
        // 新版分享朋友圈
        wx.updateTimelineShareData(opts);
        // 分享到朋友圈
        wx.onMenuShareTimeline(opts);
        // 分享给好友
        wx.onMenuShareAppMessage(opts);
        // 分享到QQ好友
        wx.onMenuShareQQ(opts);
        wx.onMenuShareWeibo(opts);
        // 分享到QQ空间
        wx.onMenuShareQZone(opts);
    });
};

export const iniShare = async (info, wxInfo) => {
    if (typeof wx !== 'object') {
        const scriptTag = document.createElement('script');
        scriptTag.setAttribute('src', '//res.wx.qq.com/open/js/jweixin-1.6.0.js');
        scriptTag.setAttribute('type', 'text/javascript');
        scriptTag.onload = () => {
            execWxShare(info, wxInfo);
        };
        document.getElementsByTagName('head')[0].appendChild(scriptTag);
    }
    else {
        console.log('[live] 检测到微信JS-SDK已被引入，请确保SDK版本大于等于1.6.0');
        execWxShare(info, wxInfo);
    }
};
