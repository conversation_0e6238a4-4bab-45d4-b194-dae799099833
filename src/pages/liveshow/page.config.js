/**
 * @file 页面配置, 脚手架@baidu/rmb生成
 * 文档：http://agroup.baidu.com/video-fe/md/article/1751799
 */


module.exports = [
    {
        name: 'index',
        title: '直播',
        renderType: 'html',
        hmSiteId: '4d51fad919d22a5ab3f2e65e326e28c9',
        template: 'src/templates/live.ejs',
        headfunc: require('./service/tmpInit-prod'),
        whiteScreen: {
            name: 'liveShareShow',
            selector: '.white-screen-patrol',
            subSelector: 'div',
            sample: 0.5
        },
        debugger: true,
        params: {
            inviterId: {
                require: false,
                desc: '邀请者标识',
                value: ''
            },
            room_id: {
                require: true,
                desc: '房间id',
                value: ''
            },
            source: {
                require: false,
                desc: '标识来源',
                value: '' // 付费拼团 是groupbuy_preview
            },
            series_nid: {
                require: false,
                desc: '系列课id',
                value: '' // 付费直播系列课id
            },
            start: {
                require: false,
                desc: '视频切片 视频开始时间',
                value: ''
            },
            end: {
                require: false,
                desc: '视频切片 视频结束时间',
                value: ''
            },
            nojump: {
                require: false,
                desc: '直播预告使用，控制预约页端内打开是否自动跳转到NA',
                value: '' // 0、1
            },
            muted: {
                require: false,
                desc: '直播预告使用，控制预约页有视频时视频播放是否静音（仅端内有效）',
                value: '' // 0、1
            },
            // 透传scheme中params的extParams参数，参考https://console.cloud.baidu-int.com/devops/icafe/issue/zhibo00-9110/show
            ext_params: {
                require: false,
                desc: '需要透传的scheme参数 - 针对手百scheme',
                value: ''
            },
            // 透传scheme中任意参数，可以用此参数代替ext_params，参考https://console.cloud.baidu-int.com/devops/icafe/issue/zhibo00-9208/show
            scheme_params: {
                require: false,
                desc: '需要透传的端scheme参数 - 针对手百scheme',
                value: ''
            },
            cUk: {
                require: false,
                desc: '用户uk，电商做任务用',
                value: ''
            },
            bUk: {
                require: false,
                desc: '主播uk，电商做任务用',
                value: ''
            },
            taskId: {
                require: false,
                desc: '任务id，电商做任务用',
                value: ''
            },
            fansGroupId: {
                require: false,
                desc: '粉丝团id，电商做任务用',
                value: ''
            },
            activityType: {
                require: false,
                desc: '活动类型，电商做任务用',
                value: ''
            },
            lotteryActivityId: {
                require: false,
                desc: '抽奖活动id？电商做任务用',
                value: ''
            },
            shareExt: {
                require: false,
                desc: '电商做任务用，encode后的json字符串，可以存放任意数据',
                value: ''
            },
            share_from: {
                require: false,
                desc: '从NA直播间内分享出去，有此参数时，回流直播间需要携带commonShareInfo',
                value: ''
            },
            auto_play: {
                require: false,
                desc: '是否自动播放',
                value: ''
            },
            template_type: {
                require: false,
                desc: '展示样式。0默认样式，1代表仅展示video（百度地图内）', // 如有其他样式可扩展
                value: ''
            }
        }
    }
];
