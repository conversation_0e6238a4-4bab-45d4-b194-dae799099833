/**
 * @file 页面初始模块注入入口
 * <AUTHOR>
 * @date 2021-11-09 20:37:48
 */

import Cookies from 'js-cookie';
import md5 from 'md5';
import {randomString} from '@/utils/utils';
import SettingItem from '@/utils/db';
import reportH5Event from '@/pages/liveshow/utils/reportH5Event';
import {setPageMode} from '@/pages/liveshow/utils/pageMode';
import {iniShare} from '@/pages/liveshow/utils/share';
import initDataChannel from '@/pages/liveshow/utils/dataChannel';
import {TemplateEnum} from '@/pages/liveshow/config/const';
import {initLogInfo} from '@/pages/liveshow/utils/thunder';
import {getUrlParam, getUrlAllParam} from '@/pages/liveshow/utils/getUrlParam';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import {isLiveDomain} from '@/utils/index';
import {getInvokeInfo, setCommonParams} from '@baidu/mcp-sdk';

const getCuid = () => {
    const BDUID = 'BIDUPSID';
    const key = 'deviceId';
    let db = new SettingItem(key);
    let deviceId = db.get();

    if (!deviceId) {
        deviceId = Cookies.get(BDUID) || randomString(32);
        deviceId = md5(deviceId);
        db.set(deviceId);
    }

    return deviceId;
};

// 这里储存一些不会响应页面的数据
export const PageStore = {
    screen: 0,
    roomId: getUrlParam('room_id') || getUrlParam('roomid'),
    queryParams: getUrlAllParam(), // 挂载所有页面url参数
    deviceId: getCuid(),
    source: getUrlParam('source'),
    share_ecid: getUrlParam('share_ecid'), // 新增加密cuid，用于电商直播间防作弊
    schemaInfo: {}
};

const updateFontSize = (info) => {
    const doc = document.documentElement;
    let size = '24.1546vw';

    if (+info.visual_id === 6) {
        doc.style.fontSize = doc.clientWidth >= 500 ? '100px' : size;
        return;
    }

    if (info.template === TemplateEnum.Preview) {
        size = doc.clientWidth <= 360 ? '90px' : '100px';
    }
    doc.style.fontSize = size;
};
const getMcpData = async () => {
    // 设置通用调起信息
    setCommonParams({
        app: 'wise',
        scene: 'sharepage',
        ext_sid: ''
    });

    // 获取调起位详细信息
    try {
        const info = await getInvokeInfo({});
        return info?.global_conf || {};
    }
    catch (error) {
        console.log(error);
    }
};

// 依赖页面模板数据的模块
export const registerTemplateInit = async (info) => {
    // 根据不同模板确定不同fontSize
    updateFontSize(info);

    try {
        // 请求接口 获取微信校验信息
        if (isLiveDomain) {
            const wxInfo = await getMcpData();
            window.console.log(wxInfo);
            // 分享模块
            iniShare(info, wxInfo);
        // const res = await getWxConfig();
        // if (+res.errno === 0
        //     && res.data
        //     && res.data.appId
        //     && res.data.appId === 'wxe4b4fae2efdaad19'
        // ) {
        //     const wxInfo = res.data;
        //     window.console.log(wxInfo);
        //     // 分享模块
        //     iniShare(info, wxInfo);
        // }
        }

    }
    catch (error) {
        console.log(error);
    }
    // 监听回调
    initDataChannel(info);

    // 初始化统计模板
    initLogInfo(info);
};

// 初始化无状态全局模块
export const registerPageInit = (info) => {
    // 端内暗黑模式
    setPageMode();

    // 在线人数上报
    reportH5Event();

    // 如果端内能拿到cuid更新一下
    boxjsUtils.getCuid().then((cuid) => {
        if (cuid) {
            PageStore.deviceId = cuid;
            PageStore.cuid = cuid;

            const db = new SettingItem('deviceId');
            db.set(cuid);
        }
    });
};

// 荣耀白牌
export const isFromHonorBrowser = getUrlParam('source') === 'bdhonorbrowser';
