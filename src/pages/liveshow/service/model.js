/**
 * @file 承接接口做数据处理返回视图
 * <AUTHOR>
 * @date 2021-11-10 20:01:30
 */
import {env, isLargerVersion} from '@/utils/env';
import {wuXiaShowLog} from '@/pages/liveshow/log';
import vSpeed from '@baidu/mvideo-vspeed';
import blackList from '@/pages/liveshow/config/blackList';
import {getFollowStatus} from '@/pages/liveshow/utils/follow';
import {openReflowByUrl, matrixAppSchemeHeaderList} from '@/pages/liveshow/utils/reflow';
import {closeAndGoNativePre, goNativeLiveRoom} from '@/pages/liveshow/utils/openNativePage';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import {replaceProtocol, getAppSchemeHead} from '@/pages/liveshow/utils/getUrlParam';
import {
    ClubhouseUrl,
    FollowEnum,
    ForbiddenUrl,
    GroupBuyUrl,
    LiveStatus,
    PageErrorStatus,
    TemplateEnum,
    LiveStatusByScheme
} from '@/pages/liveshow/config/const';

import {adCommonShowLog} from '../components/AdLiveShow/monitor/ad';
import {isHonorWhiteLabelBrowser} from '../components/AdLiveShow/utils/ua';
import {requestRoomEnterInfoWithAntiCheats} from '../components/AdLiveShow/AnyDoor/api';
import {PageStore, isFromHonorBrowser} from './register';
import {getMainData, getMoreRecommand} from './api';

const appSchemeHead = getAppSchemeHead();
const isMatrixApp = matrixAppSchemeHeaderList.includes(appSchemeHead);

const adAutoJumpToHonorNA = () => window.globalJumpTestValue === 1 && isHonorWhiteLabelBrowser();
const autoJumpToNA = () => (env.isHonorBrowser && adAutoJumpToHonorNA()) || (env.isLiteBox && env.isAndroid);


function autoJumpToBaiduNA() {
    // const isInTest = window.globalJumpTestValue === 1;
    // 手百内，并且实验内，不跳转
    if (env.isMainBox) {
        return false;
    }
    // 否则走之前逻辑
    return env.isMainBox;
}

export const moreRecommand = () => {
    return getMoreRecommand().then((res) => {
        // res = recommandList.data['229'];
        if (res && res.list) {
            let list = res.list
                .filter((item) => item.status === '直播')
                .map((item) => {
                    // 处理需要携带的scheme参数（启播优化）
                    item.schemeParams = getLiveSchemeParams({
                        screen: item.screen,
                        templateId: item.template_id,
                        status: item.live_status,
                        roomType: item.room_type,
                        playUrl: item.play_url,
                        avcUrl: item.avc_url,
                        hevcUrl: item.hevc_url,
                        rtcUrl: item.rtc,
                        kabr_spts: item.kabr_spts
                    });

                    return {
                        title: item.title,
                        count: item.user_count,
                        roomId: item.room_id,
                        imgSrc: item.pic,
                        name: item.host_name,
                        anchorPhoto: item.anchor_photo,
                        displayUserCount: item.display_user_count,
                        liveStatus: item.live_status,
                        tag: item.tag,
                        schemeParams: item.schemeParams,
                        schema_info: item?.schema_info
                    };
                });
            return list;
        }
        return [];

    });
};

// 处理需要携带的scheme参数
const getLiveSchemeParams = ({
    screen,
    templateId,
    status,
    roomType,
    playUrl = '',
    avcUrl = '',
    hevcUrl = '',
    rtcUrl = '',
    kabr_spts = ''
}) => {
    return {
        // 视频格式 1竖屏 2横屏
        template: String(+screen + 1),
        // 直播间模板类型 0通用模板 1媒体 2电商 3咨询
        templateId: String(templateId),
        // 直播间状态 0直播状态 2关闭状态等待回放生成 3关闭且有回放 4预告 20直播间终结态
        status: LiveStatusByScheme[String(status)],
        // 直播类型 0媒体 4语音房 3YY
        roomType: String(roomType),
        screen: String(screen),
        playUrl,
        avcUrl,
        hevcUrl,
        rtcUrl,
        kabr_spts
    };
};

const isSureType = (data, key) => {
    if (key) {
        return data && data[key] && +data[key] === 1;
    }
    return data && +data === 1;

};

const liveforTabs = (data, newState) => {
    let roomSwithInfo = data.room_switch_info;
    try {
        roomSwithInfo = JSON.parse(roomSwithInfo);
    }
    catch (e) {
        roomSwithInfo = {descriptionTab: 1};
    }
    let merge = {
        showChatTab: !!+data.has_chat_room,
        showMoreTab: !!+data.slide_status && !env.isTomas, // 替换为slide_status判断是否展示推荐信息
        showProfileTab: !!(data.video && data.video.description) && !!+roomSwithInfo.descriptionTab
    };
    let showTabs = Object.keys(merge).some((key) => merge[key]);
    if (!showTabs) {
        merge.showProfileTab = true;
    }
    Object.assign(newState, merge);
};

const liveforHost = (data, newState) => {
    if (data.host) {
        const titleText = isFromHonorBrowser ? '的直播' : '的百度直播';
        let {is_fans_anonymous, is_self} = data.host;
        let hide = is_self ? false : is_fans_anonymous;
        let avatar = (data.host.image && data.host.image.image_33) || '';
        let nickName = data.host.nick_name || '';
        let merge = {
            hideName: !!hide,
            hostName: nickName,
            fans: data.host.fans || 0,
            hostAvatar: avatar,
            isShowWatchingNum: blackList.includes && !blackList.includes(nickName),
            followStatus: +data.follow ? FollowEnum.FOLLOWED : FollowEnum.UNFOLLOW,
            followType: data.follow_type,
            followId: data.follow_id
        };
        Object.assign(newState, merge);

        // 设置页面title 荣耀白牌title部分不显示 百度 字样
        if (nickName === '百度直播') {
            document.title = nickName;
        }
        else if (nickName.length > 5) {
            document.title = nickName.slice(0, 5) + '...' + titleText;
        }
        else {
            document.title = nickName + titleText;
        }
    }
};

const liveforPaidAndAdPlace = (data, newState) => {
    // 付费信息
    if (newState.isPaid && data.pay_info) {
        let payInfo = data.pay_info;
        Object.assign(newState, {
            // 是否为付费拼团
            isGroupPaid: isSureType(data.pay_info, 'is_group_buy'),
            isDiscount: !!+payInfo.is_discount,
            iOSDiscountPrice: payInfo.discount_money_ios_str,
            androidDiscountPrice: payInfo.discount_money_android_str,
            iOSPrice: payInfo.pay_money_ios_str,
            androidPrice: payInfo.pay_money_android_str
        });
    }

    // 初始化挂件信息
    if (data.ad_place) {
        let temp = {
            adStatus: data.ad_place.h5_ad_status,
            compulsoryDisplay: data.ad_place.h5_compulsory_display,
            image: data.ad_place.h5_image,
            skipType: data.ad_place.h5_skip_type,
            skip: data.ad_place.h5_skip
        };
        newState.adPlace = temp;
    }
};

const liveforNa = (data, newState) => {
    const {status} = data || {};
    if (window.globalJumpTestValue === 1) {
        const nid = PageStore.queryParams.nid || '';
        adCommonShowLog({
            page: status, type: 'enter', value: 'entry', nid, screen: 1, serverId: '19092', isNewVersion: true
        });
    }
    // 秀场直播间跳转
    if (data.ext_params && data.ext_params.indexOf('tieba') >= 0 && data.cmd) {
        if (env.isMainBox) {
            goNativeLiveRoom({
                roomId: PageStore.roomId,
                hasVideo: newState.hasVideo,
                tiebaCmd: data.cmd,
                outerSchemeParams: newState.schemeParams
            });
        }
        newState.tiebaLiveCmd = data.cmd;
    }

    // 百度自动调起NA
    if ((autoJumpToBaiduNA())
        && ![1, 4].includes(+data.visual_id)
    ) {
        goNativeLiveRoom({
            roomId: PageStore.roomId,
            hasVideo: newState.hasVideo,
            isPaid: newState.isPaid,
            outerSchemeParams: newState.schemeParams
        });
    }

    // 荣耀调起NA
    if (autoJumpToNA()
        && ![1, 4].includes(+data.visual_id)
    ) {
        requestRoomEnterInfoWithAntiCheats({uid: PageStore.deviceId, roomId: PageStore.roomId}).then(res => {
            const trans_type = res?.anyMountResult?.transType || '';
            goNativeLiveRoom({
                roomId: PageStore.roomId,
                hasVideo: newState.hasVideo,
                isPaid: newState.isPaid,
                outerSchemeParams: newState.schemeParams,
                trans_type
            });
        });

    }
};

const liveforContent = (data, newState) => {
    // 直播相关信息
    if (data.video) {
        let video = data.video;
        let title = video.title;
        let pageDescription = video.description;
        if (pageDescription) {
            // 支持换行及空格
            pageDescription = pageDescription.replace(/\n/g, '<br>');
            pageDescription = pageDescription.replace(/ /g, '&nbsp;');
        }
        else {
            pageDescription = '暂无内容';
        }
        // 竖屏直播优先取竖版封面
        const coverImg
            = (+data?.screen === 0 ? video?.cover?.vertical_cover : video?.cover?.cover_100)
            || video?.cover?.cover_100 || '';

        let merge = {
            pageTitle: title,
            pageDescription,
            msgHlsUrl: replaceProtocol(video.msg_hls_url || data.chat_msg_hls_url),
            liveHlsUrl: replaceProtocol(video.live_hls_url),
            liveFlvUrl: replaceProtocol(video.live_flv_url),
            coverImg,
            hasVerCover: +data?.screen === 0 && (video?.cover?.vertical_cover || '') // 后台已配置竖版封面
        };
        Object.assign(newState, merge);
    }

    // 回放相关信息
    if (data.status === LiveStatus.END_HAS_REVIEW) {
        if (data.replay_list && data.replay_list[0]) {
            let video = data.replay_list[0].video;
            if (video) {
                newState.liveHlsUrl = replaceProtocol(video);
            }
        }
    }
};

const handleLive = (data) => {
    let newState = {
        isPaid: +data.has_pay_service === 1,
        hasVideo: data.has_video > 0,
        hasReflowBar: !!+data.has_reflow_bar && !env.isBox,
        totalUsers: data.online_users
    };

    // 直播内容相关信息
    liveforContent(data, newState);

    // 处理需要携带的scheme参数（启播优化）
    const video = data.video || {};
    newState.schemeParams = getLiveSchemeParams({
        screen: data.screen,
        templateId: data.template_id,
        status: data.status,
        roomType: data.room_type,
        playUrl: video.play_url,
        avcUrl: video.avc_url,
        hevcUrl: video.hevc_url,
        rtcUrl: video.rtc,
        kabr_spts: video.kabr_spts
    });

    // // 安卓 && 矩阵外 && 未微信内 自动调起，调起失败停留在当前页
    // if (env.isAndroid && !env.isMainBox && !isMatrixApp && !env.isTomas && !env.isWechat) {
    //     let reflowConf = {
    //         roomId: PageStore.roomId,
    //         outerSchemeParams: newState.schemeParams,
    //         status: data.status
    //     };

    //     reflow(reflowConf, true);
    // }

    // 端内相关逻辑
    liveforNa(data, newState);

    // 付费与挂件
    liveforPaidAndAdPlace(data, newState);

    // 关注/作者等信息
    liveforHost(data, newState);

    // tabs展示信息
    liveforTabs(data, newState);

    Object.assign(data, newState);
};

// 预览状态
const handlePreview = (data) => {
    // 只在手百主app
    data.isGroupPaid = isSureType(data.group_buy_info, 'is_group_buy');
    data.isLive = +data.is_living === 1;
    // 直播预览页存在视频时（https://live.baidu.com/m/media/multipage/liveshow/index.html?room_id=7337736916&source=h5pre），如果没有nojump参数，则跳转到NA页，有的话用h5承载
    const {nojump = '0'} = PageStore.queryParams;
    // 兼容荣耀浏览器 和 矩阵APP自动调起 非新视觉场景下
    if (autoJumpToNA()
        && +nojump === 0 && +data.visual_id === 0) {
        let inviterId = data.isGroupPaid ? {inviterId: PageStore.queryParams.inviterId} : {};
        closeAndGoNativePre(PageStore.roomId, inviterId, true);
    }

    if (boxjsUtils.platformInfo.isBox) {
        if (data.isGroupPaid) {
            // 付费拼团逻辑
            if (isLargerVersion(env.boxVersion, '12.24')) {
                let inviterId = PageStore.queryParams.inviterId;
                if (+nojump === 0) {
                    closeAndGoNativePre(PageStore.roomId, {inviterId}, true);
                }
            }
            else {
                // 低于 12.24 展示升级提示
                window.location.replace(GroupBuyUrl);
            }
        }
        else if (isLargerVersion(env.boxVersion, '12.18')) {
            // 非新视觉场景下 2 为配置PC相关推荐场景：如世界大会
            if (+nojump === 0 && [0, 2].includes(+data.visual_id)) {
                closeAndGoNativePre(PageStore.roomId, {}, true);
            }
        }
    }
    else {
        // 端外
        // 付费系列课id
        const series_nid = PageStore.queryParams.series_nid || (data.series_info && data.series_info.series_nid);
        data.series_nid = series_nid;

        //  微博中URL有invoke=1参数时直接invoke百度APP（国潮微博弹窗跳转百度APP直播间）
        try {
            if (/weibo/i.test(window.navigator.userAgent) && +PageStore.queryParams.invoke === 1) {
                openReflowByUrl();
            }
            else if (env.isAndroid && !isMatrixApp && !env.isTomas && !env.isWechat) {
                // 安卓 && 矩阵外 && 未微信内 自动调起，调起失败停留在当前页
                // openReflowByUrl(window.location.href, ['2', '3'], true);
            }
        }
        catch (error) {
            console.log(error);
        }
    }
};

export const handleSSRInfo = input => {
    const data = input[371];
    const ssrData = {
        ...data
    };
    if (isSureType(data.is_forbidden_url)) {
        window.location.replace(ForbiddenUrl);
    }

    // 语音房业务跳转
    if (isSureType(data.is_clubhouse)) {
        let url = ClubhouseUrl + window.location.search;
        window.location.replace(url);
    }

    // 更新调起相关信息（scheme 和 口令）
    PageStore.schemaInfo = data.schema_info;

    // 是否预约
    if (data.template === TemplateEnum.Preview) {
        // 预约逻辑处理
        handlePreview(data);
        // 获取一下关注状态
        ssrData.followStatus = false;
        // TODO
        // data.followStatus = await getFollowStatus({
        //     type: data.follow_type,
        //     third_id: data.follow_id,
        //     sfrom: 'sbox',
        // });
    }
    else if (data.template === TemplateEnum.Live) {
        const status = +data.status;
        ssrData.status = status;

        // 错误状态处理
        if (status === LiveStatus.NOT_START || status === PageErrorStatus.TEXT_LIVING || isNaN(status)) {
            if (data.ext_params && data.ext_params.indexOf('tieba') > 0) {
                return {errCode: PageErrorStatus.TIEBA_END};
            }
            return {
                errCode: status === LiveStatus.NOT_START ? PageErrorStatus.NOT_START : PageErrorStatus.ERROR_END
            };
        }

        // 直播/回放逻辑处理
        handleLive(ssrData);
    }
    else {
        return Promise.reject({errCode: PageErrorStatus.ERROR_END});
    }

    return ssrData;
};
function sendLog() {
    // 获取到从页面开始到现在的耗时
    if (!window.performance) {
        return;
    }
    const getLiveInfoTiming = performance.now();
    adCommonShowLog({
        page: 0, type: 'enter', value: 'entry', nid: '', screen: 1, serverId: '19334', isNewVersion: true,
        getLiveInfoTiming
    });
}
export const updatePageData = () => {
    wuXiaShowLog('port_btn', 15263, {
        api_name: 'get_371',
        room_id: PageStore.roomId
    });
    sendLog();
    const fetchStart = Date.now();
    return getMainData().then(async (data) => {
        // is_forbidden_url,引导跳转
        if (isSureType(data.is_forbidden_url)) {
            window.location.replace(ForbiddenUrl);
        }

        // 语音房业务跳转
        if (isSureType(data.is_clubhouse)) {
            let url = ClubhouseUrl + window.location.search;
            window.location.replace(url);
        }

        // 更新调起相关信息（scheme 和 口令）
        PageStore.schemaInfo = data.schema_info;

        // 是否预约
        if (data.template === TemplateEnum.Preview) {
            // 预约逻辑处理
            handlePreview(data);
            // 获取一下关注状态
            data.followStatus = await getFollowStatus({
                type: data.follow_type,
                third_id: data.follow_id,
                sfrom: 'sbox'
            });
        }
        else if (data.template === TemplateEnum.Live) {
            const status = +data.status;
            data.status = status;

            // 错误状态处理
            if (status === LiveStatus.NOT_START || status === PageErrorStatus.TEXT_LIVING || isNaN(status)) {
                if (data.ext_params && data.ext_params.indexOf('tieba') > 0) {
                    return Promise.reject({errCode: PageErrorStatus.TIEBA_END});
                }
                return Promise.reject({
                    errCode: status === LiveStatus.NOT_START ? PageErrorStatus.NOT_START : PageErrorStatus.ERROR_END
                });
            }

            // 直播/回放逻辑处理
            handleLive(data);
        }
        else {
            return Promise.reject({errCode: PageErrorStatus.ERROR_END});
        }

        vSpeed.sendTTI();
        vSpeed.sendFMPBySelf();
        vSpeed.send({
            'api-371-js': Date.now() - fetchStart
        });
        wuXiaShowLog('port_btn', 15263, {
            api_name: 'get_371_back',
            room_id: PageStore.roomId,
            errno: 0,
            time: Date.now() - fetchStart
        });

        return data;
    }).catch(({errCode}) => {
        wuXiaShowLog('port_btn', 15263, {
            api_name: 'get_371_back',
            room_id: PageStore.roomId,
            errno: errCode,
            time: Date.now() - fetchStart
        });
        return Promise.reject({
            errCode
        });
    });
};
