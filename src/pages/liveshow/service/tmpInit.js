/* eslint-disable */

// ! 调试方式：babel/swc编译本文件后copy到tmpInit-prod.js下

module.exports = (doc, win) => {
    function signed_crc_table() {
        let c = 0; let table = new Array(256);
        for (let n = 0; n != 256; ++n) {
            c = n;
            for (let k = 0; k < 8; ++k) {
                c = (c & 1) ? (-306674912 ^ (c >>> 1)) : (c >>> 1);
            }
            table[n] = c;
        }
        return typeof Int32Array !== 'undefined' ? new Int32Array(table) : table;
    }

    let T0 = signed_crc_table();

    function slice_by_16_tables(T) {
        let c = 0; let v = 0; let n = 0;
        let table = typeof Int32Array !== 'undefined' ? new Int32Array(4096) : new Array(4096);
        for (n = 0; n != 256; ++n) {
            table[n] = T[n];
        }
        for (n = 0; n != 256; ++n) {
            v = T[n];
            for (c = 256 + n; c < 4096; c += 256) {
                v = table[c] = (v >>> 8) ^ T[v & 0xFF];
            }
        }
        let out = [];
        for (n = 1; n != 16; ++n) {
            out[n - 1] = typeof Int32Array !== 'undefined' ? table.subarray(n * 256, n * 256 + 256) : table.slice(n * 256, n * 256 + 256);
        }
        return out;
    }

    let TT = slice_by_16_tables(T0);
    let [T1, T2, T3, T4, T5, T6, T7, T8, T9, Ta, Tb, Tc, Td, Te, Tf] = TT;

    function crc32_bstr(bstr, seed) {
        let C = seed ^ -1;
        for (let i = 0, L = bstr.length; i < L;) {
            C = (C >>> 8) ^ T0[(C ^ bstr.charCodeAt(i++)) & 0xFF];
        }
        return ~C;
    }

    function crc32_buf(B, seed) {
        let C = seed ^ -1; let L = B.length - 15; let i = 0;
        for (; i < L;) {
            C
			= Tf[B[i++] ^ (C & 255)]
			^ Te[B[i++] ^ ((C >> 8) & 255)]
			^ Td[B[i++] ^ ((C >> 16) & 255)]
			^ Tc[B[i++] ^ (C >>> 24)]
			^ Tb[B[i++]] ^ Ta[B[i++]] ^ T9[B[i++]] ^ T8[B[i++]]
			^ T7[B[i++]] ^ T6[B[i++]] ^ T5[B[i++]] ^ T4[B[i++]]
			^ T3[B[i++]] ^ T2[B[i++]] ^ T1[B[i++]] ^ T0[B[i++]];
        }
        L += 15;
        while (i < L) {
            C = (C >>> 8) ^ T0[(C ^ B[i++]) & 0xFF];
        }
        return ~C;
    }

    function crc32_str(str, seed) {
        let C = seed ^ -1;
        for (let i = 0, L = str.length, c = 0, d = 0; i < L;) {
            c = str.charCodeAt(i++);
            if (c < 0x80) {
                C = (C >>> 8) ^ T0[(C ^ c) & 0xFF];
            }
            else if (c < 0x800) {
                C = (C >>> 8) ^ T0[(C ^ (192 | ((c >> 6) & 31))) & 0xFF];
                C = (C >>> 8) ^ T0[(C ^ (128 | (c & 63))) & 0xFF];
            }
            else if (c >= 0xD800 && c < 0xE000) {
                c = (c & 1023) + 64; d = str.charCodeAt(i++) & 1023;
                C = (C >>> 8) ^ T0[(C ^ (240 | ((c >> 8) & 7))) & 0xFF];
                C = (C >>> 8) ^ T0[(C ^ (128 | ((c >> 2) & 63))) & 0xFF];
                C = (C >>> 8) ^ T0[(C ^ (128 | ((d >> 6) & 15) | ((c & 3) << 4))) & 0xFF];
                C = (C >>> 8) ^ T0[(C ^ (128 | (d & 63))) & 0xFF];
            }
            else {
                C = (C >>> 8) ^ T0[(C ^ (224 | ((c >> 12) & 15))) & 0xFF];
                C = (C >>> 8) ^ T0[(C ^ (128 | ((c >> 6) & 63))) & 0xFF];
                C = (C >>> 8) ^ T0[(C ^ (128 | (c & 63))) & 0xFF];
            }
        }
        return ~C;
    }

    const crc32 =  {
        table: T0,
        bstr: crc32_bstr,
        buf: crc32_buf,
        str: crc32_str
    };

    function once(fn) {
        let called = false;
        let result;

        return function (...args) {
            if (!called) {
                called = true;
                result = fn(...args);
            }
            return result;
        };
    }

    const getUrlAllParam = (link = '') => {
        let paramStr = '';
        if (link && typeof link === 'string') {
            paramStr = link.substring(link.indexOf('?') + 1, link.length).replace(/#\/$/, '');
        }
        else {
            const hash = window.location.hash;
            paramStr = window.location.search.substr(1) || hash.substring(hash.indexOf('?') + 1, hash.length);
        }
        const urlList = paramStr.split('&');
        const urlObject = {};
        urlList.forEach((item) => {
            const urlItem = item.split('=');
            if (urlItem[1]) {
                urlObject[urlItem[0]] = decodeURIComponent(urlItem[1]);
            }
        });
        return urlObject;
    };
    const getCachedUrlParams = once(() => getUrlAllParam(location.href));

    /**
 * 将字符串映射到 [0, bucketCount-1] 的桶编号
 */
    function getHashBucket(str, bucketCount = 100) {
        const hash = crc32.str(str);
        const unsigned = hash >>> 0;
        return unsigned % bucketCount;
    }

    /**
 * 通用流量分流函数
 * @param options.key 参与 hash 的参数名（从 URL 中获取）
 * @param options.buckets 分流桶定义，如：{ test: 50, origin: 50 }
 * @param options.defaultKey 可选，如果无法分流，返回的默认桶名
 */
    function getTrafficBucket(options) {
        const {buckets} = options;
        const id = bd_vid;

        const bucketNames = Object.keys(buckets);
        const totalWeight = Object.values(buckets).reduce((sum, val) => sum + val, 0);

        let ratio = -1;
        if (id) {
            const bucketIndex = getHashBucket(id, 10000); // 高精度 hash
            ratio = bucketIndex / 10000;
        }
        else {
            ratio = Math.random();
        }

        let acc = 0;
        for (const name of bucketNames) {
            acc += buckets[name] / totalWeight;
            if (ratio < acc) {
                return name;
            }
        }

        return bucketNames[bucketNames.length - 1];
    }

    function getUrlParam(name) {
        let reg = new RegExp('(^|(&|/?))' + name + '=([^&]*)(&|$)', 'i');
        let r = win.location.search.substr(1).match(reg);
        if (r !== null) {
            return r[3];
        }
        return null;
    }
    function checkHasValue(value) {
        if (value) {
            let ignore = ['nil', 'null', 'nullb', 'undefined', '0'];
            let check = true;
            ignore.forEach((item) => {
                if (item === value) {
                    check = false;
                }
            });
            return check;
        }
        return false;
    }
    function transUrlParam(name) {
        let value = getUrlParam(name);
        return checkHasValue(value) ? '&' + name + '=' + value : '';
    }

    function isMobileOld() {
        return /android.*?mobile|ipod|blackberry|bb\d+|phone|WindowsWechat/i.test(win.navigator.userAgent);
    }
    function isMobileNew() {
        // eslint-disable-next-line
        return /android|ipod|iPad|blackberry|bb\d+|phone|WindowsWechat|baiduboxapp|bdhonorbrowser/i.test(win.navigator.userAgent);
    }

    function isAppRedirectBlocked() {
        const {is_jump_app} = getUrlAllParam(location.href) || {};
        return is_jump_app !== undefined && +is_jump_app === 0;
    }

    // 商业直播H5路径拼接
    // 情况1：room_id=xx&livesource=xx&source=xx&bd_vid=xx&ch=xx&bd_bxst=xx&fid=xx
    // 情况2：room_id=9997353272&ch=4&livesource=xxx&is_jump_app=0
    // bd_vid 有值或 is_jump_app=0 时支持在H5内完成转化
    function isAdApp() {
        const urlParams = getUrlAllParam(location.href)
        const {bd_vid = ''} = urlParams || {};
        return bd_vid || isAppRedirectBlocked();
    }

    const isPAD = function () {
        return isTablet() || isPADMac() || isAdApp();
    };
    function isPADMac() {
        return /iPad/i.test(win.navigator.userAgent)
            || (win.navigator.platform === 'MacIntel' && win.navigator.maxTouchPoints > 1);
    }
    function isTablet() {
        const ua = win.navigator.userAgent.toLowerCase();
        // 2. UA 检测
        return /ipad|tablet|android(?!.*mobile)/i.test(ua);

    }


    let APP_NAME_PARAM = {
        a6: '百度看看',
        i6: '百度看看',
        haokan: '好看视频',
        default: '百度',
        lite: '百度极速版',
        tieba: '百度贴吧',
        youjia: '有驾',
        baidudict: '百度汉语',
        fortunecat: '古物潮玩',
        hiphop: '音磁'
    };
    let bd_vid = getUrlParam('bd_vid');
    const inPCTest = getTrafficBucket({
        buckets: {
            test: 90,
            origin: 10
        }
    });
    window.inPCTest = inPCTest;
    const condition = inPCTest ? !isMobileNew() && !isPAD() : !isMobileOld();
    if (condition) {
        let roomid = getUrlParam('roomid') || getUrlParam('room_id');
        let source = getUrlParam('source') || 'h5pre';
        let askID = transUrlParam('ask_id');
        let replaySlice = transUrlParam('replay_slice');
        let nid = transUrlParam('nid');
        if (roomid) {
            let newHost
                = 'https://live.baidu.com/m/media/pclive/pchome/live.html?room_id='
                + roomid
                + '&source='
                + source
                + askID;
            if (replaySlice && nid) {
                newHost = newHost + replaySlice + nid;
            }
            win.location.href = newHost;
        }
    }
    let urlSource = getUrlParam('source');
    if (APP_NAME_PARAM[urlSource]) {
        doc.title = APP_NAME_PARAM[urlSource] + '直播';
    }
};
