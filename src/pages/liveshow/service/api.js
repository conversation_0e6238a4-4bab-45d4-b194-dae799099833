/**
 * @file 与接口相关的所有封装
 * <AUTHOR>
 * @date 2020-12-14 16:25:38
 */
import md5 from 'md5';

import {request} from '@super-fe/BdrainfnAjax';
import {getSystemInfo} from '@baidu/ug-matrix';
import {jsonp} from '@/utils/request';
import {env} from '@/utils/env';
import {OSVersion} from '@baidu/boxx/env';
import axios from 'axios';
import qs from 'qs';
import {getAppSchemeHead, checkHasValue} from '@/pages/liveshow/utils/getUrlParam';
import {dictionaryOrder} from '@/utils/utils';
import {devHistoryMessages, isDevMockFlag} from 'mock/debug';
import {prefetchRequest} from '../components/AdLiveShow/AnyDoor/request';
import {PageStore} from './register';

const HOST = `${location.protocol}//mbd.baidu.com`;
// const HOST_TEST = `${location.protocol}//fanhang.bcc-szzj.baidu.com:8505`;
const HOST_TIEBA = env.isMainBox ? `${location.protocol}//livec.baidu.com` : `${location.protocol}//tiebac.baidu.com`;

axios.defaults.withCredentials = true;
const requestUa = `${window.innerWidth
}_${window.innerHeight
}_${env.isIOS ? 'IOS' : env.isAndroid ? 'ANDROID' : '0'
}_${env.boxVersion}`;

const osname = getAppSchemeHead();

const cmdEnum = {
    STATUS: 319, // 获取结束状态
    CANCEL_SUBSCRIBE: 321, // 立即预约
    MAIN: 371, // 主页面 (预约状态和直播状态都一个接口返回 根据template == 'live' 判断状态)
    MOPE_RECOM: 229 // 更多推荐
};


/**
 * @description 获取反作弊依赖公参
 */
export const reqXafCommonParams = async () => {

    const info = await getSystemInfo();

    return {
        app_name: PageStore.queryParams.branchname || 'baiduboxapp',
        os: env.isIOS ? 1 : env.isAndroid ? 2 : 0,
        os_version: OSVersion || '',
        cuid: PageStore.deviceId || '',
        zid: PageStore.queryParams.zid || '',
        ua: navigator.userAgent || '',
        model: info.model || '',
        brand: info.brand || '',
        uid: PageStore.queryParams.uid || '',
        app: env.isIOS ? 'ios' : env.isAndroid ? 'android' : 'universe',
        app_version: env.boxVersion,
        cua: PageStore.queryParams.ua || '',
        cfrom: PageStore.queryParams.cfrom || '',
        network: PageStore.queryParams.network || '',
        cen: PageStore.queryParams.cen || '',
        platform: env.isIOS ? 'ios' : env.isAndroid ? 'android' : 'universe'
    };
};

/**
 * 获取风控所需 oncetoken 参数
 *
 * @param {Number} retries - 重试次数
 * @param {Number} delay - 每次重试间隔 ms
 * @return {Sting} 31$CODED--v31..balabalabala..NQ==
 */
const getOnceToken = (retries = 5, delay = 30) => {

    return new Promise((resolve) => {
        let attempt = 0;
        const fetch = () => {
            try {
                attempt++;
                window.xaf.getot(token => {
                    resolve(token);
                });
            }
            catch (error) {
                if (attempt < retries) {
                    setTimeout(fetch, delay);
                }
                else {
                    resolve('');
                }
            }
        };
        fetch();
    });
};

const searchBoxRequest = (cmd, data, action = 'star', urlParams = {}) => {
    let params = {
        cmd,
        action,
        service: 'bdbox',
        osname,
        data: encodeURIComponent(JSON.stringify(data)),
        ...urlParams
    };
    return jsonp(`${HOST}/searchbox`, params).then((res) => {
        const data = res.data;
        if (res && +res.errno === 0 && res.data && res.data[cmd]) {
            return Promise.resolve(res.data[cmd]);
        }
        let errMsg = '服务错误,请稍后再试';
        let errCode = 0;
        if (+data.errno !== 0 && data.errmsg) {
            errMsg = data.errmsg;
            errCode = data.errno;
        }
        if (res.data && res.data[cmd] && res.data[cmd].error_code > 0) {
            errCode = res.data[cmd].error_code;
            errMsg = res.data[cmd].error_msg;
        }
        return Promise.reject({
            errMsg,
            errCode
        });
    });
};

const sendSrcRequest = () => {
    const nid = PageStore.queryParams.nid || '';
    const bdVid = PageStore.queryParams.bd_vid || '';
    let data = {
        data: {
            room_id: PageStore.roomId,
            device_id: PageStore.deviceId,
            source_type: 0,
            osname // 外层osname rd反馈取不到具体值 统一被赋值为baiduboxapp了
        }
    };
    let askId = PageStore.queryParams.ask_id || '';
    if (askId) {
        data.data.ask_id = askId;
    }
    data.replay_slice = PageStore.queryParams.replay_slice || 0;
    data.nid = checkHasValue(nid) ? nid : '';
    // 高考挂牌切片分享，添加wish_record_id标识区分
    if (PageStore.queryParams?.wish_record_id) {
        data.data.wish_record_id = +PageStore.queryParams?.wish_record_id;
    }
    // server 生成调起scheme 和 口令所需参数
    const browser = env.isWechat ? 'wechat' : 'other';
    // 粉丝团任务回流用的参数
    const shareTaskInfo = [
        'cUk',
        'bUk',
        'taskId',
        'fansGroupId',
        'activityType',
        'room_id',
        'shareSource',
        'lotteryActivityId',
        'shareExt'
    ].reduce((pre, cur) => {
        // tempValue为decode一次后的
        const tempValue = PageStore.queryParams[cur];
        if (cur === 'shareExt') {
            try {
                pre[cur] = JSON.parse(tempValue);
            }
            catch (e) {
                pre[cur] = tempValue;
            }
        }
        else {
            pre[cur] = tempValue;
        }
        return pre;
    }, {});

    const decodeSource = decodeURIComponent(PageStore.queryParams.source || '');
    const src_pre = decodeSource.includes('|')
        ? decodeSource.split('|')[1]
        : decodeSource;

    data.schemeParams = {
        src_pre: src_pre || (bdVid && 'bd_ads') || '', // source 前缀
        src_suf: browser, // source 后缀
        bd_vid: bdVid,
        share_uid: PageStore.queryParams.share_uid || '',
        share_cuk: PageStore.queryParams.share_cuk || '',
        share_ecid: PageStore.share_ecid || '', // 直播间分享来源的订单分享人信息透传2,新增加密cuid
        zb_tag: PageStore.queryParams.zb_tag || '',
        shareTaskInfo: JSON.stringify(shareTaskInfo), // 拼团裂变相关信息
        share_from: PageStore.queryParams.share_from || '',
        ext_params: decodeURIComponent(PageStore.queryParams.ext_params || ''),
        nid: nid
    };
    return searchBoxRequest(cmdEnum.MAIN, data, 'star', {
        ua: requestUa,
        bd_vid: bdVid,
        uid: PageStore.deviceId
    });
};
// 获取主页面数据
export const getMainData = async () => {
    const res = await prefetchRequest(
        window.__prefetchDatasource__?.requestMainData,
        () => sendSrcRequest()
    );
    return res;
};

// 预约状态更改
export const subscribe = (subscribe, roomId = PageStore.roomId) => {
    let data = {
        data: {
            roomid: roomId,
            subscribe: subscribe
        }
    };
    return searchBoxRequest(cmdEnum.CANCEL_SUBSCRIBE, data, 'star', {
        uid: PageStore.deviceId,
        ua: requestUa
    }).then((res) => {
        return +res.status === 0;
    });
};

// 获取结束状态
export const getEndStatus = () => {
    let data = {
        data: {
            roomid: PageStore.roomId
        }
    };
    return searchBoxRequest(cmdEnum.STATUS, data);
};

// 获取更多推荐
export const getMoreRecommand = () => {
    let data = {
        room_id: PageStore.roomId,
        device_id: PageStore.deviceId,
        source_type: 1,
        source_from_type: 1, // 添加此参数 接口会返回是否是预约状态
        source: PageStore.queryParams.source || '',
        osname,
        _: Math.round(+new Date() / 1000),
        req_from: 'h5_live_landpage'
    };

    return searchBoxRequest(cmdEnum.MOPE_RECOM, data);
};

// 获取历史消息
export const getHistoryComment = () => {
    const timestamp = Math.round(+new Date() / 1000);
    const roomId = PageStore.roomId;
    const token = md5(`${roomId}${timestamp}lFCG7T14ww4KGlX2c4OcemCCTD0KWjX1`);
    const params = {
        appid: 405384,
        uid: '',
        src: '1',
        ak: 'G5pWdRhA13YClRe6zhx0x1nY',
        room_id: roomId,
        mcast_type: '1',
        count: '-50',
        msgid_end: +new Date() * 1000,
        timestamp,
        token
    };
    const historyMessageHost = `${location.protocol}//liveshowserv.baidu.com/api/1.0/users.fetchMsg`;
    return request({
        data: {
            url: historyMessageHost,
            data: params,
            dataType: 'json',
            method: 'POST'
        }
    }).then(
        (res) => {
            if (+res.error_code === 0) {
                return res.messages;
            }
            return [];
        },
        () => (isDevMockFlag ? devHistoryMessages : [])
    );
};

export const pcUsersReport = () => {
    const roomID = PageStore.roomId;
    return jsonp(`${HOST_TIEBA}/bdlive/log/h5report`, {
        room_id: roomID
    }).then((res) => {
        if (res.errno === 0) {
            return Promise.resolve();
        }
        return Promise.reject(res.errmsg);
    });
};

// 获取系列课详情
export const getCourseDetail = async (params) => {
    // return mockJson;
    const newparams = {
        type: 'series',
        action: 'detail',
        format: 'jsonp',
        data: JSON.stringify(params)
    };
    return jsonp(`${HOST}/webpage`, newparams);
};

// 获取优惠券信息
export const getCouponDetail = async (params) => {
    // return coupon;
    const host = 'https://cecom.baidu.com';
    const url = `${host}/proxy/live/coupon/best_coupon?sign=c25ffb389a85a2550135fc860098163a`;
    return request({
        data: {
            url,
            data: params,
            dataType: 'json',
            method: 'POST'
        }
    });
};

// 获取商品列表
export const getCoodsList = async params => {
    // return goods;
    const host = 'https://cecom.baidu.com';
    const url = `${host}/proxy/live/goods/roomGoodsList?sign=c25ffb389a85a2550135fc860098163a`;
    return request({
        data: {
            url,
            data: params,
            dataType: 'json',
            method: 'POST'
        }
    });
};

// 获取微信开放标签 配置信息
export const getWxConfig = () => {
    const url = `${location.protocol}//ug.baidu.com/weixintoken`;
    const params = {
        share_url: location.href,
        // bca-disable-next-line
        appid: 'wxe4b4fae2efdaad19',
        // bca-disable-next-line
        from: 'live'
    };
    return request({
        data: {
            url,
            data: JSON.stringify(params),
            dataType: 'json',
            method: 'POST'
        }
    });
};

// 批量预约
export const batchSubscribeLive = roomIds => {
    const params = {
        room_ids: roomIds,
        appname: 'baiduboxapp',
        ua: '1170_2532_iphone_13.46.5.10_0', // 必须是五段构成的ua!!!!
        uid: PageStore.deviceId
    };

    const token = 'tiebaclient!!!';

    return axios.post(`${HOST_TIEBA}/bdlive/user/batch_subscription`,
        qs.stringify({
            ...params,
            sign: md5(dictionaryOrder(params) + token)
        })
    ).then(res => res.data).catch(() => {});
};

// 武侠歌会 预约+关注接口
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/kVI24OE6uV/3IzHMHtsxu/79DtJlVAsA7fmo
export const SubscribeAndFollow = async (params) => {
    const reqCommon = await reqXafCommonParams();
    const oncetoken = await getOnceToken();

    const rcp = {
        room_id: params.roomId || '',
        follow_anchor: params.followAnchor || '',
        cuid: PageStore.deviceId || '',
        share_cuk: PageStore.queryParams.share_cuk || '',
        client_type: env.isIOS ? 1 : 2
    };

    const destParams = {
        ...rcp,
        ...reqCommon,
        oncetoken
    };

    const token = 'tiebaclient!!!';

    return axios.post(`${HOST_TIEBA}/bdlive/invite/subscription`,
        qs.stringify({
            ...destParams,
            sign: md5(dictionaryOrder(destParams) + token)
        })
    ).then(res => res.data);
};

// 活动预约页 预约+关注接口
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/kVI24OE6uV/3IzHMHtsxu/4Vg1zuCaqXnwDt
export const CommonSubscribeAndFollow = async (params) => {
    const rcp = {
        room_id: params.roomId || '',
        follow_anchor: params.followAnchor || '',
        cuid: PageStore.deviceId || '',
        client_type: env.isIOS ? 1 : 2,
        act_id: params.actId,
        biz_id: params.bizId
    };

    const destParams = {
        ...rcp
    };

    const token = 'tiebaclient!!!';

    return axios.post(`${HOST_TIEBA}/bdlive/invite/subscription`,
        qs.stringify({
            ...destParams,
            sign: md5(dictionaryOrder(destParams) + token)
        })
    ).then(res => res.data);
};

// 活动预约页 助力
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/kVI24OE6uV/3IzHMHtsxu/4Vg1zuCaqXnwDt
export const getSupportResult = async (params) => {
    const reqCommon = await reqXafCommonParams();
    const oncetoken = await getOnceToken();

    const destParams = {
        ...reqCommon,
        ...params,
        room_id: PageStore.roomId || '',
        cuid: PageStore.deviceId || '',
        share_cuk: PageStore.queryParams.share_cuk || '',
        client_type: env.isIOS ? 1 : 2,
        oncetoken
    };

    const token = 'tiebaclient!!!';

    return axios.post(`${HOST_TIEBA}/bdlive/invite/support`,
        qs.stringify({
            ...destParams,
            sign: md5(dictionaryOrder({...destParams}) + token)
        })
    ).then(res => res.data).catch(() => {});
};

// 活动预约页 邀请任务接口 （获取当前邀请用户信息）
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/kVI24OE6uV/3IzHMHtsxu/4Vg1zuCaqXnwDt
export const getInviteResult = async () => {
    const rcp = {
        room_id: PageStore.roomId || ''
    };
    const token = 'tiebaclient!!!';

    return axios.post(`${HOST_TIEBA}/bdlive/invite/subscription_record`,
        qs.stringify({
            ...rcp,
            sign: md5(dictionaryOrder(rcp) + token)
        })
    ).then(res => res.data);
};

export const getLotteryResult = async () => {
    const reqCommon = await reqXafCommonParams();
    const oncetoken = await getOnceToken();

    const destParams = {
        ...reqCommon,
        room_id: PageStore.roomId || '',
        oncetoken
    };

    const token = 'tiebaclient!!!';

    return axios.post(`${HOST_TIEBA}/bdlive/invite/draw`,
        qs.stringify({
            ...destParams,
            sign: md5(dictionaryOrder({...destParams}) + token)
        })
    ).then(res => res.data).catch(() => {});
};

// 通用活动预约页 助力列表
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/kVI24OE6uV/3IzHMHtsxu/mMayQgCJdVS18Q#anchor-1aba07b0-b3b0-11ef-b57d-4bd633e0bfab
export const getInviteList = async (params) => {
    // const reqCommon = await reqXafCommonParams();

    const destParams = {
        // ...reqCommon,
        room_id: PageStore.roomId || '',
        ...params
    };

    return axios.post(`${HOST_TIEBA}/bdlive/invite/support_list`,
        qs.stringify({
            ...destParams
        })
    ).then(res => res.data).catch(() => {});
};

// 通用活动预约页 中奖名单
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/kVI24OE6uV/3IzHMHtsxu/mMayQgCJdVS18Q#anchor-1aba07b0-b3b0-11ef-b57d-4bd633e0bfab
export const getWinnerList = async (params) => {
    // const reqCommon = await reqXafCommonParams();

    const destParams = {
        // ...reqCommon,
        room_id: PageStore.roomId || '',
        ...params
    };

    return axios.post(`${HOST_TIEBA}/bdlive/activity/draw/winner_list`,
        qs.stringify({
            ...destParams
        })
    ).then(res => res.data).catch(() => {});
};

// 通用活动预约页 抽奖接口
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/kVI24OE6uV/3IzHMHtsxu/mMayQgCJdVS18Q
export const getLotteryResultV2 = async (params) => {
    const reqCommon = await reqXafCommonParams();
    const oncetoken = await getOnceToken();

    const destParams = {
        ...reqCommon,
        ...params,
        room_id: PageStore.roomId || '',
        oncetoken
    };

    const token = 'tiebaclient!!!';

    return axios.post(`${HOST_TIEBA}/bdlive/invite/draw_v2`,
        qs.stringify({
            ...destParams,
            sign: md5(dictionaryOrder({...destParams}) + token)
        })
    ).then(res => res.data).catch(() => {});
};

// 通用活动预约页 关注接口
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/kVI24OE6uV/3IzHMHtsxu/wKFQTbh1Ymglcx
export const getFollowRes = async (params) => {
    const reqCommon = await reqXafCommonParams();

    const destParams = {
        ...reqCommon,
        ...params,
        room_id: PageStore.roomId || ''
    };
    const token = 'tiebaclient!!!';

    return axios.post(`${HOST_TIEBA}/bdlive/user/follow`,
        qs.stringify({
            ...destParams,
            sign: md5(dictionaryOrder({...destParams}) + token)
        })
    ).then(res => res.data).catch(() => {});
};

// 通用活动预约页 取消关注接口
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/kVI24OE6uV/3IzHMHtsxu/wKFQTbh1Ymglcx
export const getUnfollowRes = async (params) => {
    const reqCommon = await reqXafCommonParams();

    const destParams = {
        ...reqCommon,
        ...params,
        room_id: PageStore.roomId || ''
    };
    const token = 'tiebaclient!!!';

    return axios.post(`${HOST_TIEBA}/bdlive/user/unfollow`,
        qs.stringify({
            ...destParams,
            sign: md5(dictionaryOrder({...destParams}) + token)
        })
    ).then(res => res.data).catch(() => {});
};

// base64 前缀
const base64Prefix = 'data:image/png;base64';

/**
 * 生成一个用不重复的ID
 * @param { Number } randomLength
 */
export const getUuid = randomLength => {
    return Number(Math.random().toString().substr(2, randomLength) + Date.now()).toString(36);
};

// 上传图片
export const uploadImage = (imageBase64, namePrefix = 'img_') => {
    if (imageBase64.startsWith(base64Prefix)) {
        imageBase64 = imageBase64.split(';base64,')[1];
    }
    const imageName = `${namePrefix + getUuid(8)}.png`;
    // https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/1WlB_LN1ZE/K48EJw57rNlx_Y
    const params = new URLSearchParams();
    // 如：canvas.toDataURL().split(';base64,')[1]，特别注意：没有;base64, 前缀
    params.append('image_base64', imageBase64);
    // 如：test.png，特别注意：如果名字不变，再次请求会返回 空字符串
    params.append('image_name', imageName);
    // http://wxcoff.bcc-gzhxy.baidu.com:8089

    return axios.post('https://mbd.baidu.com/dynamic/activity/imgUpload/question_source', params.toString()
    ).then(res => res.data).catch(() => {});
    // return context.request.post('https://mbd.baidu.com/dynamic/activity/imgUpload/question_source', params.toString(), {
    //     // 不弹默认toast
    //     preventToast: true,
    // });
};

export const upload = (params) => {

    return axios.post(`${location.protocol}//liveshowserv.baidu.com/live/common/upload`,
        qs.stringify({
            ...params
        })
    ).then(res => res.data).catch(() => {});
};
