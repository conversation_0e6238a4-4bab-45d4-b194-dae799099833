/**
 * @file 底部主播头像+关注组件（新版）
 * <AUTHOR>
 * @date 2022-04-19 14:00:18
 */

import React, { useEffect } from 'react';
import './index.less';
import FollowBtn from '../FollowBtn';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import { Avatar } from '@baidu/nano-react';
import { commonShowLog, commonClickLog } from '@/pages/liveshow/log';
import {supportMcpInvoke} from '@/pages/liveshow/config/const';
import { isFromHonorBrowser } from '@/pages/liveshow/service/register';

function AuthorInfo(props) {
    let {
        followStatus,
        follow_id,
        follow_type,
        isLogin,
        updatePageData,
        hostAvatar,
        template,
        status,
        nid,
        screen,
        componentPos,
        handleInvoke,
        isMatrixApp
    } = props;

    useEffect(() => {
        commonShowLog(status, 'head', nid, screen, '15264', true, componentPos);
    }, []);

    const clickHead = e => {
        e.preventDefault();
        e.stopPropagation();

        commonClickLog(status, 'head', nid, screen, '15264', true, componentPos);

        if (supportMcpInvoke) {
            handleInvoke('part');
        }
        else {
            boxjsUtils.toAuthorPage(follow_id, follow_type === 'ugc');
        }

    };

    const followParams = {
        followStatus,
        template,
        third_id: follow_id,
        type: follow_type,
        is_login: isLogin,
        updatePageData,
        status,
        nid,
        screen,
    };

    if (supportMcpInvoke) {
        followParams.onClickFollow = () => handleInvoke('part');
    }

    return (
        <div
            className={`bottom-author-info-wrapper ${+followStatus === 1 ? 'is-followed' : ''}`}
            onClick={clickHead}>
            <Avatar className='avatar' src={hostAvatar} />
            {/* 荣耀白牌不展示关注按钮 */}
            {!isFromHonorBrowser && <FollowBtn {...followParams} isIconStyle componentPos={componentPos} />}
        </div >
    );
}

export default AuthorInfo;
