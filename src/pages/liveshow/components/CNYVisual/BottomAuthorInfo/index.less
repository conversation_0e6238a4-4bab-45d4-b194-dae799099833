@import (reference) '../../../assets/style-util.less';

.bottom-author-info-wrapper {
    width: 210pr;
    height: 120pr;
    padding-left: 15pr;
    position: relative;
    display: flex;
    align-items: center;
    border-radius: 60pr;
    box-sizing: border-box;
    .common-btn-bgcolor;
    // animation: all 1s ease;

    &.is-followed {
        background: rgba(0, 0, 0, .23);
    }

    .avatar {
        flex: none;
        width: 90pr;
        height: 90pr;

        &::after {
            border: 9pr solid #fff;
            box-sizing: border-box;
        }
    }
}
