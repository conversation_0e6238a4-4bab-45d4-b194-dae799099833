/**
 * @file CNY底bar
 */

import React, {useEffect, useState} from 'react';
import <PERSON><PERSON> from 'react-lottie';
import animationData from './lottie/mainBtn.json';
import {cnyClickLog} from '@/pages/liveshow/log';
import {supportMcpInvoke} from '@/pages/liveshow/config/const';
import './index.less';

function CNYBottomBar(props) {
    const { onClickShowModel, handleInvoke, newVisual} = props;
    const isBackFlow = newVisual?.cny_bottom_bar && Object.keys(newVisual.cny_bottom_bar).length > 0;
    const backFlowConfig = newVisual?.cny_bottom_bar || {};

    const [animationDataObject, setAnimationDataObject] = useState(null);

    useEffect(() => {
        const fetchAnimationData = async () => {
            try {
                const response = await fetch(backFlowConfig?.main_btn_img);
                if (response.ok) {
                    const data = await response.json();
                    setAnimationDataObject(data);
                } else {
                    console.error('Failed to fetch animation data:', response.status);
                }
            } catch (error) {
                console.error('Error fetching animation data:', error);
            }
        };
        if (isBackFlow) {
            fetchAnimationData();
        }
        else {
            setAnimationDataObject(animationData);
        }
    }, []);


    const mainBtn = {
        loop: true,
        autoplay: true,
        animationData: animationDataObject,
        rendererSettings: {
          preserveAspectRatio: 'xMidYMid slice',
        },
    };

    const clickBottomBar = e => {
        e.preventDefault();
        e.stopPropagation();

        cnyClickLog('4', '15263')

        if (supportMcpInvoke) {
            handleInvoke('part');
        }
        else {
            onClickShowModel({
                show: true,
                type: 'reflow',
                from: 'bottomBar',
            });
        }
    };

    return (
        <div className='cny-bottom-bg'  onClick={clickBottomBar}>
            <div className='cny-bar-wrapper'>
                <div className='cny-icon-1'>
                    <div className='icon-img1'></div>
                    <div className='icon-words'>发评论</div>
                </div>
                <div className='cny-icon-1'>
                    <div className='icon-img2'></div>
                    <div className='icon-words'>送礼物</div>
                </div>
                <div className='cny-icon-1'>
                    <div className='icon-img3'></div>
                    <div className='icon-words'>分享</div>
                </div>
                <div className='cny-icon-1'>
                    <div className='icon-img4'></div>
                    <div className='icon-words'>点赞</div>
                </div>
                <div className='cny-icon-2'></div>
            </div>
            <div className='cny-btn-wrapper'>
                <div
                    className='btn-bg'
                    style={{
                        backgroundImage: `url(${backFlowConfig?.btn_bg ?? ''})`
                    }}
                ></div>
                <div
                    className='cny-btn1'
                    style={{
                        backgroundImage: `url(${backFlowConfig?.left_btn_img ?? ''})`
                    }}
                >
                </div>
                <div className='cny-btn2'>
                    <Lottie options={mainBtn} />
                </div>
                <div
                    className='cny-btn3'
                    style={{
                        backgroundImage: `url(${backFlowConfig?.right_btn_img ?? ''})`
                    }}
                >
                </div>
            </div>
        </div>

    );
}

export default CNYBottomBar;
