/**
 * @file 聊天框组件（新版）
 * <AUTHOR>
 * @date 2022-04-19 14:00:18
 */

import React, { useEffect } from 'react';
import './index.less';
import { hasClass } from '@/utils';
import { env } from '@/utils/env';
import { commonShowLog, commonClickLog } from '@/pages/liveshow/log';
import {supportMcpInvoke} from '@/pages/liveshow/config/const';
import { isFromHonorBrowser } from '@/pages/liveshow/service/register';

function ChatBar(props) {
    const { onClickShowModel, setShowTip, status, nid, screen, handleInvoke, isMatrixApp} = props;
    // 是否隐藏底部分享按钮-荣耀白牌 非微信 环境下隐藏分享按钮
    const isHideShareBtn = isFromHonorBrowser && env.browserType !== 'wechat';

    useEffect(() => {
        ['text_box', 'gift', 'share'].forEach((item) => {
            commonShowLog(status, item, nid, screen, '15262', true);
        });
    }, []);

    const clickBottomBar = e => {
        e.preventDefault();
        e.stopPropagation();

        let clickTarget = 'text_box';

        if (hasClass(e.target, 'icon-1')) {
            clickTarget = 'gift';
        }

        if (hasClass(e.target, 'icon-2')) {
            clickTarget = 'share';
        }

        commonClickLog(status, clickTarget, nid, screen, '15262', true);

        // 微信中点击的分享按钮
        if (env.browserType === 'wechat' && hasClass(e.target, 'icon-2')) {
            setShowTip(true);
            return;
        }

        if (supportMcpInvoke) {
            handleInvoke('part');
        }
        else {
            onClickShowModel({
                show: true,
                type: 'reflow',
                from: 'bottomBar',
            });
        }
    };

    return (
        <div className='chat-bar-wrapper' onClick={clickBottomBar}>
            {/* 荣耀白牌不展示 聊一聊 和 礼物 */}
            { !isFromHonorBrowser && <div className='input-like'>聊一聊</div>}
            { !isFromHonorBrowser && <div className='icon-1'></div>}
            {/* 荣耀白牌 非微信 环境下隐藏分享按钮 */}
            {!isHideShareBtn && <div className={`icon-2 ${+isFromHonorBrowser ? 'isFromHonorBrowser' : ''}` }/>}
        </div>
    );
}

export default ChatBar;
