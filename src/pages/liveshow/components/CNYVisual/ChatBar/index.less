@import (reference) '../../../assets/style-util.less';

@height: 120pr;

.chat-bar-wrapper {
    width: 100%;
    height: @height;
    // padding: 0 30pr;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #fff;

    .input-like {
        position: relative;
        box-sizing: border-box;
        height: @height;
        font-size: 48pr;
        line-height: @height;
        padding: 0 48pr;
        flex: 1;
        .bgMask(.23, 60pr);
    }

    .icon-1,
    .icon-2 {
        width: 120pr;
        height: 120pr;
        margin-left: 30pr;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center center;
        flex: none;
    }

    .icon-1 {
        background-image: url(./img/gift.png);
        position: relative;
    }

    .icon-2 {
        background-image: url(./img/share.png);

        &.isFromHonorBrowser {
            margin-left: 0;
        }
    }
}
