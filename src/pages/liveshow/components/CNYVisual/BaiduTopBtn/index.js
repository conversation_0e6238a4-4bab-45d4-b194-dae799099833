/**
 * @file 回流百度APP顶部按钮
 * <AUTHOR>
 * @date 2022-09-19 14:00:18
 */

 import React, { useEffect } from 'react';
 import './index.less';
 import cls from 'classnames';
 import { reflow, isMatrix} from '@/pages/liveshow/utils/reflow';
 import { PageStore } from '@/pages/liveshow/service/register';
 import { commonShowLog, commonClickLog, reflowCallbackLog, cnyClickLog} from '@/pages/liveshow/log';
 import { getAppName} from '@/pages/liveshow/utils/getUrlParam';
 import { env } from '@/utils/env';
 import {isAndroid} from '@baidu/xbox-native/detect';
 import {getWxConfig} from '../../../service/api';
 import {initWxTag} from '@/utils/initWxTag';
 import {isLiveDomain} from '@/utils/index';

 const appName = getAppName();

 function BaiduTopBtn(props) {
     // type代表回流按钮类型：default flot-btn(悬浮样式) top-btn(顶部固定样式)
     const {
         text,
         status,
         nid,
         screen,
         outerSchemeParams = {},
         handleInvoke,
     } = props;

     // 安卓微信场景新增relative属性
     const isShowWeTag = !isMatrix && isAndroid() && env.browserType === 'wechat' && isLiveDomain;
     const reflowScheme = reflow({
        roomId: PageStore.roomId,
        outerSchemeParams,
        status
     }, false, true);

     const onClickFun = e => {
        if (e) {
            e.preventDefault();
            e.stopPropagation();
            commonClickLog(status, 'top_btn', nid, screen, '15263', '1');
            cnyClickLog('2', '15263');
        }

        handleInvoke('part');

        // let reflowConf = {
        //     roomId: PageStore.roomId,
        //     outerSchemeParams,
        //     status
        // };

        // reflow(reflowConf);
     };

     useEffect(() => {
        commonShowLog(status, 'top-btn', nid, screen, '15263', '1');
     }, []);

     useEffect(() => {
         async function addWeixinTag() {
             const targetDom = document.querySelector('.baidu-reflow-btn-wrapper');
             window.console.log(targetDom)
             // 回流百度APP && 仅安卓 && 微信 && DOM节点有效时加入标签
             if (isShowWeTag && targetDom) {
                const res = await getWxConfig();
                if (+res.errno === 0 && res.data && res.data.appId) {
                    const wxInfo = res.data;
                    window.console.log(wxInfo);
                    initWxTag({
                        appName: 'baiduboxapp',
                        targetDom,
                        wxTagId: 'weixin-baidu-reflow-btn-wrapper', // 开放标签的Id
                        wxInfo: wxInfo,
                        scheme: reflowScheme,
                        onClick: function () {
                            window.console.log('click');
                            commonClickLog(status, 'top_btn', nid, screen, '15263', '1');
                            cnyClickLog('2', '15263')

                            // reflowCallbackLog(status, 'reflow_then', '', 0, '15263', true, '', 'wechat_clk');
                        },
                        onSucceed: function (e) {
                            // 微信开放标签成功
                            // reflowCallbackLog(status, 'reflow_then', '', 0, '15263', true, '', 'wechat_suc');
                            window.console.log('success!', e);
                        },
                        onFailed: function (e) {
                            // 微信开放标签失败
                            // reflowCallbackLog(status, 'reflow_then', '', 0, '15263', true, '', 'wechat_fail');
                            window.console.log('failed!', e);
                            onClickFun();
                        }
                    })
                }
             }
         }

        // addWeixinTag();
     }, []);

     const getRootClass = cls({
         ['baidu-reflow-btn-wrapper']: true,
         ['reflow-btn-wrapper']: true,
         ['no-icon']: true
     });

     return (
        <div
            className={getRootClass}
            style={{position: `${isShowWeTag ? 'relative' : 'none'}`}}
         >
            <div className="baidu-reflow-btn" onClick={onClickFun}>
                <span className='text'>{text ? text : `${appName}内播放`}</span>
            </div>
         </div>
     );
 }

 export default BaiduTopBtn;
