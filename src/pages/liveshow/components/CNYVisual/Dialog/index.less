@import (reference) '../../../assets/style-util.less';

.dialog-wrapper {
    .dialog-body {
        margin: 0 81pr;
        padding: 72pr 0 90pr;
        font-size: 48pr;
        line-height: 70pr;
        // text-indent: 2em;
        color: #999;
    }

    .s-dialog {
        font-size: 0 !important;
    }

    .s-dialog-mask {
        max-width: unset;
    }

    .dialog-footer {
        padding: 0 81pr 84pr 81pr;
    }

    .dialog-btn {
        width: 100%;
        height: 132pr;
        font-size: 54pr;
        color: #fff;
        border: none;
        border-radius: 66pr;
        .common-btn-bgcolor;
    }

    .dialog-btn-breath {
        width: 2.497rem;
        height: .482rem;
        border-radius: .33333rem;
        font-size: .23rem;
        background: linear-gradient(89deg, #FF335C 6.41%, #FF4B33 104.27%);
        animation: breath 1s infinite;
    }

    .CNY-bg {
        width: 100%;
        height: 2.44rem;
        background-repeat: no-repeat;
        background-size: 100%;
    }

    .CNY-title {
        color: #000;
        font-family: PingFang SC;
        font-size: .2rem;
        font-style: normal;
        font-weight: 600;
        line-height: .2rem; /* 73.77% */
        margin-top: .24rem;
    }
}

@keyframes breath {
    0% {
        transform: none;
    }

    70% {
        transform: scale(1.1);
    }

    100% {
        transform: none;
    }
}
