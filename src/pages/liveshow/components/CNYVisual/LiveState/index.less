@import (reference) '../../../assets/style-util.less';

.live-state-wrapper {
    height: 54pr;
    display: flex;
    justify-content: center;
    align-items: center;
    .bgMask(.4, 33pr);

    .text {
        padding: 0 21pr 0 9pr;
    }

    // 直播中动画
    .living-icon {
        flex: none;
        width: 54pr;
        height: 54pr;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6pr 12pr;
        background-color: #FF0020;
        border-radius: 50%;

        span {
            display: inline-block;
            flex: none;
            width: 2px;
            height: 20pr;
            background-color: #fff;
            border-radius: 9pr;
        }

        .first {
            animation: living .6s infinite .2s;
        }

        .second {
            animation: living .6s infinite .4s;
        }

        .third {
            animation: living .6s infinite .6s;
        }

        @keyframes living {
            0% {
                height: 40%;
            }

            30% {
                height: 60%;
            }

            50% {
                height: 80%;
            }

            70% {
                height: 60%;
            }

            100% {
                height: 40%;
            }
        }
    }

    // 回放/预告按钮
    &.review::before,
    &.preview::before {
        content: '';
        display: inline-block;
        width: 54pr;
        height: 54pr;
        border: 50%;
        background-size: 100% 100%;
    }

    &.review::before {
        background-image: url(./img/review.png);
    }

    &.preview::before {
        background-image: url(./img/preview.png);
    }
}
