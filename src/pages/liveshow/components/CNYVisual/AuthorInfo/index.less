@import (reference) '../../../assets/style-util.less';

.author-info-wrapper {
    width: 100%;
    position: relative;
    box-sizing: border-box;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 42pr;

    .author-container {
        max-width: 510pr;
        margin-right: 10pr;
        padding: 6pr 15pr 6pr 6pr;
        flex: none;
        .bgMask(.23, 81pr);

        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: bolder;
        position: relative;

        .is-android & {
            max-width: 516pr;
            margin-right: 27pr;
        }

        .avatar {
            flex: none;
            width: 108pr;
            height: 108pr;
            margin-right: 12pr;
        }

        .author-content {
            flex: 1;
            margin-right: 9pr;

            .is-android & {
                margin-right: 15pr;
            }

            .author-name {
                word-break: break-all;
                height: .15rem;
                .text-ellipse(1);

                &.tomas-name {
                    height: .2rem;
                }
            }
        }
    }

    .users-container {
        height: 90pr;
        flex: 1;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        position: relative;

        .users-profile-list {
            display: flex;
            align-items: center;
            justify-content: flex-start;
        }

        .avatar {
            flex: none;
            width: 90pr;
            height: 90pr;
            margin-right: 18pr;
            position: relative;

            &::after {
                border: 1px solid #FCDBA1;
                box-sizing: border-box;
            }

            &:nth-child(2)::after {
                border-color: #C0C8F3;
            }
        }

        .total-users {
            .bgMask(.23, 48pr);

            height: 90pr;
            padding: 0 18pr;
            font-size: 36pr;
            line-height: 90pr;
            text-align: center;
            // .text-ellipse(1);

            font-weight: 700;
            white-space: nowrap;

            .is-android & {
                padding: 0 21pr;
            }
        }
    }
}
