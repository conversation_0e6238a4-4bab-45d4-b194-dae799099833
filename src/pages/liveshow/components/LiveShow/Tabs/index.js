/**
 * @file 直播落地页tabs模块
 * <AUTHOR>
 * @date 2021-11-16 18:07:08
 */
import React, { useState, useEffect } from 'react';
import { Tabs } from '@baidu/nano-react';
import ignoreReflow from '@/pages/liveshow/utils/ignoreReflow';
import thunder from '@/pages/liveshow/utils/thunder';
import GroupRule from '../../GroupBuy/GroupRule';
import MoreTab from '../MoreTab';
import ChatTab from '../ChatTab';
import BottomBar from '../BottomBar';
import './index.less';

function LiveTabs(props) {
    const {
        isGroupPaid,
        showChatTab,
        showMoreTab,
        showProfileTab,
        pageDescription,
        hasReflowBar,
        setShowModel,
        status,
        msgHlsUrl,
        hideName,
        nid,
        screen,
    } = props;
    const [tabIndex, setTabIndex] = useState(0);
    const [recommendFirstShow, setRecommendFirstShow] = useState(false);

    useEffect(() => {
        if (!showMoreTab && !showProfileTab) {
            setRecommendFirstShow(true);
        }
    }, []);

    const onChange = (item) => {
        setTabIndex(item.activeIndex);

        if (item.name === '更多' && !recommendFirstShow) {
            setRecommendFirstShow(true);
        }

        thunder.sendLog({
            tid: 11068,
            logExtra: {
                value: item.name,
            },
        });
    };

    let desProps = {
        dangerouslySetInnerHTML: {
            __html: pageDescription,
        },
    };

    return (
        <Tabs activeIndex={tabIndex} animated onChange={onChange} className='tabs-container'>
            {showChatTab && (
                <Tabs.Panel name='聊天'>
                    <ChatTab
                        className={`tab-content ${!ignoreReflow ? 'chat-tab-content' : ''}`}
                        hideName={hideName}
                        status={status}
                        msgHlsUrl={msgHlsUrl}
                    />
                    {!ignoreReflow && (
                        <BottomBar
                            setShowModel={setShowModel}
                            status={status}
                            nid={nid}
                            screen={screen}
                        />
                    )}
                </Tabs.Panel>
            )}

            {showProfileTab && (
                <Tabs.Panel name='简介'>
                    <div className='tab-content'>
                        <div className='profile' {...desProps} />
                        {isGroupPaid && <GroupRule isPreview={false} />}
                    </div>
                </Tabs.Panel>
            )}

            {showMoreTab && !ignoreReflow && (
                <Tabs.Panel name='更多'>
                    <div className='tab-content'>
                        <MoreTab
                            hasReflowBar={hasReflowBar}
                            setShowModel={setShowModel}
                            status={status}
                            nid={nid}
                            screen={screen}
                            recommendFirstShow={recommendFirstShow}
                        />
                    </div>
                </Tabs.Panel>
            )}
        </Tabs>
    );
}

export default LiveTabs;
