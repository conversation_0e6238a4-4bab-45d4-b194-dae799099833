/*
 * @file 直播落地页 tabs 模块
 * <AUTHOR>
 * @date 2021-11-16 18:07:11
 */
@import (reference) '../../../assets/style-util.less';

.tabs-container {
    font-size: 40pr;
    flex-grow: 1;
    display: flex;
    flex-direction: column;

    // 间距
    .s-tabs-wrap {
        box-sizing: border-box;
        padding: 0 49pr 0 17.5pr;
    }

    .s-tabs-nav {

        // tab Border
        &::after {
            content: "";
            display: block;
            position: absolute;
            left: 0;
            bottom: 0;
            width: 300%;
            height: 1px;
            transform: scale(.333);
            .theme-background(GC34);

            transform-origin: 0 0;
        }

        // 下划线颜色
        .s-tabs-cursor {
            .theme-background(GC77);

            z-index: 3;
        }
    }

    // tab宽度调整
    .s-tab {
        flex: 0 0 171pr;
    }

    .s-tabs-content {
        flex: 1;
    }

    .tab-content {
        width: 100%;
        overflow: scroll;
        -webkit-overflow-scrolling: touch;
        position: absolute;
        // top: 132pr;
        top: 0;
        bottom: 0;
        .theme-background(LC17);

        // 聊天界面有一个底bar，所以距离需要调整下
        &.chat-tab-content {
            bottom: 144pr;
        }
    }

    .profile {
        box-sizing: border-box;
        width: 100%;
        padding: 51pr 51pr 0;
        font-size: 48pr;
        line-height: 1.4;

        .theme-color(GC1);
    }
}