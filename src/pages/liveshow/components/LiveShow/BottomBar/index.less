@import '../../../assets/style-util.less';

.bottom-bar {
    display: flex;
    box-sizing: border-box;
    width: 100%;
    height: 144pr;
    padding: 0 72pr 0 51pr;
    background: #fff;
    align-items: center;
    justify-content: space-between;
    position: absolute;
    left: 0;
    bottom: 0;

    .darkmode & {
        background-color: #080808 !important;
    }

    &::after {
        content: '';
        .border-common-color(#e6e6e6, 0, top);
        z-index: -1;
    }

    .input-like {
        position: relative;
        box-sizing: border-box;
        height: 90pr;
        margin-right: 106pr;
        padding-top: 18pr;
        padding-left: 36pr;
        font-size: 39pr;
        line-height: 54pr;
        flex-grow: 1;
        .theme-color(GC4);

        .android {
            padding-top: 21pr;
        }

        &::after {
            content: '';

            .border-common-color(#666, 9pr, all);
        }
    }

    .icon-1 {
        width: 72pr;
        height: 72pr;
        margin-right: 88pr;
        background: url(./img/i-1.png) 0 0 no-repeat;
        background-size: 72pr 72pr;
    }

    .icon-2 {
        width: 72pr;
        height: 72pr;
        background: url(./img/i-2.png) 0 0 no-repeat;
        background-size: 72pr 72pr;
    }
}