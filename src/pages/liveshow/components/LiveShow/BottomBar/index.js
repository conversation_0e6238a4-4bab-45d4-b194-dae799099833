import React, { useEffect } from 'react';
import './index.less';
import { commonShowLog, commonClickLog } from '@/pages/liveshow/log';
import { hasClass } from '@/utils';

function BottomBar(props) {
    const { setShowModel, status, nid, screen } = props;

    useEffect(() => {
        ['text_box', 'gift', 'share'].forEach((item) => {
            commonShowLog(status, item, nid, screen, '15262');
        });
    }, []);

    const clickBottomBar = e => {
        let clickTarget = 'text_box';

        if (hasClass(e.target, 'icon-1')) {
            clickTarget = 'gift';
        }

        if (hasClass(e.target, 'icon-2')) {
            clickTarget = 'share';
        }

        commonClickLog(status, clickTarget, nid, screen, '15262');

        setShowModel({
            show: true,
            type: 'reflow',
            from: 'bottomBar',
        });
    };
    return (
        <div className='bottom-bar' onClick={clickBottomBar}>
            <div className='input-like'>我来说两句...</div>
            <div className='icon-1' />
            <div className='icon-2' />
        </div>
    );
}

export default BottomBar;
