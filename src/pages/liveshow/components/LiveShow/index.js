/**
 * @file 直播模板入口
 * <AUTHOR>
 * @date 2021-11-12 13:38:53
 */

import React, { useCallback, useEffect, useState } from 'react';
import './index.less';
import Tabs from './Tabs';
import Dialog from './Dialog';
import ReflowBtn from './ReflowBtn';
import AuthorInfo from './AuthorInfo';
import StaticPendant from '../common/StaticPendant';
import LiveVideo from '../common/LiveVideo';
import { env } from '@/utils/env';
import { getAppSchemeHead } from '@/pages/liveshow/utils/getUrlParam';
import { PageStore } from '@/pages/liveshow/service/register';
import { LiveStatus } from '@/pages/liveshow/config/const';
import { logModelChange } from '@/pages/liveshow/utils/thunder';
import { scrollApprove, watchOrientation } from '@/utils/scrollApprove';
import { registerPageHidden } from '@/utils/index';
import { commonShowLog, commonStartTimingLog, commonEndTimingLog } from '@/pages/liveshow/log';

const appSchemeHead = getAppSchemeHead();

function LiveShow({ data, refresh }) {
    const { hasReflowBar, pageTitle, tiebaLiveCmd, isPaid, status, nid, screen } = data;
    const isBiajiahaoPreview = PageStore.queryParams.tag === 'baijiahao' && status === LiveStatus.END_HAS_REVIEW;

    const [showModel, setShowModel] = useState({
        show: false,
        type: 'reflow',
    });

    const changeModel = useCallback(
        (res) => {
            setShowModel({
                show: res.show,
                type: res.type,
            });
            if (env.isMainBox) {
                return;
            }
            logModelChange(res);
        },
        [setShowModel]
    );

    useEffect(() => {
        // 处理ios橡皮筋效果
        scrollApprove(['tab-content']);
        watchOrientation();

        registerPageHidden(
            () => {
                commonEndTimingLog('15266');
            },
            () => {
                commonStartTimingLog(status, '', nid, screen, '15266');
            }
        );
    }, []);

    useEffect(() => {
        commonShowLog(status, '', nid, screen, '15267');
        commonStartTimingLog(status, '', nid, screen, '15266');
    }, [status]);

    return (
        <div className={`white-screen-patrol container ${appSchemeHead}`}>
            {isBiajiahaoPreview ? (
                <LiveVideo {...data} setShowModel={changeModel} refresh={refresh} />
            ) : (
                <React.Fragment>
                    <LiveVideo {...data} setShowModel={changeModel} refresh={refresh} />
                    <ReflowBtn hasReflowBar={hasReflowBar} setShowModel={changeModel} />

                    <div className='title-wrapper'>
                        <div className='title-text'>{pageTitle}</div>
                    </div>

                    <AuthorInfo {...data} refresh={refresh} />

                    <div className='tab-splitter' />

                    <Tabs {...data} setShowModel={changeModel}></Tabs>

                    <StaticPendant {...data} />
                </React.Fragment>
            )}
            <Dialog
                showModel={showModel}
                tiebaLiveCmd={tiebaLiveCmd}
                isPaid={isPaid}
                setShowModel={changeModel}
                outerSchemeParams={data.schemeParams}
            />
        </div>
    );
}

export default LiveShow;
