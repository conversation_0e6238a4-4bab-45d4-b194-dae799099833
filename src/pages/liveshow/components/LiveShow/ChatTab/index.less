/**
* @file index.less
* <AUTHOR>
*/
@import '../../../assets/style-util.less';

.chat-tab-container {
    .chat-tab {
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        padding: 51pr 51pr 0;
        .theme-background(LC17);

        .last-msg {
            position: fixed;
            bottom: 195pr;
            left: 50%;
            display: flex;
            height: 92pr;
            padding: 27pr 51pr;
            font-size: 39pr;
            line-height: 1;
            text-align: center;
            color: #38f;
            border-radius: 100pr;
            background-color: #ecf4ff;
            transform: translateX(-50%);

            .msg-text {
                overflow: hidden;
                max-width: 500pr;
                max-width: 40vw;
                margin-left: 26pr;
                white-space: nowrap;
                text-overflow: ellipsis;
            }

            .arrow {
                position: relative;
                top: 6pr;
                width: 24pr;
                height: 28pr;
                background: url(./img/arr.png) 0 0 no-repeat;
                background-size: 24pr 28pr;
            }

            &::after {
                content: '';
                height: 293% !important;

                .border-common-color(#38f, 100pr, all);
            }
        }
    }

    .chat-item-container {
        display: flex;
        margin-bottom: 45pr;

        .avatar-container {
            margin-right: 36pr;
            .common-avatar(105pr, 105pr);
        }

        .chat-item-content {
            display: flex;
            flex-direction: column;
            align-items: flex-start;

            .chat-info {
                display: flex;
                margin-bottom: 22pr;
                line-height: 1;
                align-items: flex-end;

                .username {
                    margin-right: 24pr;
                    font-size: 42pr;

                    .theme-color(LC5);
                }

                .publish-time {
                    font-size: 36pr;

                    .theme-color(LC7);
                }
            }
        }

        .chat-bubble-container {
            position: relative;
            left: -15pr;
        }
    }

    .chat-bubble-container {
        position: relative;
        padding-left: 23pr;

        .text-area {
            position: relative;
            max-width: 700pr;
            padding: 24pr 66pr 24pr 35pr;
            font-size: 48pr;
            line-height: 1.5;
            text-align: justify;
            word-break: break-all;
            border-radius: 36pr;

            .theme-background(GC16);
            .theme-color(LC1);

            &::after {
                content: '';
                z-index: 0;

                .border-common-color(#e6e6e6, 36pr, all);
            }
        }

        .response {
            .theme-color(LC6);
        }

        .triangle {
            position: absolute;
            z-index: 1;
            top: 24pr;
            left: 0pr;
            width: 29pr;
            height: 62pr;
            background-image: url(./img/triangle-light.png);
            background-repeat: no-repeat;
            background-position: 0 0;
            background-size: 29pr 62pr;

            .darkmode & {
                background-image: url(./img/triangle-dark.png);
            }
        }

        .triangle-cover {
            position: absolute;
            z-index: 2;
            top: 24pr;
            left: 26pr;
            display: block;
            width: 10pr;
            height: 60pr;

            .theme-background(GC16);
        }
    }

    .chat-bubble.chat-bubble-container::after,
    .triangle,
    .triangle-cover {
        .bdhiphop & {
            background-image: url(./img/triangle-dark.png);
        }
    }
}