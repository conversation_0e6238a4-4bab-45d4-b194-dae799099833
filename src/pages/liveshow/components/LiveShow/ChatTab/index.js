import React, { useEffect, useRef, useState } from 'react';
import { Image } from '@baidu/nano-react';
import { useThrottleFn } from 'ahooks';
import cls from 'classnames';
import ignoreReflow from '@/pages/liveshow/utils/ignoreReflow';
import { PageErrorStatus } from '@/pages/liveshow/config/const';
import ErrorPage from '@/pages/liveshow/components/common/ErrorStatus';
import Chat, { MessageType } from '@/pages/liveshow/utils/chat';

import './index.less';

const showName = (item, hideName) => {
    if (hideName) {
        return `${item.name.slice(0, 1)}***`;
    }
    return item.name;
};

function ChatItem({ item }) {
    let { avatarSrc, username, publishTime, content, responseUser } = item;
    return (
        <div className='chat-item-container'>
            <div className='avatar-container'>
                <Image className='user-avatar' url={avatarSrc} />
            </div>
            <div className='chat-item-content'>
                <div className='chat-info'>
                    <div className='username'>{username}</div>
                    <div className='publish-time'>{publishTime}</div>
                </div>
                <div className='chat-bubble chat-bubble-container'>
                    <div className='text-area'>
                        {responseUser && <span className='response'>{`回复:${responseUser}:&nbsp;`}</span>}
                        {content}
                    </div>
                    <div className='triangle' />
                    <div className='triangle-cover' />
                </div>
            </div>
        </div>
    );
}

function ChatTab({ status, msgHlsUrl, hideName, className = '' }) {
    const $msgBox = useRef();
    const [chatList, setChatList] = useState([]);
    const [lastMsg, setLastMsg] = useState('');
    const chat = useRef(Chat.getInstance());
    const isScroll = useRef(false);

    const scrollToBottom = () => {
        const scrollPanel = $msgBox.current;
        if (scrollPanel) {
            scrollPanel.scrollTop = scrollPanel.scrollHeight - scrollPanel.clientHeight;
        }
    };

    const { run } = useThrottleFn(
        () => {
            const target = $msgBox.current;
            const { scrollHeight, clientHeight, scrollTop } = target;
            if (scrollTop + 10 >= scrollHeight - clientHeight) {
                isScroll.current = false;
                setLastMsg('');
            } else {
                isScroll.current = true;
            }
        },
        { wait: 60 }
    );

    useEffect(() => {
        chat.current.start({
            m3u8Link: msgHlsUrl,
            status: status,
        });

        chat.current.on(MessageType.MESSAGE, (list) => {
            setChatList(() => {
                // eslint-disable-next-line max-nested-callbacks
                let newList = list.map((item) => {
                    return {
                        msgId: item.msgId,
                        avatarSrc: item.portrait,
                        username: showName(item, hideName),
                        publishTime: item.timestr,
                        content: item.word,
                        responseUser: item.responseUser,
                    };
                });
                return newList;
            });
            if (!isScroll.current) {
                scrollToBottom();
            } else {
                let newMsg = list[list.length - 1];
                if (newMsg && newMsg.word) {
                    setLastMsg(newMsg.word);
                }
            }
        });
    }, []);

    return (
        <div className={`chat-tab-container ${className}`} ref={$msgBox} onScroll={run}>
            <div className='chat-tab'>
                {chatList.length > 0 ? (
                    <div className='chat-scroll'>
                        {chatList.map((item) => (
                            <ChatItem key={item.msgId} item={item} />
                        ))}
                    </div>
                ) : (
                    <ErrorPage error={{ errCode: PageErrorStatus.NO_CHAT }} />
                )}
                {lastMsg && (
                    <div onClick={scrollToBottom}>
                        <div className='last-msg'>
                            <div className='arrow' />
                            <div className='msg-text'>{lastMsg}</div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}

export default ChatTab;
