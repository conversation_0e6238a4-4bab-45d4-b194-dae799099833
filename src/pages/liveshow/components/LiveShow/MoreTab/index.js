import React, { useEffect } from 'react';
import { env } from '@/utils/env';
import { Image, Flash } from '@baidu/nano-react';
import { useRequest } from 'ahooks';
import { getAppName, h5UrlForRoomId, getAppSchemeHead } from '@/pages/liveshow/utils/getUrlParam';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import ErrorPage from '@/pages/liveshow/components/common/ErrorStatus';
import { moreRecommand } from '@/pages/liveshow/service/model';
import { reflow } from '@/pages/liveshow/utils/reflow';
import { goNativeLiveRoom } from '@/pages/liveshow/utils/openNativePage';
import thunder from '@/pages/liveshow/utils/thunder';
import ReflowBtn from '../ReflowBtn';
import { commonShowLog, commonClickLog } from '@/pages/liveshow/log';
import './index.less';

const appName = getAppName();

const isBox = env.isBox || env.isLiteBox;

const appSchemeHead = getAppSchemeHead();

function MoreItem({ item, index, clickItem }) {
    let { name, title, imgSrc, count, roomId } = item;

    return (
        <div className='more-item-container' onClick={(ev) => clickItem(ev, roomId, index)}>
            <div className='content'>
                <div className='text-content'>
                    <div className='title'>{title}</div>
                    <div className='info-wrapper'>
                        <div className='live-text'>直播</div>
                        {!isBox ? (
                            <div className={`open-bd ${appSchemeHead}`}>
                                打开
                                {appName}
                                APP
                            </div>
                        ) : (
                            <div className='join-num'>{count}</div>
                        )}
                        <div className='name'>{name}</div>
                    </div>
                </div>
                <Image url={imgSrc} size='3:2' className='item-image' />
            </div>
        </div>
    );
}

function MoreTab(props) {
    const { hasReflowBar, setShowModel, status, nid, screen, recommendFirstShow } = props;
    const { loading, data, error } = useRequest(moreRecommand);

    const clickItem = (event, roomId, index) => {
        // 回流到百度
        event.preventDefault();

        commonClickLog(status, 'morelive_btn', nid, screen, '15263');

        if (env.isBaiduChillin) {
            window.location.href = h5UrlForRoomId(roomId);
        } else if (env.isLiteBox) {
            boxjsUtils.openEasyBrowse(h5UrlForRoomId(roomId));
        } else if (env.isMainBox) {
            goNativeLiveRoom({
                roomId,
                outerSchemeParams: data[index].schemeParams,
            }, true); // 对改版前的列表项跳转同步新增标识
        } else {
            reflow({
                roomId,
                outerSchemeParams: data[index].schemeParams,
            });
        }

        thunder.sendLog({
            tid: 11069,
            logExtra: {
                m_nid: roomId,
                m_roomid: roomId,
                r_type: '2',
                value: `${index + 1}`,
            },
        });
    };

    useEffect(() => {
        if (data && recommendFirstShow) {
            commonShowLog(status, 'morelive_btn', nid, screen, '15263');
        }
    }, [data, recommendFirstShow]);

    return (
        <div className='more-tab-container'>
            {loading ? (
                <div className='flash-wrapper'>
                    <Flash />
                </div>
            ) : error ? (
                <ErrorPage />
            ) : (
                <div>
                    <div className='more-item-list'>
                        {data.map((item, index) => (
                            <MoreItem key={item.roomId} item={item} index={index} clickItem={clickItem} />
                        ))}
                    </div>
                    <ReflowBtn
                        setShowModel={setShowModel}
                        hasReflowBar={hasReflowBar}
                        from='moreTabBtn'
                        text={`打开${appName}APP看更多直播`}
                    />
                </div>
            )}
        </div>
    );
}

export default MoreTab;
