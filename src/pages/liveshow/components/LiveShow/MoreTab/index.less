/*
 * <AUTHOR>
 * @date 2021-11-09 23:00:28
 */

@import '../../../assets/style-util.less';

.more-tab-container {
    position: absolute;
    top: 0;
    left: 0;
    overflow: scroll;
    -webkit-overflow-scrolling: touch;
    width: 100%;
    height: 100%;

    // 页面整体加载态的wrapper样式
    .flash-wrapper {
        display: flex;
        width: 100%;
        height: 100%;
        align-items: center;
        justify-content: center;
    }

    .more-item-list {
        position: relative;

        .more-item-container {
            box-sizing: border-box;
            width: 100%;
            height: 350pr;
            padding: 51pr 51pr 0 51pr;

            &:active {
                .theme-background(BC75);
            }

            .s-image-default {
                background-size: contain !important;
            }

            .content {
                position: relative;
                box-sizing: border-box;
                width: 100%;
                height: 100%;
                padding-bottom: 51pr;
                display: flex;
                justify-content: space-between;
                align-items: center;

                &::after {
                    content: '';
                    .border-common-theme(GC34, 0, bottom);
                }

                .text-content {
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-right: 24pr;
                }

                .title {
                    position: relative;
                    top: -7pr;
                    font-size: 57pr;
                    line-height: 1.3;
                    margin-right: 24pr;
                    .theme-color(GC1);
                    .text-ellipse(2);
                }

                .info-wrapper {
                    line-height: 1.2;
                    font-size: 36pr;
                    display: flex;

                    .live-text {
                        white-space: nowrap;
                        .theme-color(LC18);
                    }

                    .open-bd {
                        white-space: nowrap;
                        .theme-color(GC7);

                        &.bdtiebalive {
                            color: #7559ff;
                        }

                        &.youjia {
                            color: #00cecf;
                        }

                        &.baidudict {
                            color: #00cb8a;
                        }

                        &.fortunecat {
                            color: #c9a687;
                        }
                    }

                    .join-num,
                    .name {
                        .theme-color(GC4);
                    }

                    .join-num,
                    .open-bd,
                    .name {
                        margin-left: 24pr;
                    }

                    .name {
                        .text-ellipse(1);
                    }
                }

                .item-image {
                    flex-shrink: 0;
                    width: 372pr;
                    height: 100%;
                    border-radius: 9pr;
                    overflow: hidden;
                }
            }
        }
    }
}