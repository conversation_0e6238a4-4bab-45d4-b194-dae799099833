import React, { useEffect } from 'react';
import './index.less';
import FollowBtn from '@/pages/liveshow/components/common/FollowBtn';
import useTotalUsers from '@/pages/liveshow/components/common/TotalUsers';
import thunder from '@/pages/liveshow/utils/thunder';
import { Image } from '@baidu/nano-react';
import { reflow } from '@/pages/liveshow/utils/reflow';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import { huaweiFeed } from '@/pages/liveshow/utils/ignoreReflow';
import { PageStore } from '@/pages/liveshow/service/register';
import { commonShowLog, commonClickLog } from '@/pages/liveshow/log';

function AuthorInfo(props) {
    if (huaweiFeed) {
        return null;
    }
    const totalUsers = useTotalUsers(props.totalUsers);

    let {
        followStatus,
        follow_id,
        follow_type,
        isLogin,
        refresh,
        hostAvatar,
        hostName,
        isShowWatchingNum,
        template,
        status,
        nid,
        screen
    } = props;

    useEffect(() => {
        commonShowLog(status, 'head', nid, screen, '15264');
    }, []);

    const isFortunecat = PageStore.queryParams.source === 'fortunecat';

    const clickHead = () => {
        commonClickLog(status, 'head', nid, screen, '15264');

        if (isFortunecat) {
            reflow({ roomId: PageStore.roomId });
        } else {
            thunder.sendLog({
                tid: 11070,
                cst: 2,
                logExtra: {
                    click_type: 'author',
                    page: 'live',
                    id: follow_id,
                },
            });
            boxjsUtils.toAuthorPage(follow_id, follow_type === 'ugc');
        }
    };

    const onClickFollow = () => {
        if (isFortunecat) {
            reflow({ roomId: PageStore.roomId });
            return true;
        }
    };

    const followPros = {
        sourceLive: true,
        followStatus,
        template,
        third_id: follow_id,
        type: follow_type,
        is_login: isLogin,
        refresh,
        onClickFollow,
        status,
        nid,
        screen,
    };

    return (
        <div className='author-info-container'>
            <div className='info-area' onClick={clickHead}>
                <div className='avatar-container'>
                    <Image url={hostAvatar} />
                </div>
                <div className='author-info'>
                    <div className='author-name'>{hostName}</div>
                    {isShowWatchingNum && (
                        <div className='follow-num'>
                            {totalUsers}
                            人参与
                        </div>
                    )}
                </div>
            </div>
            <FollowBtn {...followPros} />
        </div>
    );
}

export default AuthorInfo;
