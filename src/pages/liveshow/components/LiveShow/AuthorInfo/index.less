@import (reference) '../../../assets/style-util.less';

.author-info-container {
    position: relative;
    display: flex;
    box-sizing: border-box;
    width: 100%;
    height: 200pr;
    padding: 0 51pr;
    line-height: 1;
    align-items: center;
    flex-shrink: 0;
    justify-content: space-between;

    &::before {
        content: '';
        z-index: 1;
        .border-common-theme(GC34, 0, top);
    }

    .info-area {
        position: relative;
        z-index: 2;
        display: flex;
        height: 100%;
        align-items: center;
        flex-grow: 1;
        justify-content: flex-start;

        .avatar-container {
            margin-right: 22pr;
            .common-avatar(105pr, 105pr);
        }

        .author-info {
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            height: 100%;
            padding: 51pr 0 53pr;
            align-items: flex-start;
            flex-grow: 1;
            justify-content: center;

            .author-name {
                margin-bottom: 19pr;
                font-size: 48pr;
                line-height: 1.2;
                flex-shrink: 0;
                .text-ellipse(1);
                .theme-color(GC1);
            }

            .follow-num {
                font-size: 38pr;

                .theme-color(GC4);
            }
        }
    }
}