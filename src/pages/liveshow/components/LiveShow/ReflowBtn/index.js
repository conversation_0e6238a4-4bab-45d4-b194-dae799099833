/**
 * @file 落地页回流按钮逻辑
 * <AUTHOR>
 * @date 2021-11-16 15:13:18
 */
import React from 'react';
import cls from 'classnames';
import { env, isAndroidSougou } from '@/utils/env';
import ignoreReflow from '@/pages/liveshow/utils/ignoreReflow';
import { getAppName } from '@/pages/liveshow/utils/getUrlParam';
import './index.less';

const appName = getAppName();

// 搜狗游览器兼容问题
const sougou = isAndroidSougou();

function ReflowBtn({ hasReflowBar, setShowModel, from, text }) {
    const clickToBaidu = () => {
        setShowModel({
            show: true,
            type: 'reflow',
            from,
            value: '3',
        });
    };
    const show = hasReflowBar && !ignoreReflow && !env.isBaiduChillin && !env.isTomas;

    return show ? (
        <div
            className={cls({
                'down-load-btn': true,
                'down-load-btn-env': sougou && !text,
            })}
            onClick={clickToBaidu}
        >
            {text ? text : `打开${appName}APP，查看更多精彩内容`}
        </div>
    ) : (
        ''
    );
}

export default ReflowBtn;
