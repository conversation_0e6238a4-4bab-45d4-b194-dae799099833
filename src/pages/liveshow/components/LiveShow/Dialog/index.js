/**
 * @file 直播落地页弹窗组件
 * <AUTHOR>
 * @date 2021-11-16 14:28:18
 */
import React, { useEffect } from 'react';
import { Button, Dialog } from '@baidu/nano-react';
import { getAppName, getAppSchemeHead } from '@/pages/liveshow/utils/getUrlParam';
import { reflow } from '@/pages/liveshow/utils/reflow';
import { PageStore } from '@/pages/liveshow/service/register';
import style from './index.less';
import { commonShowLog, commonClickLog } from '@/pages/liveshow/log';

const appName = getAppName();

const textMap = {
    reflow: {
        align: 'left',
        title: '直播邀请',
        content: `        该直播使用最新版本${appName}APP观看体验更好，我在${appName}APP等你，快来看我的直播吧。`,
    },
    pay: {
        align: 'center',
        title: '购买付费直播',
        content: `该直播为付费内容\n请打开最新版${appName}APP进行购买`,
    },
};

function LiveDialog(props) {
    const {
        showModel,
        setShowModel,
        isPaid,
        tiebaLiveCmd,
        status,
        nid,
        screen,
        outerSchemeParams = {},
    } = props;
    const { show, type } = showModel;

    if (!show) {
        return '';
    }

    useEffect(() => {
        commonShowLog(status, 'windows_btn', nid, screen, '15263');
    }, []);

    const onClose = () => {
        setShowModel({
            type: type,
            show: false,
            value: '2',
        });
    };

    const info = textMap[type];

    const onClick = () => {
        commonClickLog(status, 'windows_btn', nid, screen, '15263');

        setShowModel({
            type: type,
            show: false,
            value: '1',
        });

        let obj = {
            roomId: PageStore.roomId,
            tiebaCmd: tiebaLiveCmd,
            isPaid: isPaid,
            outerSchemeParams,
        };
        if (type === 'pay') {
            obj.queryParams = {
                pop_name: 'pay_live_payment_plugin',
            };
        }

        reflow(obj);
    };

    return (
        <Dialog
            className="dialog-container"
            title={info.title}
            closeType='inside'
            showCancel
            showClose
            onClose={onClose}
        >
            <div className={style.dialogBody} style={{ textAlign: info.align }}>
                {info.content}
            </div>
            <div className={style.dialogFooter}>
                <Button type='primary' className={style.dialogBtn} onClick={onClick}>
                    打开{appName}APP
                </Button>
            </div>
        </Dialog>
    );
}

export default LiveDialog;
