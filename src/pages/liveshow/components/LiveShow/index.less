/*
 * @file 直播落地页样式入口
 * <AUTHOR>
 * @date 2021-11-09 23:11:03
 */

@import (reference) '../../assets/style-util.less';

body.landscape {
    overflow-y: scroll;

    #root {
        overflow-y: scroll;
    }

    .tabs-container {
        min-height: 700pr;
    }
}

.hidden {
    display: none;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100%;

    &.bdhiphop {
        background: #111;
    }
}

.title-wrapper {
    box-sizing: border-box;
    width: 100%;
    padding: 40pr 51pr;

    .title-text {
        font-size: 54pr;
        font-weight: 800;
        line-height: 78pr;

        .text-ellipse(2);
        .theme-color(GC1);
    }
}

.tab-splitter {
    width: 100%;
    height: 24pr;
    min-height: 24pr;

    .theme-background(GC33);
}

.bdhiphop {
    .title-wrapper {
        .title-text {
            color: #FFF;
        }
    }

    .author-info-container {
        .info-area {
            .author-info {
                .author-name {
                    color: #FFF;
                }
            }
        }
    }

    .tab-splitter {
        background: rgba(255, 255, 255, .06);
    }

    .s-tab {
        color: rgba(255, 255, 255, .65) !important;
    }

    .s-tab.s-tab-active {
        color: #FFF !important;
    }

    .s-tabs-cursor {
        background-color: #5DFF6C !important;
    }

    .tab-content .profile,
    .s-dialog-header {
        color: #FFF !important;
    }

    .s-tabs-nav::after {
        display: none;
    }

    .chat-tab,
    .s-dialog {
        background-color: #111 !important;
    }

    .tab-content,
    .bottom-bar {
        background-color: #111;
    }

    .chat-bubble.chat-bubble-container::after,
    .triangle-cover {
        background-color: #333;
    }

    .chat-bubble-container .text-area {
        background-color: #333;
        color: #FFF;
    }

    .chat-bubble-container .text-area::after {
        border-color: #333;
    }

    .input-like {
        background-color: #666;
        color: #FFF;
        margin-right: 0;
    }

    .icon-1,
    .icon-2 {
        display: none;
    }

    .more-item-container .content .text-content {
        .title {
            color: #FFF;
        }

        .open-bd.bdhiphop {
            color: #5DFF6C;
        }
    }

    .more-item-container:active {
        background-color: #333;
    }
}