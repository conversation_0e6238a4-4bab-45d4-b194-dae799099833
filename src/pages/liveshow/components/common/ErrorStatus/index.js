/**
 * @file 错误样式模块 (新旧版本共用)
 * <AUTHOR>
 * @date 2021-11-09 20:37:48
 */
import React from 'react';
import { PageErrorStatus } from '@/pages/liveshow/config/const';
import './index.less';

export default function ErrorPage({ error }) {
    let errorCode = error && +error.errCode;
    let isNoChat = errorCode === PageErrorStatus.NO_CHAT;
    let pageErrorMessage = '';
    let pageErrorType = '';
    switch (errorCode) {
        case PageErrorStatus.NOT_START:
            pageErrorMessage = '直播未开始，请稍后再试';
            pageErrorType = 'content';
            break;
        case PageErrorStatus.ERROR_END:
            pageErrorMessage = '该内容已过期清理';
            pageErrorType = 'content';
            break;
        case PageErrorStatus.CROWDED:
            pageErrorMessage = '直播已挤爆 请稍后再试';
            pageErrorType = 'wifi';
            break;
        case PageErrorStatus.TIEBA_END:
            pageErrorMessage = '本场直播已结束';
            pageErrorType = 'content';
            break;
        case PageErrorStatus.NO_CHAT:
            pageErrorMessage = '还没有人发言，快来抢沙发~';
            pageErrorType = 'comment';
            break;
        default:
            pageErrorMessage = '网络不给力，请稍后重试';
            pageErrorType = 'wifi';
            break;
    }

    return (
        <div className={`white-screen-patrol error-wrapper ${pageErrorType}`}>
            <div className={`icon icon-${pageErrorType || 'content'}`} />
            <div className={`g-error-text ${isNoChat ? 'mt-60' : ''}`}>{pageErrorMessage}</div>
        </div>
    );
}
