@import (reference) '~@baidu/nano-theme/index.less';

.error-wrapper {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .icon {
        background-position: 0 0;
        background-repeat: no-repeat;
    }

    .icon-comment {
        width: 178pr;
        height: 172pr;
        background-image: url(./icons/no-comment-light.png);
        background-size: 178pr 172pr;

        .dark & {
            background-image: url(./icons/no-comment-dark.png);
        }
    }

    .icon-content {
        width: 178pr;
        height: 190pr;
        background-image: url(./icons/content-light.png);
        background-size: 178pr 190pr;

        .dark & {
            background-image: url(./icons/content-dark.png);
        }
    }

    .icon-wifi {
        width: 190pr;
        height: 141pr;
        background-image: url(./icons/wifi-light.png);
        background-size: 190pr 141pr;

        .dark & {
            background-image: url(./icons/wifi-dark.png);
        }
    }

    .icon-close {
        width: 46pr;
        height: 46pr;
        background-image: url(./icons/close-light.png);
        background-size: 46pr 46pr;

        .dark & {
            background-image: url(./icons/close-dark.png);
        }
    }

    .g-error-text {
        margin-top: 100pr;
        font-size: 48pr;
        line-height: 1;
        .theme-color(GC3);
    }

    .mt-60 {
        margin-top: 60pr;
    }
}