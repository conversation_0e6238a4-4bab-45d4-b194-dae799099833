@import '../../../assets/style-util.less';

.flex-center-column {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.absolute() {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.baijiahao-preview {
    .mp-nostart {
        .mp-bottom {
            display: flex !important;
            opacity: 1;
            visibility: visible;
            transition: none;
        }
    }
}

.live-video-container {
    position: relative;
    width: 100%;
    padding-top: 56.3%; // 16: 9
    line-height: 1;

    .content {
        .absolute();

        #video-box {
            .absolute();

            z-index: 1;
        }

        .not-playing-wrapper {
            position: relative;
            z-index: 2;
            width: 100%;
            height: 100%;

            .mask {
                .absolute();

                z-index: 2;
                background: rgba(0, 0, 0, .65);
            }

            .center {
                position: absolute;
                z-index: 3;
                top: 50%;
                left: 50%;
                width: 100%;
                transform: translate(-50%, -50%);
            }

            .end-text {
                font-size: 60pr;

                .theme-color(GC6);
            }

            .mb31 {
                margin-bottom: 31pr;
            }

            .tips {
                font-size: 42pr;
                white-space: nowrap;
                color: #999;
            }

            .end-no-preview {
                .flex-center-column;

                .watched-num {
                    margin-bottom: 51pr;
                }

                .review-btn {
                    position: relative;
                    box-sizing: border-box;
                    width: 228pr;
                    height: 84pr;
                    padding-top: 21pr;
                    font-size: 42pr;
                    line-height: 1;
                    text-align: center;

                    .theme-color(GC6);

                    &::after {
                        content: '';

                        .border-common-theme(GC6, 15pr, all);
                    }
                }
            }

            .end-has-preview {
                .flex-center-column;
            }

            .paid-container {
                .flex-center-column;

                .paid-title {
                    margin-bottom: 45pr;
                    font-size: 42pr;

                    .theme-color(GC6);
                }

                .paid-price-container {
                    margin-bottom: 81pr;
                    font-size: 36pr;

                    .theme-color(GC62);

                    .discount {
                        margin-left: 21pr;
                        text-decoration: line-through;

                        .theme-color(GC5);
                    }
                }

                .paid-btn {
                    width: 270pr;
                    height: 84pr;
                    font-size: 42pr;
                    line-height: 84pr;
                    text-align: center;
                    border-radius: 42pr;

                    .theme-background(GC62);
                    .theme-color(GC6);
                }
            }

            .group-container {
                .flex-center-column;

                color: #fff;

                .group-title {
                    margin-bottom: 40pr;
                    font-family: PingFangSC-Medium;
                    font-size: 20px;
                }

                .group-tip {
                    opacity: .6;
                    font-family: PingFangSC-Regular;
                    font-size: 14px;
                    margin-bottom: 55pr;
                }

                .group-btn {
                    min-width: 504pr;
                    height: 144pr;
                    background-image: linear-gradient(180deg, #fff 0%, #fe9 100%);
                    border-radius: 24px;
                    font-family: PingFangSC-Semibold;
                    font-size: 18px;
                    line-height: 144pr;
                    color: #f35;
                    text-align: center;
                    padding: 0 6pr;
                    box-sizing: content-box;
                }
            }
        }
    }
}
