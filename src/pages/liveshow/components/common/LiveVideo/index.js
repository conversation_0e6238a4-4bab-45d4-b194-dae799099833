/**
 * @file 基于media-player的移动端播放器组件
 * <AUTHOR>
 * @date 2021-11-23 20:33:27
 */
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { env } from '@/utils/env';
import { LiveStatus } from '@/pages/liveshow/config/const';
import { Image } from '@baidu/nano-react';
import thunder from '@/pages/liveshow/utils/thunder';
import { PageStore } from '@/pages/liveshow/service/register';
import useTotalUsers from '@/pages/liveshow/components/common/TotalUsers';
import MediaPlayer from '@baidu/media-player/dist/mobile-player';

// 如果是pc调试,请引入pc播放器包,默认引入移动包不包含解码器无法pc播放
// import MediaPlayer from '@baidu/media-player';
import Chat, { MessageType } from '@/pages/liveshow/utils/chat';
import './index.less';

const VideoMask = (props) => {
    const { isPaid, isGroupPaid, status, coverImg, iOSPrice, androidPrice, setShowModel, clickReview } = props;
    const totalUsers = useTotalUsers(props.totalUsers);
    const isShowEndNoPreview = status === LiveStatus.END_NO_REVIEW;
    const isShowEndHasPreview = status === LiveStatus.END_HAS_REVIEW;
    const price = env.isIOS ? iOSPrice : androidPrice;

    const clickPaidBtn = () => {
        setShowModel({
            show: true,
            type: 'pay',
        });

        thunder.sendLog({
            tid: 11169,
            cst: 2,
        });
    };

    const reviewOnClick = () => {
        clickReview && clickReview();
    };

    const renderVideoStatus = () => {
        if (isGroupPaid) {
            return (
                <div className='group-container'>
                    <div className='group-title'>付费直播拼团活动已结束</div>
                    <div className='group-tip'>原价购入不计入分享领现金任务中</div>
                    <div className='group-btn' onClick={clickPaidBtn}>
                        原价购买{price}
                    </div>
                </div>
            );
        } else if (isPaid) {
            const { iOSDiscountPrice, isDiscount, androidDiscountPrice } = props;
            const discountPrice = env.isIOS ? iOSDiscountPrice : androidDiscountPrice;
            return (
                <div className='paid-container'>
                    <div className='paid-title'>当前内容为付费内容，请购买后观看</div>
                    <div className='paid-price-container'>
                        {isDiscount ? (
                            <div>
                                {discountPrice}
                                <span className='discount'>{price}</span>
                            </div>
                        ) : (
                            <div>{price}</div>
                        )}
                    </div>
                    <div className='paid-btn' onClick={clickPaidBtn}>
                        购买直播
                    </div>
                </div>
            );
        } else if (isShowEndHasPreview) {
            const { isShowWatchingNum } = props;
            return (
                <div className='end-no-preview'>
                    <div className='end-text mb31'>直播已结束</div>
                    {isShowWatchingNum && (
                        <div className='watched-num tips'>
                            {totalUsers}
                            人看过
                        </div>
                    )}
                    <div className='review-btn' onClick={reviewOnClick}>
                        观看回放
                    </div>
                </div>
            );
        } else if (isShowEndNoPreview) {
            return (
                <div className='end-has-preview'>
                    <div className='end-text mb31'>直播已结束</div>
                    <div className='watched-num tips mb31'>
                        {totalUsers}
                        人看过
                    </div>
                    <div className='tips'>小编正在努力剪辑回放，敬请期待</div>
                </div>
            );
        } else {
            return '';
        }
    };

    const isBiajiahaoPreview = PageStore.queryParams.tag === 'baijiahao';

    const show = !isBiajiahaoPreview && (isGroupPaid || isPaid || isShowEndNoPreview || isShowEndHasPreview);

    return show ? (
        <div className='not-playing-wrapper'>
            {coverImg && <Image url={coverImg} size='16:9' />}
            <div className='mask' />
            <div className='center'>{renderVideoStatus()}</div>
        </div>
    ) : (
        ''
    );
};

const VideoLive = (props) => {
    const { status, liveHlsUrl, isPaid, isGroupPaid, refresh, coverImg } = props;
    const chat = useRef(Chat.getInstance());
    const player = useRef();
    const [showMask, setShowMask] = useState(true);

    const clickReview = useCallback(() => {
        if (player.current) {
            setShowMask(false);
            player.current.play();
        }
    }, [player.current]);

    useEffect(() => {
        // 付费或者无播放链接不处理
        const isShowEndNoPreview = status === LiveStatus.END_NO_REVIEW;

        if (isPaid || isGroupPaid || isShowEndNoPreview || !liveHlsUrl) {
            return;
        }

        const { start: videoStartTime, end: videoEndTime } = PageStore.queryParams;
        const isNeedSetVideoStarTime = +videoStartTime >= 0 && +videoEndTime >= 0; // 是否需要视频切片，start 有可能是零

        const isLive = status === LiveStatus.LIVEING;

        player.current = new MediaPlayer({
            id: 'video-box',
            playsinline: true,
            videoInit: true,
            isLive: isLive,
            url: liveHlsUrl,
            poster: coverImg ? coverImg : '',
            useMobile: true,
            errorReload: true,
            videoStartTime: isNeedSetVideoStarTime ? +videoStartTime : 0,
            videoEndTime: isNeedSetVideoStarTime ? +videoEndTime : 0,
            attribute: {
                'x5-video-player-type': 'h5-page',
                't7-video-player-type': 'inline',
                'x5-playsinline': true,
                'x-webkit-airplay': 'allow',
                'controlsList': 'nodownload',
            },
        });
        const onEnd = () => {
            if (isLive) {
                player.current.destroy();
                player.current = null;
                refresh && refresh();
            } else {
                setShowMask(true);
            }
        };
        const onPlaying = () => {
            thunder.sendLog({ tid: 11064, cst: 2, logExtra: { status: '1' } });
        };

        const changeUrl = (url) => {
            if (player.current) {
                player.current.src = url;
            }
        };

        chat.current.on(MessageType.LIVE_END, onEnd);
        chat.current.on(MessageType.LIVE_CHANGE_FLOW, changeUrl);

        player.current.on('ended', onEnd);
        player.current.once('playing', onPlaying);

        return () => {
            if (player.current) {
                player.current.off('ended', onEnd);
                player.current.destroy();
            }
            chat.current.off(MessageType.LIVE_END, onEnd);
            chat.current.off(MessageType.LIVE_CHANGE_FLOW, changeUrl);
        };
    }, [status]);
    const isBiajiahaoPreview = PageStore.queryParams.tag === 'baijiahao';

    return (
        <div className={`live-video-container`}>
            <div className='content'>
                {showMask && <VideoMask {...props} clickReview={clickReview} />}
                <div id='video-box' className={isBiajiahaoPreview ? 'baijiahao-preview' : ''}></div>
            </div>
        </div>
    );
};

export default VideoLive;
