/**
 * @file 挂件组件 (新旧版本共用)
 * <AUTHOR>
 * @date 2021-11-09 20:37:48
 */

import './index.less';
import React, { useEffect, useState } from 'react';
import { env } from '@/utils/env';
import { invokeApp } from '@baidu/ug-invoke-app';
import { getAppSchemeHead } from '@/pages/liveshow/utils/getUrlParam';
import { commonShowLog, commonClickLog } from '@/pages/liveshow/log';

function CloseSVG() {
    /* eslint-disable max-len */
    const d = `M512 451.669333l211.2-211.2 60.330667 60.330667-211.2 211.2 211.2 211.2-60.330667 60.330667-211.2-211.2-211.2 211.2-60.330667-60.330667 211.2-211.2-211.2-211.2L300.8 240.469333z`;
    /* eslint-enable max-len */
    return (
        <svg
            t='1596550256614'
            className='icon'
            viewBox='0 0 1024 1024'
            version='1.1'
            xmlns='http://www.w3.org/2000/svg'
            p-id='3038'
            width='16'
            height='16'
        >
            <path d={d} p-id='3039' fill='#ffffff' />
        </svg>
    );
}

function StaticPendant(props) {
    const { adPlace = {}, tiebaLiveCmd, className, status, nid, screen, isNewVersion } = props;

    if (+adPlace.adStatus !== 1 || env.isBaiduChillin) {
        return null;
    }

    const [show, setShow] = useState(true);
    const { compulsoryDisplay, skipType, skip, image } = adPlace;

    useEffect(() => {
        commonShowLog(status, 'guajian', nid, screen, '15313', isNewVersion);
    }, []);

    const clickStaticPendant = () => {
        switch (skipType) {
            case 0:
                // 不跳转
                break;
            case 1:
                // 跳转h5
                commonClickLog(status, 'guajian', nid, screen, '15313', isNewVersion);
                window.location.href = skip;
                break;
            case 2: {
                // schema 跳转
                commonClickLog(status, 'guajian', nid, screen, '15313', isNewVersion);
                let config = {
                    appName: getAppSchemeHead(tiebaLiveCmd && 'tieba'),
                    scheme: skip,
                };
                invokeApp(config);
                break;
            }
            default:
        }
    };

    const closeStaticPendant = () => {
        // 挂件关闭,不显示
        setShow(false);
        // 关闭打点
        sendlog('close');
    };

    return show ? (
        <div className={`static-pendant-container ${className}`}>
            {image && <img src={image} onClick={clickStaticPendant} />}

            {compulsoryDisplay === 0 && (
                <div className='static-pendant-close' onClick={closeStaticPendant}>
                    <CloseSVG />
                </div>
            )}
        </div>
    ) : (
        ''
    );
}

export default StaticPendant;
