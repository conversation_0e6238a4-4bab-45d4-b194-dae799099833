/**
 * @file 关注按钮
 * <AUTHOR>
 * @date 2021-11-12 16:46:54
 */

import React, { useEffect, useState } from 'react';
import { useUpdateEffect } from 'ahooks';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import { PageStore } from '@/pages/liveshow/service/register';
import { FollowEnum } from '@/pages/liveshow/config/const';
import { clickFollow, initFollowChannel } from '@/pages/liveshow/utils/follow';
import { oppo } from '@/pages/liveshow/utils/ignoreReflow';
import cls from 'classnames';
import styles from './index.less';
import { commonShowLog, commonClickLog } from '@/pages/liveshow/log';

const followObj = {
    text: {
        [FollowEnum.PENDING]: '',
        [FollowEnum.UNFOLLOW]: '关注',
        [FollowEnum.FOLLOWED]: '已关注',
        [FollowEnum.NOT_INIT]: '',
    },
    className: {
        [FollowEnum.PENDING]: styles.following,
        [FollowEnum.UNFOLLOW]: styles.unfollow,
        [FollowEnum.FOLLOWED]: styles.followed,
        [FollowEnum.NOT_INIT]: styles.hidden,
    },
};

// 代码里面过滤了 hiphop 和 baidudict
const ignore = ['hiphop', 'baidudict'];

function FollowBtn(props) {
    if (ignore.includes(PageStore.queryParams.source) || oppo) {
        return '';
    }
    const {
        // 是否是预约页面
        isPreview = false,
        followStatus,
        type,
        third_id,
        is_login,
        refresh,
        sourceLive,
        template,
        onClickFollow,
        status,
        nid,
        screen,
    } = props;
    const [innerFollowStatus, setInnerFollowStatus] = useState(followStatus);

    useUpdateEffect(() => {
        setInnerFollowStatus(followStatus);
    }, [followStatus]);

    useEffect(() => {
        initFollowChannel(third_id, (newfollowStatus) => {
            setInnerFollowStatus(newfollowStatus);
        });
    }, []);

    useEffect(() => {
        if (isPreview) {
            return;
        }

        if (innerFollowStatus === 0) {
            commonShowLog(status, 'follow', nid, screen, '15265');
        } else if (innerFollowStatus === 1) {
            commonShowLog(status, 'cancel_follow', nid, screen, '15265');
        }
    }, [innerFollowStatus]);

    const onClick = (e) => {
        e.preventDefault();
        e.stopPropagation();

        if (innerFollowStatus === FollowEnum.PENDING) {
            return;
        }

        if (!isPreview) {
            commonClickLog(
                status,
                innerFollowStatus ? 'cancel_follow' : 'follow',
                nid,
                screen,
                '15265',
            );
        }

        if (onClickFollow && onClickFollow()) {
            return;
        }

        if (is_login) {
            setInnerFollowStatus(FollowEnum.PENDING);
            clickFollow({
                template,
                type: type,
                status: innerFollowStatus,
                third_id: third_id,
            }).then((nextStatus) => {
                setInnerFollowStatus(nextStatus);
                refresh && refresh();
            });
        } else {
            boxjsUtils.login().then(() => {
                // 一定延时后调用(解决安卓11.20这个逻辑运行不稳定的问题)
                window.setTimeout(() => {
                    refresh && refresh();
                }, 500);
            });
        }
    };
    const cc = cls({
        [styles.cmFollowBtn]: true,
        [followObj.className[innerFollowStatus]]: true,
        [styles.isLiveBtn]: !!sourceLive,
    });

    return (
        <div className={cc} onClick={onClick}>
            {followObj.text[innerFollowStatus]}
        </div>
    );
}

export default FollowBtn;
