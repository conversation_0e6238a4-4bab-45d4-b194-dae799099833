@import '~@baidu/nano-theme/index.less';

.theme-root() !important;

@keyframes rotateS {
    from {
        transform: rotate(0deg) translate(-50%, -50%);
    }

    to {
        transform: rotate(1turn) translate(-50%, -50%);
    }
}

.cmFollowBtn {
    position: relative;
    flex-shrink: 0;
    display: inline-block;
    border-radius: .14rem;
    width: .57rem;
    border: 0;
    height: .28rem;
    line-height: .28rem;
    font-size: .13rem;
    font-weight: 500;
    text-align: center;
    .theme-background(GC51);
    .theme-color(GC67);

    outline: 0;
    -webkit-tap-highlight-color: transparent;
    -webkit-appearance: none;
    user-select: none;
    z-index: 1;
}

.isLiveBtn {
    width: 171pr;
    height: 84pr;
    padding: 0 18pr;
    border-radius: 15pr;
    align-items: center;
    justify-content: center;
}

.cmFollowBtn.hidden {
    display: none;
}

.cmFollowBtn.followed {
    // .theme-background(GC54);
    // .theme-color(GC4);
    background-color: #F5F6FA;
    color: #848691;
    font-weight: 500;
    border: none;
}

// .cmFollowBtn.followed::after {
//     box-sizing: border-box;
//     content: '';
//     position: absolute;
//     left: 0;
//     top: 0;
//     border-width: 1px;
//     border-style: solid;
//     .theme-border(GC55);

//     border-radius: .42rem;
//     width: 300%;
//     height: 300%;
//     transform: scale(.333);
//     transform-origin: 0 0;
// }

.cmFollowBtn.following::before {
    content: '';
    position: absolute;
    display: inline-block;
    left: 50%;
    top: 50%;
    animation: rotateS 1s infinite;
    transform-origin: 0 0;
    width: 42pr;
    height: 42pr;
    background-image: url('./img/spinner.png');
    background-repeat: no-repeat;
    background-size: contain;
}

.cmFollowBtn.followed:active,
.cmFollowBtn.unfollow:active {
    opacity: .2;
    filter: alpha(opacity=20);
}

// xxMatrix 是预约样式  .baiduxxx 是落地页样式
.cmFollowBtn.unfollow,
.cmFollowBtn.following {
    // .theme-background(GC71);
    // .theme-color(FC108);
    background-color: #F5F6FA;
    color: #4E6EF2;

    .haokanMatrix & {
        background-color: #ff4141;
    }

    .baiduhaokan & {
        background-color: #ff4141;
    }

    .tiebaMatrix & {
        background: #7559ff;
    }

    .bdtiebalive & {
        background: #7559ff;
        border-radius: 66pr;
    }

    .fortunecatMatrix & {
        background: #c9a687;
    }

    .fortunecat & {
        background: #c9a687;
        border-radius: 66pr;
    }

    .youjiaMatrix & {
        background: #00cecf;
    }

    .youjia & {
        background: #00cecf;
        border-radius: 66pr;
    }

    .hiphopMatrix & {
        background: #5dff6c;
    }

    .bdhiphop & {
        background: #5dff6c;
        border-radius: 66pr;
    }

    .baidudictMatrix & {
        background: #00cb8a;
    }

    .baidudict & {
        background: #00cb8a;
        border-radius: 66pr;
    }
}
