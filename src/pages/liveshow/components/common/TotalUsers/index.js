/**
 * @file 根据message实时更新人数的hook组件 (新旧版本共用)
 * <AUTHOR>
 * @date 2021-11-25 14:52:38
 */
import { useEffect, useRef, useState } from 'react';
import { useUpdateEffect } from 'ahooks';
import Chat, { MessageType } from '@/pages/liveshow/utils/chat';
import handleUsersCount from '@/pages/liveshow/utils/handleUsersCount';

const useTotalUsers = (totalUsers) => {
    const [total, setTotal] = useState(handleUsersCount(totalUsers));
    const chat = useRef(Chat.getInstance());

    useUpdateEffect(() => {
        setTotal(handleUsersCount(totalUsers));
    }, [totalUsers]);

    useEffect(() => {
        chat.current.on(MessageType.TOTAL_USER, (users) => {
            setTotal(handleUsersCount(users));
        });
    }, []);

    return total;
};

export default useTotalUsers;
