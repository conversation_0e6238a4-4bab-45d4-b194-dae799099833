.share-tip-wrapper {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 51;
    background-color: rgba(0, 0, 0, .5);

    .tip {
        font-size: bolder;
        font-size: 42pr;
        line-height: 48pr;
        color: #fff;
        padding: 51pr 132pr 51pr 45pr;
        position: absolute;
        top: 30pr;
        right: 30pr;

        &::before {
            content: '';
            display: inline-block;
            position: absolute;
            top: -15pr;
            right: 34pr;
            width: 0;
            height: 0;
            border: 18pr solid;
            border-radius: 6pr;
            border-color: rgba(31, 31, 31, .95) rgba(31, 31, 31, .95) transparent transparent;
            transform: rotate(-45deg);
            z-index: -2;
        }

        &::after {
            content: '👆';
            font-size: 90pr;
            width: 100%;
            height: 100%;
            padding-right: 33pr;
            box-sizing: border-box;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            position: absolute;
            top: 0;
            right: 0;
            z-index: -1;
            background: rgba(31, 31, 31, .95);
            border-radius: 39pr;

            .is-android & {
                font-size: 72pr;
            }
        }
    }
}