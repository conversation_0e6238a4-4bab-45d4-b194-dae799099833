/**
 * @file 回流组件（新版）
 * <AUTHOR>
 * @date 2022-04-19 14:00:18
 */

 import React, {useEffect, useCallback} from 'react';
 import './index.less';
 import cls from 'classnames';
 import { reflow, matrixAppSchemeHeaderList} from '@/pages/liveshow/utils/reflow';
 import { PageStore } from '@/pages/liveshow/service/register';
 import { commonShowLog, commonClickLog, reflowCallbackLog, cnyClickLog} from '@/pages/liveshow/log';
 import {
     getAppName,
     getAppIcons,
     getAppSchemeHead,
     getUrlParam,
     getAppSource
 } from '@/pages/liveshow/utils/getUrlParam';
 import { env } from '@/utils/env';
 import {isAndroid} from '@baidu/xbox-native/detect';
 import {isLiveDomain} from '@/utils/index';
 import {supportMcpInvoke} from '@/pages/liveshow/config/const';
 import {getWxConfig} from '../../../service/api';
 import {createWxTag} from '@baidu/ug-invoke-app';
 import {initWxTag} from '@/utils/initWxTag';

 const isTomas = env.isTomas || getAppSource() === 'tomas';
 const appName = getAppName();
 const schemeHead = getAppSchemeHead();
 const canUseDefaultIcon = ['baiduboxvision', 'baiduboxlite'];
 const defaultIcon = canUseDefaultIcon.includes(schemeHead)
     ? 'https://b.bdstatic.com/searchbox/icms/searchbox/img/baidu-icon.png'
     : '';
 const typeObj = {
     'default': {
         className: '',
         icon: '',
         defaultIcon: '',
         logValue: 'top_btn',
     },
     'flot-btn': {
         logValue: 'flot_btn',
         className: 'flot-btn',
         icon: getAppIcons(1),
         defaultIcon,
     },
     'top-btn': {
         logValue: 'top_btn',
         className: 'top-btn',
         icon: getAppIcons(2),
         defaultIcon,
     },
 }
 // 回流矩阵场景
 const isMatrixApp = matrixAppSchemeHeaderList.includes(schemeHead);

 function ReflowBtn(props) {
     // type代表回流按钮类型：default flot-btn(悬浮样式) top-btn(顶部固定样式)
     const {
         text,
         className = '',
         type = 'default',
         status,
         nid,
         screen,
         outerSchemeParams = {},
         handleInvoke,
         invokeInfo,
         isCNY
     } = props;
     const OriginIcon = typeObj[type].icon;
     const DefaultIcon = typeObj[type].defaultIcon;
     const icon = OriginIcon || DefaultIcon;

     // 安卓微信场景新增relative属性
     const isShowWeTag = !isMatrixApp && isAndroid() && env.browserType === 'wechat' && isLiveDomain;
     const reflowScheme = reflow({
        roomId: PageStore.roomId,
        outerSchemeParams,
        status
     }, false, true);

     if (type === 'top-btn' && !icon) {
         return '';
     }

     const onClickFun = e => {
        if (e) {
            e.preventDefault();
            e.stopPropagation();
        }

        if (typeObj[type].logValue) {
            commonClickLog(status, typeObj[type].logValue, nid, screen, '15263', '1');
            isCNY && cnyClickLog('2', '15263');
        }

        let reflowConf = {
            roomId: PageStore.roomId,
            outerSchemeParams
        };

        if (typeObj[type].logValue) {
            reflowConf = {
                ...reflowConf,
                status
            }
        }

        if (supportMcpInvoke) {
            handleInvoke('part');
            return;
        }

        reflow(reflowConf);
     };

     useEffect(() => {
         if (typeObj[type].logValue) {
             commonShowLog(status, typeObj[type].logValue, nid, screen, '15263', '1');
         }
     }, []);

     const createWxTags = useCallback(invokeInfo => {
        const {action_rule, log_id} = invokeInfo;
        const partRuleInfo = action_rule.pos_part[0];

        // 底部悬浮回流按钮
        const hoveReflowDom = document.querySelector('.hover-reflow-btn');
        // 直播结束顶部回流
        const endReflowDom = document.querySelector('.end-reflow-btn');

        const partDoms = [hoveReflowDom, endReflowDom];
        partDoms.forEach(dom => {
            dom && createMcpWxTag({targetDom: dom}, 'pos_part', partRuleInfo, log_id);
        });

    }, []);

     useEffect(() => {
         async function addWeixinTag() {
             const targetDom = document.querySelector(`.${className}`);
             window.console.log(targetDom)
             // 回流百度APP && 仅安卓 && 微信 && DOM节点有效时加入标签
             if (!isMatrixApp
                    && isAndroid()
                    && env.browserType === 'wechat'
                    && targetDom
                ) {
                window.console.log(targetDom)
                const res = await getWxConfig();
                if (+res.errno === 0 && res.data && res.data.appId) {
                    const wxInfo = res.data;
                    window.console.log(wxInfo);
                    createWxTag({
                        appName: 'baiduboxapp',
                        targetDom,
                        wxTagId: 'weixin-' + className, // 开放标签的Id
                        wxInfo: wxInfo,
                        scheme: reflowScheme,
                        onClick: function () {
                            window.console.log('click');
                            reflowCallbackLog(status, 'reflow_then', '', 0, '15263', true, '', 'wechat_clk');
                        },
                        onSucceed: function (e) {
                            // 微信开放标签成功
                            reflowCallbackLog(status, 'reflow_then', '', 0, '15263', true, '', 'wechat_suc');
                            window.console.log('success!', e);
                        },
                        onFailed: function (e) {
                            // 微信开放标签失败
                            // reflowCallbackLog(status, 'reflow_then', '', 0, '15263', true, '', 'wechat_fail');
                            window.console.log('failed!', e);
                            setTimeout(() => {
                                onClickFun();
                            }, 1000);
                        }
                    })
                }
             }
         }

        // 存在调起信息 && 安卓 && 微信 && 回流主板 新增微信开放标签校验
        if (!isMatrixApp && env.isAndroid && env.isWechat
            && invokeInfo && invokeInfo.action_rule) {
            createWxTags(invokeInfo);
        }
     }, [className]);

     const getRootClass = cls({
         [className]: className,
         [typeObj[type].className]: true,
         ['reflow-btn-wrapper']: true,
         ['no-icon']: !icon,
         ['is-default-icon']: !OriginIcon && DefaultIcon,
     });

     const getInnerClass = cls({
        [typeObj[type].className]: true,
        ['reflow-btn-wrapper-inner']: true,
        ['is-tomas']: isTomas,
    });

     return (
        <div
            className={getRootClass}
            style={{position: `${isShowWeTag ? 'relative' : 'none'}`}}
         >
             {type === 'flot-btn' && isTomas
                 ? (
                     <div className={getInnerClass} onClick={onClickFun}>
                         <img className='icon' src='https://ala-gift.cdn.bcebos.com/gift/2022-8/1661870507043/tomas.png' />
                         <span className='text'>{`${appName}内播放`}</span>
                     </div>
                 )
                 : (
                     <div className={getInnerClass} onClick={onClickFun}>
                         {icon && <img className='icon' src={icon} />}
                         <span className='text'>{text ? text : `${appName}内播放`}</span>
                     </div>
                 )
             }
         </div>
     );
 }

 export default ReflowBtn;
