@import (reference) '../../../assets/style-util.less';

.btnStyle() {
    flex: none;
    color: #fff;
    font-size: 42pr;
    border-radius: 54pr;
    font-weight: 700;
    background: linear-gradient(180deg, #FF0063 0%, #FF3442 100%);
    .common-flex-center();
    .common-btn-bgcolor;
}

.reflow-btn-wrapper-inner {
    padding: 0 39pr;

    &.top-btn {
        padding: 0 51pr;
    }
}

.reflow-btn-wrapper,
.reflow-btn-wrapper-inner {
    .btnStyle();

    height: 105pr;
    // padding: 0 39pr;

    .text {
        white-space: nowrap;
    }

    // 悬浮回流按钮样式
    &.flot-btn {
        height: auto;
        padding: 0;
        background: none;
        display: flex;
        justify-content: center;
        align-items: center;

        .icon {
            width: auto;
            height: 234pr;
        }

        .text {
            display: none;
        }

        &.is-default-icon {
            .btnStyle();

            background: #4E6EF2;
            font-size: 51pr;
            height: 132pr;
            padding: 0 102pr;
            border-radius: 69pr;

            .icon {
                width: auto;
                height: 66pr;
            }

            .text {
                display: block;
                margin-left: 24pr;
            }
        }

        &.no-icon {
            .btnStyle();

            font-size: 51pr;
            height: 132pr;
            padding: 0 102pr;
            border-radius: 69pr;

            .text {
                display: block;
            }
        }

        &.is-tomas {
            background: #4E6EF2;
            box-shadow: 0 23px 50px 0 rgba(78, 110, 242, .4);
            border-radius: 72px;
            padding: 0 77pr 0 83pr;
            bottom: 165pr !important;

            .icon {
                width: 66pr;
                height: 66pr;
                margin-right: 24pr;
            }

            .text {
                display: block;
                font-size: 60pr;
                line-height: 144pr;
                font-family: PingFangSC-Medium;
            }
        }
    }

    // 顶部回流按钮样式
    &.top-btn {
        width: 100%;
        height: 168pr;
        display: flex;
        justify-content: space-between;
        // padding: 0 51pr;
        border-radius: 0;
        background: #161824;

        .icon {
            width: auto;
            height: 105pr;
        }

        .text {
            .btnStyle();

            background: #4E6EF2;
            height: 99pr;
            font-size: 39pr;
            padding: 0 36pr;
            border-radius: 50pr;
        }
    }
}
