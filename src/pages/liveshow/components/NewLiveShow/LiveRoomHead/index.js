/**
 * @file 直播间头部信息组件（新版）
 * <AUTHOR>
 * @date 2022-04-19 14:00:18
 */

import './index.less';
import React, {useEffect} from 'react';
import cls from 'classnames';
import AuthorInfo from '../AuthorInfo';
import ReflowBtn from '../../common/ReflowBtn';
import BaiduTopBtn from '../BaiduTopBtn';
import { huaweiFeed } from '@/pages/liveshow/utils/ignoreReflow';
import {LiveStatus, supportMcpInvoke} from '@/pages/liveshow/config/const';
import { commonShowLog, commonClickLog } from '@/pages/liveshow/log';


function LiveRoomInfo(props) {
    let {
        className = '',
        updatePageData,
        status,
        onClickShowModel,
        hasReflowBtn,
        swiperScreen = () => { },
        nid,
        screen,
        showRecMoreBtn,
        schemeParams,
        componentPos,
        showAuthorInfo = true,
        handleInvoke,
        invokeInfo,
        isMatrixApp,
        isCNY
    } = props;

    const isLive = status === LiveStatus.LIVEING;
    const getRootClass = cls('room-head-wrapper', {
        'has-author-info': !huaweiFeed,
        'has-reflow-btn': hasReflowBtn,
        'has-review-info': !isLive,
        'has-more-live-btn': showRecMoreBtn,
        [className]: true
    });

    useEffect(() => {
        // 回放标签展现打点
        commonShowLog(status, 'playback_btn_show', nid, screen, '15264');
    }, []);

    const onClickMoreRecommend = e => {
        e.preventDefault();
        e.stopPropagation();

        swiperScreen(2);
    }

    const handlePlayback = e => {
        e.preventDefault();
        e.stopPropagation();

        // 点击回放标签打点
        supportMcpInvoke && commonClickLog(status, 'playback_btn_click', nid, screen, '15264');

        supportMcpInvoke && handleInvoke('part');
    }

    return (
        <div className={getRootClass}>
            <div className="top-wrapper">
                {!huaweiFeed && showAuthorInfo && (
                    <AuthorInfo
                        {...props}
                        isLive={isLive}
                        updatePageData={updatePageData}
                        componentPos={componentPos}
                        handleInvoke={handleInvoke}
                        isMatrixApp={isMatrixApp}
                    />
                )}
                {
                    hasReflowBtn ? (
                        isMatrixApp ? (
                            <ReflowBtn
                                className="auther-reflow-btn"
                                onClickShowModel={onClickShowModel}
                                text='打开APP'
                                status={status}
                                nid={nid}
                                screen={screen}
                                outerSchemeParams={schemeParams}
                                handleInvoke={handleInvoke}
                                invokeInfo={invokeInfo}
                                isCNY={isCNY}
                            />
                        ) : (
                            <BaiduTopBtn
                                onClickShowModel={onClickShowModel}
                                text='打开APP'
                                status={status}
                                nid={nid}
                                screen={screen}
                                outerSchemeParams={schemeParams}
                                handleInvoke={handleInvoke}
                                isCNY={isCNY}
                            />
                        )
                    ) : null
                }
            </div>

            {!isLive && (
                <div className='review-info' onClick={handlePlayback}>
                    <p className="review-icon">回放</p>
                    <p className="review-tip">直播已结束，正在播放回放</p>
                </div>
            )}

            {showRecMoreBtn && (
                <div className='more-live-btn' onClick={onClickMoreRecommend}>更多直播</div>
            )}
        </div >
    );
}

export default LiveRoomInfo;
