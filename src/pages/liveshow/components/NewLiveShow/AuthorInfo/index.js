/**
 * @file 个人信息组件（新版）
 * <AUTHOR>
 * @date 2022-04-19 14:00:18
 */

import React, { useEffect } from 'react';
import './index.less';
import cn from 'classnames';
import FollowBtn from '../FollowBtn';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import useTotalUsers from '@/pages/liveshow/components/common/TotalUsers';
import { env } from '@/utils/env';
import { Avatar } from '@baidu/nano-react';
import { reflow } from '@/pages/liveshow/utils/reflow';
import {supportMcpInvoke} from '@/pages/liveshow/config/const';
import { PageStore, isFromHonorBrowser } from '@/pages/liveshow/service/register';
import { commonShowLog, commonClickLog } from '@/pages/liveshow/log';

// 来源是古物潮玩
const isFortunecat = PageStore.queryParams.source === 'fortunecat';
const roomId = PageStore.roomId;

function AuthorInfo(props) {
    const totalUsers = useTotalUsers(props.totalUsers);
    const userListNum = totalUsers.length >= 6 ? 1 : 2; // 总展示位数大于等于6位时，显示一个头像，如：999.9万人
    let {
        followStatus,
        follow_id,
        follow_type,
        isLogin,
        updatePageData,
        hostAvatar,
        hostName,
        isLive,
        template,
        status,
        nid,
        screen,
        online_user_list,
        componentPos,
        feedbacks,
        handleInvoke,
        isMatrixApp
    } = props;
    let userList = online_user_list || [];
    userList = userList.slice(0, userListNum);

    useEffect(() => {
        commonShowLog(status, 'head', nid, screen, '15264', true, componentPos);
        isLive && !isMatrixApp && commonShowLog(status, 'users_online_show', nid, screen, '15264', true);
    }, []);

    const clickHead = e => {
        e.preventDefault();
        e.stopPropagation();

        commonClickLog(status, 'head', nid, screen, '15264', true, componentPos);

        // 如果来源是招财猫 则直接回流
        if (isFortunecat) {
            reflow({ roomId });
            return;
        }

        if (supportMcpInvoke) {
            handleInvoke('part');
        }
        else {
            boxjsUtils.toAuthorPage(follow_id, follow_type === 'ugc');
        }
    };

    const followParams = {
        followStatus,
        template,
        third_id: follow_id,
        type: follow_type,
        is_login: isLogin,
        updatePageData,
        status,
        nid,
        screen,
    };

    if (isFortunecat) {
        followParams.onClickFollow = () => reflow({ roomId });
    }

    if (supportMcpInvoke) {
        followParams.onClickFollow = () => handleInvoke('part');
    }

    const handleOnlineUsers = e => {
        e.preventDefault();
        e.stopPropagation();

        // 在线人数打点
        supportMcpInvoke && commonClickLog(status, 'users_online_click', nid, screen, '15264', true);
        supportMcpInvoke && handleInvoke('part');
    }

    return (
        <div className='author-info-wrapper'>
            {/* 主播信息区 */}
            <div className='author-container' onClick={clickHead}>
                <Avatar className='avatar' hairline={false} src={hostAvatar} />
                <div className="author-content">
                    <p className={cn('author-name', env.isTomasSource && 'tomas-name')}>{hostName}</p>
                    {env.isTomasSource && (
                        <p className='total-users'>
                            {feedbacks}点赞 {totalUsers}人
                        </p>
                    )}
                </div>
                {/* 荣耀白牌不展示关注按钮 */}
                {!isFromHonorBrowser && <FollowBtn {...followParams} isHideFollowed componentPos={componentPos} />}
            </div>

            {/* 用户信息区 */}
            {
                isLive
                && !env.isTomasSource
                && (
                    <div className='users-container' onClick={handleOnlineUsers}>
                        <div className='users-profile-list'>
                            {userList.length >= userListNum
                                && (
                                    userList.map((item, idx) => {
                                        return <Avatar key={idx} className='avatar' src={item.avatar} />
                                    })
                                )
                            }
                        </div>
                        <p className='total-users'>{totalUsers}人</p>
                    </div>
                )
            }
        </div >
    );
}

export default AuthorInfo;
