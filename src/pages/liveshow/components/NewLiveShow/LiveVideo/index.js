/**
 * @file 播放器组件（新版）
 * <AUTHOR>
 * @date 2022-04-19 14:00:18
 */

import React, {useEffect, useRef, useState} from 'react';
import cls from 'classnames';
import HPlayer from '@baidu/hplayer';
import Hls from 'hls.js';
import Chat, { MessageType } from '@/pages/liveshow/utils/chat';
import { env } from '@/utils/env';
import { isBox, isAndroid, isLiteBox } from '@baidu/boxx/env';
import {LiveStatus, supportMcpInvoke} from '@/pages/liveshow/config/const';
import { PageStore } from '@/pages/liveshow/service/register';
import { goNativeLiveRoom } from '@/pages/liveshow/utils/openNativePage';
import {createMcpWxTag} from '@baidu/mcp-sdk';
import { commonShowLog, commonClickLog, cnyClickLog } from '@/pages/liveshow/log';
import {reflow} from '@/pages/liveshow/utils/reflow';
import { registerPageHidden } from '@/utils/index';
import './index.less';

const VideoMask = props => {
    if (props.isBaijiahaoTag && props.isNoVideo) {
        return '';
    }

    const {
        onClickShowModel = () => { },
        updatePageData = () => { },
        isDiscount,
        iOSDiscountPrice,
        androidDiscountPrice,
        iOSPrice,
        androidPrice,
        isGroupPaid,
        isPaid,
        liveHlsUrl,
        hasVideo,
        tiebaLiveCmd,
    } = props;
    const price = env.isIOS ? iOSPrice : androidPrice;
    const discountPrice = env.isIOS ? iOSDiscountPrice : androidDiscountPrice;

    // 点击付费按钮
    const clickPaidBtn = () => {
        if (env.isMainBox) {
            goNativeLiveRoom({
                roomId: PageStore.roomId,
                hasVideo: hasVideo,
                tiebaCmd: tiebaLiveCmd,
            });
        } else {
            onClickShowModel({
                show: true,
                type: 'pay',
            });
        }
    };

    const ContentRender = () => {
        // 展示付费拼团提示
        if (isGroupPaid) {
            return (
                <div className='group-wrapper'>
                    <div className='group-title'>付费直播拼团活动已结束</div>
                    <div className='group-tip'>原价购入不计入分享领现金任务中</div>
                    <div className='group-btn' onClick={clickPaidBtn}>
                        原价购买{price}
                    </div>
                </div>
            );
        }

        // 展示付费提示
        if (isPaid) {
            return (
                <div className='paid-wrapper'>
                    <div className='paid-title'>当前内容为付费内容，请购买后观看</div>
                    <div className='paid-price-container'>
                        {isDiscount
                            ? (
                                <div>
                                    {discountPrice}
                                    <span className='discount'>{price}</span>
                                </div>
                            )
                            : <div>{price}</div>
                        }
                    </div>
                    <div className='paid-btn' onClick={clickPaidBtn}>
                        购买直播
                    </div>
                </div>
            );
        }

        return (
            <div className='url-error-wrapper' onClick={updatePageData} >
                网络异常，点此重试
            </div>
        );
    }

    return (
        <div className="video-mask-wrapper">
            {ContentRender()}
        </div>
    );
}

let isClickPlay = false; // 是否点击过播放
let innerInitStatus = true; // 视频第一帧播放成功（用于初始化im消息）
let hasShowModal = false; // 是否已展示过调起弹窗
const LiveVideo = props => {
    const {
        className = '',
        status,
        liveHlsUrl,
        isPaid,
        isGroupPaid,
        updatePageData,
        onClickShowModel,
        coverImg,
        screen,
        isBaijiahaoTag,
        setInitStatus = () => { },
        isInitStatus, // 首帧播放成功后值为false
        isAutoPlay = false,
        isMatrixApp,
        handleInvoke,
        nid,
        schemeParams,
        watch_video_duration = 5, // 默认播放5s
        isCNY,
        invokeInfo,
        noreflow = false,
        hasVerCover = false
    } = props;
    innerInitStatus = isInitStatus;
    const player = useRef();
    const chat = useRef(Chat.getInstance());
    const liveEndedTimerRef = useRef();
    const timeupdateTimerRef = useRef();
    const [showMask, setShowMask] = useState(false);
    const [videoLoadErr, setVideoLoadErr] = useState(false); // 是否加载异常
    const [showLoading, setShowLoading] = useState(false); // 是否显示加载中
    const [currentTime, setCurrentTime] = useState(0); // 重新加载时记录播放时间
    const [showPlayBtnMask, setShowPlayBtnMask] = useState(false); // 播放5s暂停后展示

    const isLive = status === LiveStatus.LIVEING;
    const isNoVideo = isPaid || isGroupPaid || !liveHlsUrl;
    // const isGaoKao = PageStore.queryParams?.wish_record_id; // 根据URL参数判断是否为高考切片分享

    const initVideoType = streamURL => {
        // 默认类型为 normal
        let videoType = 'normal';

        // 仅PC端用iOS UA看H5页面才用HLS插件播放
        // 1. 移动端原生支持HLS
        // 2. 规避安卓H5播放器抢占手百音频焦点问题
        // 3. iOS Safari不支持MSE
        if (!env.isAndroid && !streamURL.includes('.mp4') && Hls.isSupported()) {
            videoType = 'hls';
        }

        return videoType;
    };

    // 初始化videoType
    const videoType = initVideoType(liveHlsUrl);

    window.console.log('videoType:', videoType);

    useEffect(() => {
        setVideoLoadErr(false);

        // 付费或者无播放链接不处理
        if (isNoVideo) {
            setShowMask(true);
            return;
        }

        initMediaPlayer(isAutoPlay);

        return destroyPlayer;
    }, [status]);

    useEffect(() => {
        if (videoLoadErr === true) {
            destroyPlayer();
        }
    }, [videoLoadErr]);

    useEffect(() => {
        // 直播按钮展现
        !isMatrixApp && commonShowLog(status, 'h5_play_show', nid, screen, '15264');
    }, []);

    useEffect(() => {
        if (showPlayBtnMask && env.isAndroid && env.isWechat && !isMatrixApp && invokeInfo && invokeInfo.action_rule) {
            const {action_rule, log_id} = invokeInfo;
            // 播放按钮遮罩
            const playBtnMaskDom = document.querySelector('.play-btn-mask');
            playBtnMaskDom && createMcpWxTag({targetDom: playBtnMaskDom}, 'pos_part', action_rule.pos_part[0], log_id);
        }
    }, [showPlayBtnMask, invokeInfo]);

    const initMediaPlayer = (startPlay = false) => {
        setVideoLoadErr(false);

        player.current = new HPlayer({
            container: document.getElementById('video-box'),
            playsinline: true, // 是否内联播放
            theme: '#ff3355', // 主体色
            lang: 'zh-cn', // 本地语言
            loop: !isLive, // 循环
            live: isLive, // 是否直播中
            speed: !isLive && !isCNY, // 倍速播放
            screenshot: false, // 不启用截屏
            hotkey: false, // 不启用热键
            preload: 'auto', // video preload
            pressSpeed: isBox && isAndroid ? 2 : 3, // android手百仅支持到2倍速
            video: {
                url: liveHlsUrl,
                bgpic: !isBox && coverImg ? coverImg : '', // 手百里容器层有探测图片弹NA View，手百里不添加兜底poster了
                pic: coverImg ? coverImg : '',
                type: videoType, // hls模式应用在尽可能的规避各浏览器强制接管h5 video
                customType: {
                    hls: function (video, player) {
                        if (player.plugins.hls) {
                            player.plugins.hls.destroy();
                        }

                        const hls = new Hls();
                        // Object.keys(Hls.Events).forEach(eventName => {
                        //     hls.on(Hls.Events[eventName], console.info.bind(console));
                        // });
                        hls.on(Hls.Events.MEDIA_ATTACHED, console.info.bind(console));
                        hls.on(Hls.Events.MANIFEST_PARSED, console.info.bind(console));
                        hls.on(Hls.Events.FRAG_PARSING_USERDATA, console.info.bind(console));
                        hls.on(Hls.Events.FRAG_PARSING_METADATA, console.info.bind(console));
                        hls.on(Hls.Events.ERROR, console.info.bind(console));

                        hls.attachMedia(video);

                        hls.on(Hls.Events.MEDIA_ATTACHED, (event, data) => {
                            hls.loadSource(liveHlsUrl);

                            hls.on(Hls.Events.ERROR, (event, data) => {
                                if (data.fatal) {
                                    switch (data.type) {
                                        case Hls.ErrorTypes.NETWORK_ERROR:
                                            if (data.url === liveHlsUrl) {
                                                hls.detachMedia();
                                                onError();
                                            }
                                            break;
                                        case Hls.ErrorTypes.MEDIA_ERROR:
                                            hls.swapAudioCodec();
                                            hls.recoverMediaError();
                                            break;
                                        default:
                                            break;
                                    }
                                }
                            });
                        });

                        player.plugins.hls = hls;
                        player.events.on('destroy', () => {
                            hls.destroy();
                            delete player.plugins.hls;
                        });
                    },
                },
            },
        });

        videoSectionEvent();

        player.current.on('play', () => {
            // 回放状态 直接调起
            supportMcpInvoke && commonClickLog(status, 'h5_play_click', nid, screen, '15264');
            isCNY && cnyClickLog('1', '15263');

            isClickPlay = true;
            setShowLoading(true);

            window.console.log('play');
            // 回放 或 直播中已经展示过调起弹窗 再次执行调起
            if (hasShowModal) {
                if (supportMcpInvoke && !env.isBaiduHi) {
                    // 主板
                    handleInvoke('part');
                    return;
                }
                else if (PageStore.queryParams.source === 'lite') {
                    // lite 支持再次点击调起
                    reflow({
                        roomId: PageStore.roomId,
                        outerSchemeParams: schemeParams
                    });
                    return;
                }
            }
        });

        player.current.on('playing', () => {
            window.console.log('playing');

            clearTimeout(liveEndedTimerRef.current);
            clearTimeout(timeupdateTimerRef.current);
            setInitStatus(false);
            setShowLoading(false);

            try {
                // 支持回流场景
                // 直播中 或 回放 播放5s 后暂停播放并展示调起弹窗, 播放时长为0时 不限制
                // 非主板、非Lite 端内、端外回流主板 或 安卓回流Lite
                if (!noreflow
                    && (!isLiteBox && !env.isMainBox)
                    && (!isMatrixApp || (PageStore.queryParams.source === 'lite' && env.isAndroid))
                    && +watch_video_duration !== 0 && !hasShowModal) {
                    setTimeout(() => {
                        player.current && player.current.pause && player.current.pause();
                        onClickShowModel({
                            show: true,
                            type: 'reflow',
                            from: 'video',
                        });

                        setShowPlayBtnMask(true);

                        hasShowModal = true;
                    }, watch_video_duration * 1000);
                }
            }
            catch (e) {
                window.console.log(e);
            }
        });

        player.current.on('waiting', () => {
            setShowLoading(true);
        });

        player.current.on('pause', () => {
            clearTimeout(liveEndedTimerRef.current);
            clearTimeout(timeupdateTimerRef.current);
            setShowLoading(false);
        });

        player.current.on('ended', () => {
            clearTimeout(liveEndedTimerRef.current);
            clearTimeout(timeupdateTimerRef.current);
            !isLive && setCurrentTime(0);
        });

        player.current.on('error', onError);

        player.current.on('timeupdate', () => {
            setShowLoading(false);
            clearTimeout(liveEndedTimerRef.current);
            clearTimeout(timeupdateTimerRef.current);

            if (!innerInitStatus) {
                // 直播中状态 && 播放过程中8s加载不出资源，请求接口获取直播结束状态
                if (isLive) {
                    liveEndedTimerRef.current = setTimeout(() => {
                        updatePageData && updatePageData();
                    }, 8000);
               }

               // 15s加载不出资源，展示网络异常
               timeupdateTimerRef.current = setTimeout(() => {
                    setVideoLoadErr(true);
               }, 15000);
            }
        });

        player.current.on('loadstart', () => {
            if (player.current) {
                startPlay && player.current.play();
                if (!isLive && !isClickPlay) {
                    // 回放视频，重新加载时设置上一次播放位置
                    player.current.video.currentTime = currentTime;
                }
            }
        });

        // player.current.on('seeked', (e) => {
        //     window.console.log('seeked');
        //     // 更新进度
        //     if (!isMatrixApp) {
        //         handleInvoke('part');
        //     }
        // });

        player.current.on('ratechange', (e) => {
            window.console.log('ratechange');
            // 切换倍速
            if (supportMcpInvoke) {
                handleInvoke('part');
            }
        });

        player.current.on('fullscreen', (e) => {
            window.console.log('fullscreen');
            // 切换全屏
            if (supportMcpInvoke) {
                handleInvoke('part');
            }
        });

        // player.current.on('volumechange', (e) => {
        //     // 点击小喇叭
        //     window.console.log('volumechange');
        //     // 切换静音状态
        //     if (!isMatrixApp) {
        //         handleInvoke('part');
        //     }
        // });


        if (isLive) {
            // 直播中 监听im消息切流状态 然后重新设置播放链接
            chat.current.on(MessageType.LIVE_CHANGE_FLOW, live_hls_url => {
                if (live_hls_url && player.current) {
                    player.current.switchVideo({ url: live_hls_url });
                }
            });
        }
    }

    // 视频切片设置
    const videoSectionEvent = () => {
        // 是否需要视频切片，start有可能是零
        const { start: videoStartTime, end: videoEndTime } = PageStore.queryParams;
        const isNeedSetVideoStarTime = !isLive && +videoStartTime >= 0 && +videoEndTime >= 0;

        if (isNeedSetVideoStarTime) {
            // 切片是否播放完成
            let isEndedBySection = false;

            player.current.on('loadeddata', () => {
                // 初始化 设置视频切片开始时间
                if (+videoStartTime > 0) {
                    player.current.video.currentTime = +videoStartTime;
                }
            });
            player.current.on('timeupdate', () => {
                // 监听视频切片结束时间 自动暂停
                if (!isEndedBySection && player.current.video.currentTime >= +videoEndTime) {
                    player.current.pause();
                    isEndedBySection = true;
                }
            });
        }
    }

    const onError = () => {
        if (isClickPlay) {
            setVideoLoadErr(true);
            setShowLoading(false);
        }
    }

    // 销毁播放器及im监听
    const destroyPlayer = () => {
        clearTimeout(liveEndedTimerRef.current);
        clearTimeout(timeupdateTimerRef.current);

        isClickPlay = false;
        setInitStatus(true);
        setShowLoading(false);

        if (player.current) {
            !isLive && setCurrentTime(player.current.video.currentTime);
            player.current.destroy();
            player.current = null;
        }

        if (chat.current) {
            chat.current.off(MessageType.LIVE_CHANGE_FLOW);
        }
    }

    const getRootClass = cls({
        'live-video-wrapper': true,
        [className]: true,
        'is-hor': +screen === 1,
        'is-ver': +screen === 0,
        'is-living': isLive,
        'is-no-video': isNoVideo,
        'is-init-status': isInitStatus,
        'is-video-error': videoLoadErr,
        'is-video-loading': showLoading,
        'has-ver-cover': hasVerCover
    });

    return (
        <React.Fragment>
            <div className={getRootClass}>
                {/* 遮罩层 */}
                {showMask && (
                    <VideoMask
                        {...props}
                        isBaijiahaoTag={isBaijiahaoTag}
                        onClickShowModel={onClickShowModel}
                        updatePageData={updatePageData}
                        isNoVideo={isNoVideo}
                    />
                )}

                {/* 播放器 */}
                <div
                    id='video-box'
                    style={{ backgroundImage: isNoVideo ? `url(${coverImg})` : 'none' }}
                ></div>

                {/* 仅在播放5s后暂停后展示 */}
                {showPlayBtnMask && (
                    <div className={'play-btn-mask'} onClick={() => handleInvoke('part')}></div>
                )}

                {/* 仅回放直播间展示全屏按钮遮罩 */}
                {+status === LiveStatus.END_HAS_REVIEW && (
                    <div
                        className={`fullscreen-btn-mask ${+screen === 1 ? 'hor-video' : 'ver-video'}`}
                        onClick={() => handleInvoke('part')}></div>
                )}

                {videoLoadErr && (
                    <div
                        className='error-wrapper'
                        onClick={() => {
                            initMediaPlayer(true);
                        }}
                    >直播间加载异常，请点击重试</div>
                )}
            </div>
        </React.Fragment>
    );
};

export default LiveVideo;
