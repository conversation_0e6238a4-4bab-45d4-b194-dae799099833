/**
 * @file CNY底bar
 */

import React, { useEffect } from 'react';
import Lot<PERSON> from 'react-lottie';
import animationData from './lottie/mainBtn.json';
import { cnyClickLog } from '@/pages/liveshow/log';
import {supportMcpInvoke} from '@/pages/liveshow/config/const';
import './index.less';

function CNYBottomBar(props) {
    const { onClickShowModel, setShowTip, status, nid, screen, handleInvoke, isMatrixApp} = props;

    const mainBtn = {
        loop: true,
        autoplay: true,
        animationData: animationData,
        rendererSettings: {
          preserveAspectRatio: 'xMidYMid slice',
        },
    };

    const clickBottomBar = e => {
        e.preventDefault();
        e.stopPropagation();

        cnyClickLog('4', '15263')

        if (supportMcpInvoke) {
            handleInvoke('part');
        }
        else {
            onClickShowModel({
                show: true,
                type: 'reflow',
                from: 'bottomBar',
            });
        }
    };

    return (
        <div className='bot-bg'  onClick={clickBottomBar}>
            <div className='bot-bar-wrapper'>
                <div className='cny-icon-1'>
                    <div className='icon-img1'></div>
                    <div className='icon-words'>发评论</div>
                </div>
                <div className='cny-icon-1'>
                    <div className='icon-img2'></div>
                    <div className='icon-words'>送礼物</div>
                </div>
                <div className='cny-icon-1'>
                    <div className='icon-img3'></div>
                    <div className='icon-words'>分享</div>
                </div>
                <div className='cny-icon-1'>
                    <div className='icon-img4'></div>
                    <div className='icon-words'>点赞</div>
                </div>
                <div className='cny-icon-2'></div>
            </div>
            <div className='bot-btn-wrapper'>
                <div className='btn-bg'></div>
                <div className='cny-btn1'>
                    <div className='btn-words'>领现金</div>
                </div>
                <div className='cny-btn2'>
                    <Lottie options={mainBtn} />
                </div>
                <div className='cny-btn3'>
                    <div className='btn-words'>任务红包</div>
                </div>
            </div>
        </div>

    );
}

export default CNYBottomBar;
