/**
 * @file 更多推荐组件（新版）
 * <AUTHOR>
 * @date 2022-04-19 14:00:18
 */

import React, {useCallback, useEffect} from 'react';
import './index.less';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import ErrorPage from '@/pages/liveshow/components/common/ErrorStatus';
import handleUsersCount from '@/pages/liveshow/utils/handleUsersCount';
import {env} from '@/utils/env';
import {Image, Avatar} from '@baidu/nano-react';
import {useRequest} from 'ahooks';
import {h5UrlForRoomId, getUrlParam, getAppSource} from '@/pages/liveshow/utils/getUrlParam';
import {moreRecommand} from '@/pages/liveshow/service/model';
import {reflow} from '@/pages/liveshow/utils/reflow';
import {PageStore} from '@/pages/liveshow/service/register';
import {goNativeLiveRoom} from '@/pages/liveshow/utils/openNativePage';
import {appLiveScheme, APP_NAME_SCENE, supportMcpInvoke, needGetMcpInfo} from '@/pages/liveshow/config/const';
import {execInvoke, getInvokeInfo, setCommonParams, createMcpWxTag} from '@baidu/mcp-sdk';
import {commonClickLog} from '@/pages/liveshow/log';
import LiveState from '../LiveState';

const isBox = env.isBox || env.isLiteBox;

// 推荐列表调起位信息（mcp内配置API返回)
let recommandInvokeInfo = {};
// 回流渠道
const source = getAppSource() || PageStore.queryParams.source || '';

function Item({item, index, clickItem}) {
    let {
        name,
        title,
        imgSrc,
        roomId,
        anchorPhoto,
        displayUserCount = '',
        liveStatus,
        tag
    } = item;

    return (
        <div
            className={`item-wrapper item${index + 1}`}
            onClick={(ev) => clickItem(ev, roomId, index)}
        >
            <div className="cover-box">
                <Image url={imgSrc} size='full' className='cover-img' />
                <LiveState status={liveStatus} text={tag} />
                {!isBox && <div className="open-app">打开APP</div>}
            </div>
            <div className="title">{title}</div>
            <div className="user">
                <Avatar className='user-head' src={anchorPhoto} hairline={false} />
                <p className="user-name">{name}</p>
                <p className="user-count">{handleUsersCount(displayUserCount)}</p>
            </div>
        </div>
    );
}

function LiveList(props) {
    const {
        status,
        nid,
        screen,
        recommendFirstShow,
        outerSchemeParams = {},
        isMatrixApp,
        handleInvoke,
        invokeInfo,
        noreflow
    } = props;
    const {loading, data, error} = useRequest(moreRecommand);

    const createWxTags = useCallback(recommandInvokeInfo => {
        const {action_rule, log_id} = recommandInvokeInfo;
        // 更多直播标题按钮调起信息
        const moreRuleInfo = invokeInfo?.action_rule?.pos_morelive[0];

        // 列表标题按钮
        const listTitleDom = document.querySelector('.more-btn');

        // 更多直播，回流到直播Tab
        listTitleDom
        && moreRuleInfo && createMcpWxTag({targetDom: listTitleDom}, 'pos_morelive', moreRuleInfo, invokeInfo?.log_id);

        // 创建list微信开放标签
        data.forEach((item, index) => {
            // 列表单项按钮
            const ltItemDom = document.querySelector(`.item${index + 1}`);

            ltItemDom
                && createMcpWxTag({targetDom: ltItemDom}, 'pos_part', action_rule.pos_part[index], log_id);
        });
    }, [data]);

    const generateInvokeInfo = useCallback(() => {
        const res = {pos_part: []};

        data.forEach(item => {
            res.pos_part.push(
                {
                    'share_scheme': item?.schema_info?.enter_cmd || appLiveScheme
                }
            );
        });

        return res;
    }, [data]);

    useEffect(() => {
        // 设置通用调起信息
        setCommonParams({
            app: 'wise',
            scene: APP_NAME_SCENE[source] || 'sharepage',
            from: '',
            ext_sid: getUrlParam('ext_sid') || '' // 透传url上sid参数
        });
        const fetchMCPData = async () => {
            const invokeParams = generateInvokeInfo();

            // 获取调起位详细信息
            const info = await getInvokeInfo({
                invoke_info: invokeParams
            });
            recommandInvokeInfo = info;

            // 存在调起信息 && 安卓 && 微信 && 回流主板 新增微信开放标签校验
            if (!isMatrixApp && env.isAndroid && env.isWechat
                    && info && info.action_rule) {
                info && createWxTags(info);
            }
        };

        // 回流手百 且 支持回流时执行 !isMatrixApp
        if (data && needGetMcpInfo && !noreflow) {
            fetchMCPData();
        }

    }, [data]);

    // 执行具体调起行为
    const execMcpInvoke = useCallback(async index => {
        // 支持url参数判断不执行回流
        if (noreflow) {
            return;
        }
        // 调起目标直播间roomId
        const invokeRoomId = data[index]?.roomId || PageStore.room_id;
        const invokeInfoForPos = recommandInvokeInfo?.action_rule?.pos_part;

        if (!invokeInfoForPos || !Array.isArray(invokeInfoForPos) || !invokeInfoForPos[index]) {
            window.console.log('无相关调起位信息');
            // 执行默认调起 兼容接口返回异常情况
            reflow({roomId: invokeRoomId});
            return;
        }
        try {
            const res = await execInvoke(
                'pos_part',
                invokeInfoForPos[index],
                invokeInfoForPos[index].log_id
            );
        }
        catch (e) {
            window.console.log(e);
        }
    }, [data]);

    const clickItem = (e, roomId, index) => {
        e.preventDefault();
        e.stopPropagation();

        commonClickLog(status, 'morelive_btn', nid, screen, '15263', true);

        // 接入MCP场景下
        if (supportMcpInvoke) {
            execMcpInvoke(index);
        }
        else {
            if (env.isBaiduChillin) {
                window.location.href = h5UrlForRoomId(roomId);
            }
            else if (env.isLiteBox) {
                boxjsUtils.openEasyBrowse(h5UrlForRoomId(roomId));
            }
            else if (env.isMainBox) {
                goNativeLiveRoom({
                    roomId,
                    outerSchemeParams: data[index].schemeParams
                }, true); // 推荐列表项目前接口未返回相应的scheme信息，此处新增是列表项跳转标识
            }
            else {
                reflow({
                    roomId,
                    outerSchemeParams: data[index].schemeParams
                }); // 后期需RD配合返回列表项目对应scheme和口令信息
            }
        }
        if (boxjsUtils.platformInfo.isHarmonyBox) {
            const url = new URL(window.location.href);
            url.searchParams.set('room_id', roomId);
            url.searchParams.set('source', 'live_more');
            window.location.href = url.toString();
        }
    };

    const onClickMore = () => {
        commonClickLog(status, 'morelive_btn', nid, screen, '15263', true);

        if (['lite', 'haokan'].includes(source)) {
            // 好看、极速版回流主直播间
            handleInvoke('part');
        }
        else if (!isMatrixApp) {
            // 主板回流频道
            handleInvoke('morelive');
        }
        else {
            // 未接入MCP场景
            if (env.isMainBox) {
                goNativeLiveRoom({
                    roomId: PageStore.roomId,
                    outerSchemeParams
                });
            }
            else {
                reflow({
                    roomId: PageStore.roomId,
                    outerSchemeParams
                });
            }
        }
    };

    const ContentRender = () => {
        return (
            <div className='inner-content'>
                <div className='list-title'>
                    <p className='title'>更多热门推荐</p>
                    {!env.isBox && (
                        <p
                            className='more-btn'
                            onClick={onClickMore}
                        >查看更多</p>
                    )}
                </div>
                <div className='list'>
                    {data.map((item, index) => (
                        <Item key={item.roomId} item={item} index={index} clickItem={clickItem} />
                    ))}
                </div>
            </div>
        );
    };

    if (loading) {
        return '';
    }

    return (
        <div className='live-list-wrapper'>
            {error
                ? <ErrorPage />
                : ContentRender()
            }
        </div>
    );
}

export default LiveList;
