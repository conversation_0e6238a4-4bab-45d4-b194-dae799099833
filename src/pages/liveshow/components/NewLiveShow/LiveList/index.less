@import (reference) '../../../assets/style-util.less';

.live-list-wrapper {
    width: 100%;
    background: #161824;

    .error-wrapper {
        height: 100vh;
    }

    // 页面整体加载态的wrapper样式
    .flash-wrapper {
        display: flex;
        width: 100%;
        height: 100%;
        align-items: center;
        justify-content: center;
    }

    .inner-content {
        font-size: 18px;
        color: #FFF;
        padding: 0 51pr 120pr;
        overflow: hidden;
    }

    .list-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 60pr;
        margin-bottom: 57pr;
        margin-top: 99pr;

        .title {
            font-size: 54pr;
            font-weight: bolder;
        }

        .more-btn {
            font-size: 42pr;
            padding-right: 27pr;
            position: relative;

            &::after {
                content: '';
                display: block;
                position: absolute;
                top: 30pr;
                right: -9pr;
                .arrow(16pr);
            }
        }
    }

    .list {
        display: flex;
        flex-wrap: wrap;
    }

    .item-wrapper {
        width: 558pr;
        height: 558pr;
        color: #FFF;
        margin: 0 24pr 24pr 0;
        background: #1E2339;
        border-radius: 27pr;
        overflow: hidden;
        position: relative;

        &:nth-child(even) {
            margin-right: 0;
        }

        .s-image-default {
            background-color: transparent;
        }

        .cover-box {
            width: 558pr;
            height: 315pr;
            box-shadow: inset 0 0 51pr 0 rgba(0, 0, 0, .05);
            border-radius: 36pr 36pr 0 0;
            font-size: 33pr;
            overflow: hidden;
            position: relative;

            .cover-img {
                width: 100%;
                height: 100%;

                .s-image-default {
                    background-size: 33%;
                }
            }

            .open-app,
            .live-state-wrapper {
                position: absolute;
                top: 27pr;
            }

            .live-state-wrapper {
                left: 27pr;
            }

            .open-app {
                .bgMask(.4, 33pr);
                right: 27pr;
                padding: 0 21pr;
                height: 54pr;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }

        .title {
            height: 132pr;
            margin: 18pr 27pr 12pr;
            line-height: 66pr;
            font-size: 48pr;
            .text-ellipse(2);
            word-break: break-all;
        }

        .user {
            padding: 0 27pr;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .user-head {
                flex: none;
                width: 42pr;
                height: 42pr;
                margin-right: 12pr;
            }

            .user-name {
                word-break: break-all;
                margin-left: 15pr;
                font-size: 36pr;
                opacity: 0.7;
                flex: 1;
                .text-ellipse(1);
            }

            .user-count {
                opacity: 0.7;
                font-size: 36pr;
                flex: none;
                margin-left: 6pr;
            }
        }
    }
}