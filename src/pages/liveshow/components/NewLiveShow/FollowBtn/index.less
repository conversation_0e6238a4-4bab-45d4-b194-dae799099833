@import (reference) '../../../assets/style-util.less';

@keyframes rotateS {
    from {
        transform: rotate(0deg) translate(-50%, -50%);
    }

    to {
        transform: rotate(1turn) translate(-50%, -50%);
    }
}

@borderRadius: 42pr;

.follow-btn-wrapper {
    width: auto;
    flex: none;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;

    &.is-default-style {
        min-width: 150pr;
        height: 84pr;
        padding: 0 33pr;
        font-size: 42pr;
        border-radius: @borderRadius;
        font-weight: bolder;
        color: #fff;
        background: linear-gradient(180deg, #FF1F66 0%, #FF4D4D 100%);
        .common-btn-bgcolor;

        // 已关注
        &.followed {
            background: transparent;
            color: #858585;

            &::after {
                content: '';
                display: inline-block;
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                box-sizing: border-box;
                border: 1px solid #858585;
                border-radius: @borderRadius;
            }
        }
    }

    &.is-icon-style {
        padding: 27pr 40pr 27pr 25pr;

        &::after {
            content: '';
            display: inline-block;
            width: 40pr;
            height: 40pr;
            background-color: transparent;
            background-image: url(./img/add.png);
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center center;
        }

        &.followed::after {
            background-image: url(./img/remove.png);
        }
    }

    // 关注中
    &.following {
        &::after {
            opacity: 0;
        }

        &::before {
            content: '';
            display: inline-block;
            position: absolute;
            left: 50%;
            top: 50%;
            animation: rotateS 1s infinite;
            transform-origin: 0 0;
            width: 42pr;
            height: 42pr;
            background-image: url('./img/spinner.png');
            background-repeat: no-repeat;
            background-size: contain;
        }
    }

    // 隐藏
    &.hidden {
        display: none;
    }
}