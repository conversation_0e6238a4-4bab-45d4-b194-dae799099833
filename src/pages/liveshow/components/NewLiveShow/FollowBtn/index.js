/**
 * @file 关注组件（新版）
 * <AUTHOR>
 * @date 2022-04-19 14:00:18
 */

import React, { useEffect, useState } from 'react';
import { useUpdateEffect } from 'ahooks';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import { PageStore } from '@/pages/liveshow/service/register';
import { FollowEnum } from '@/pages/liveshow/config/const';
import { clickFollow, initFollowChannel } from '@/pages/liveshow/utils/follow';
import ignoreReflow from '@/pages/liveshow/utils/ignoreReflow';
import { commonShowLog, commonClickLog } from '@/pages/liveshow/log';
import cls from 'classnames';
import './index.less';

const ignore = ['hiphop', 'baidudict'];

function FollowBtn(props) {
    // 音磁、汉语app及不支持回流的情况 不展示关注按钮
    if (ignore.includes(PageStore.queryParams.source) || ignoreReflow) {
        return '';
    }

    const {
        className = '',
        isHideFollowed = false,
        isIconStyle = false,
        followStatus,
        type,
        third_id,
        is_login,
        updatePageData,
        template,
        onClickFollow,
        status,
        nid,
        screen,
        componentPos,
    } = props;
    const [innerFollowStatus, setInnerFollowStatus] = useState(followStatus);
    const followObj = {
        text: {
            [FollowEnum.PENDING]: '',
            [FollowEnum.UNFOLLOW]: isIconStyle ? '' : '关注',
            [FollowEnum.FOLLOWED]: isIconStyle ? '' : '已关注',
            [FollowEnum.NOT_INIT]: '',
        },
        className: {
            [FollowEnum.PENDING]: 'following',
            [FollowEnum.UNFOLLOW]: 'unfollow',
            [FollowEnum.FOLLOWED]: isHideFollowed ? 'hidden' : 'followed',
            [FollowEnum.NOT_INIT]: 'hidden',
        },
    };

    useEffect(() => {
        initFollowChannel(third_id, (newfollowStatus) => {
            setInnerFollowStatus(newfollowStatus);
        });
    }, []);

    useUpdateEffect(() => {
        setInnerFollowStatus(followStatus);
    }, [followStatus]);

    useEffect(() => {
        if (innerFollowStatus === 0) {
            commonShowLog(status, 'follow', nid, screen, '15265', true, componentPos);
        } else if (innerFollowStatus === 1 && !isHideFollowed) {
            commonShowLog(status, 'cancel_follow', nid, screen, '15265', true, componentPos);
        }
    }, [innerFollowStatus]);

    const onClick = e => {
        e.preventDefault();
        e.stopPropagation();

        if (innerFollowStatus === FollowEnum.PENDING) {
            return;
        }

        commonClickLog(
            status,
            innerFollowStatus ? 'cancel_follow' : 'follow',
            nid,
            screen,
            '15265',
            true,
            componentPos
        );

        if (onClickFollow) {
            onClickFollow(); // 打点

            // 鸿蒙端内能关注和取消关注 安卓以及IOS端内不变
            if (!boxjsUtils.platformInfo.isHarmonyBox) {
                return;
            }
        }


        if (is_login) {
            setInnerFollowStatus(FollowEnum.PENDING);
            clickFollow({
                template,
                type: type,
                status: innerFollowStatus,
                third_id: third_id,
            }).then((nextStatus) => {
                setInnerFollowStatus(nextStatus);
                updatePageData && updatePageData();
            });
        } else {
            boxjsUtils.login().then(() => {
                // 一定延时后调用(解决安卓11.20这个逻辑运行不稳定的问题)
                window.setTimeout(() => {
                    updatePageData && updatePageData();
                }, 500);
            });
        }
    };

    const getRootClass = cls('follow-btn-wrapper', {
        [className]: className,
        'is-icon-style': isIconStyle,
        'is-default-style': !isIconStyle,
        [followObj.className[innerFollowStatus]]: true,
    });

    return (
        <div className={getRootClass} onClick={onClick}>
            {followObj.text[innerFollowStatus]}
        </div>
    );
}

export default FollowBtn;
