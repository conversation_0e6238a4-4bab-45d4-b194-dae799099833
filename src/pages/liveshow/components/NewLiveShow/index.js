/**
 * @file 直播落地页入口（新版）
 * <AUTHOR>
 * @date 2022-04-19 14:00:18
 */

import React, {useEffect, useState, useRef, useCallback} from 'react';
import {execInvoke, getInvokeInfo, setCommonParams, createMcpWxTag, getExitInfo} from '@baidu/mcp-sdk';
import './index.less';
import './tomas.less';
import cls from 'classnames';
import ignoreReflow from '@/pages/liveshow/utils/ignoreReflow';
import {env} from '@/utils/env';
import {registerPageHidden} from '@/utils/index';
import {
    LiveStatus,
    appLiveScheme,
    invokeKeys,
    APP_NAME_SCENE,
    supportMcpInvoke,
    needGetMcpInfo
} from '@/pages/liveshow/config/const';
import {PageStore, isFromHonorBrowser} from '@/pages/liveshow/service/register';
import {watchOrientation} from '@/utils/scrollApprove';
import {getAppSchemeHead, getUrlParam, getAppSource} from '@/pages/liveshow/utils/getUrlParam';
import {matrixAppSchemeHeaderList, reflow} from '@/pages/liveshow/utils/reflow';
import {commonShowLog, commonClickLog, commonStartTimingLog, commonEndTimingLog, cnyShowLog} from '@/pages/liveshow/log';
import ReflowBtn from '../common/ReflowBtn';
import StaticPendant from '../common/StaticPendant';
import ShareTip from '../common/ShareTip';
import ChatMsg from './ChatMsg';
import ChatBar from './ChatBar';
import Dialog from './Dialog';
import LiveRoomHead from './LiveRoomHead';
import LiveVideo from './LiveVideo';
import LiveList from './LiveList';
import LiveRoomClosed from './LiveRoomClosed';
import BottomAuthorInfo from './BottomAuthorInfo';
import CNYBottomBar from './CNYBottomBar';

// 关于tag=baijiahao，因为是很久以前的逻辑，pm和开发人员已经不在了，所以只能保留原有逻辑
// 1. 在百家号APP上，做了一个视频切片，展示特等时间区间的直播
// 2. tag=baijiahao 且 直播结束有回放时，只展示视频播放器
// 3. tag=baijiahao时，不会展示付费等提示信息
const isBaijiahaoTag = PageStore.queryParams.tag === 'baijiahao';
const isMapAttractions = +PageStore.queryParams.template_type === 1; // 地图景点进入直播的情况
const isAutoPlay = +PageStore.queryParams.auto_play === 1;
const appSchemeHead = getAppSchemeHead();
// 回流矩阵场景 || iOS Lite H5 页内观看
const isMatrixApp = matrixAppSchemeHeaderList.includes(appSchemeHead) || (env.isIOS && env.isLiteBox);
// 不执行回流 / 不创建微信开放标签 / 不展示5s回流弹窗
const noreflow = PageStore.queryParams.noreflow === 'all';
// 回流渠道
const source = getAppSource() || PageStore.queryParams.source || '';

let isLiveEndNoReview = false;
// 当前屏幕 1横屏 2竖屏（默认）
let curBrowserScreen = 2;
// 当前页面 1播放器页 2推荐列表页
let curPage = 1;
// touchstart screenY位置
let startY = -1;
// touchend screenY位置
let endY = -1;

// 调起位信息（mcp内配置API返回）此处因为在触发播放器事件后，导致状态信息清空
let invokeInfo = {};

window.console.log('test');

function SwiperRender({
    data,
    refresh,
    showRecommend,
    showRecMoreBtn,
    onClickShowModel,
    recommendFirstShow,
    setRecommendFirstShow,
    setInitStatus,
    isInitStatus,
    invokeInfo,
    execMcpInvoke,
    isCNY
}) {
    const showHoverReflow = true; // 展示二屏后默认展示
    const {status, showChatTab, nid, screen} = data;
    const $swiperWrapper = useRef();
    const $innerScroll = useRef();
    // 是否展示右上角分享提示
    const [showTip, setShowTip] = useState(false);
    // 是否展示回流按钮 荣耀白牌不展示回流按钮
    const hasReflowBtn = data.hasReflowBar && !ignoreReflow && !env.isBaiduChillin
                        && !env.isTomas && !isFromHonorBrowser;
    // const { run } = useThrottleFn(() => setHoverReflowFun(), { wait: 100 });

    // 是否展示第二屏内容 荣耀白牌不展示推荐列表 结束态仍展示 CNY不展示第二屏
    const showSecondScreen = (showRecommend && !isFromHonorBrowser && !isCNY) || isLiveEndNoReview;
    curPage = isLiveEndNoReview ? 2 : 1;
    if (!recommendFirstShow && curPage === 2) {
        setRecommendFirstShow(true);
    }

    useEffect(() => {
        initAddEvent();
    }, []);

    // 推荐列表中前四个元素完全展现时 展示悬浮回流按钮
    // const setHoverReflowFun = () => {
    //     if (!hasReflowBtn) {
    //         return;
    //     }

    //     const targetItem = document.querySelector('.live-list-wrapper .item-wrapper.item3');
    //     const scrollBox = $innerScroll.current;
    //     const screenHeight = document.documentElement.clientHeight;

    //     if (targetItem && scrollBox) {
    //         const showBtn = (targetItem.offsetTop - scrollBox.scrollTop) <= screenHeight;
    //         setHoverReflow(showBtn);
    //     }
    // }

    // 切换页面
    const swiperScreen = value => {
        curPage = value;
        if (!recommendFirstShow && value === 2) {
            setRecommendFirstShow(true);
        }

        const swiperDom = $swiperWrapper.current;
        swiperDom.style.transform = `translate(0px, -${swiperDom.clientHeight * (value - 1)}px)`;
    };

    const initAddEvent = () => {
        const element = $swiperWrapper.current;
        ['touchstart', 'touchend', 'touchmove'].forEach(item => {
            element.addEventListener(item, (e) => handleTouchEvent(e, item), {passive: false});
        });
    };

    const handleTouchEvent = (e, eventType) => {

        // 当前屏幕为横屏 或者 只有一屏的时候不切换页面
        if (curBrowserScreen === 1 || isLiveEndNoReview) {
            return;
        }

        // 点击的是播放器controls
        const playerBottom = document.querySelector('#video-box .mp-bottom');
        if (playerBottom && playerBottom.contains(e.target)) {
            return;
        }

        // 点击的是消息区
        const msg = document.querySelector('.chat-msg-wrapper');
        if (msg && msg.contains(e.target)) {
            return;
        }

        e.stopPropagation();
        const element = $innerScroll.current;

        if (element) {
            const {scrollTop} = element;

            if (eventType === 'touchstart') {
                // 如果当前是第二屏且页面没有滚动到顶部 不切换页面
                if (curPage === 2 && scrollTop > 0) {
                    return;
                }

                if (e.touches[0]) {
                    startY = e.touches[0].screenY;
                }

                return;
            }

            if (eventType === 'touchmove') {
                if (e.touches[0]) {
                    endY = e.touches[0].screenY;
                }

                // 阻止浏览器默认事件
                const isPull = startY < endY;
                const needCancel = curPage === 1
                    || curPage === 2 && isPull && scrollTop === 0;

                if (e.cancelable && needCancel) {
                    e.preventDefault();
                }

                return;
            }
        }


        if (eventType === 'touchend') {
            // 直播结束没有回放
            if (isLiveEndNoReview) {
                return;
            }

            if (startY > -1 && endY > -1) {
                const distance = startY - endY;
                if (Math.abs(distance) > 100) {
                    if (distance > 0) {
                        swiperScreen(2);
                    }
                    else {
                        swiperScreen(1);
                    }
                }
            }

            startY = -1;
            endY = -1;
            return;
        }
    };

    const handleInvoke = key => {
        // 判断仅在支持MCP时，统一执行调起
        if (!supportMcpInvoke) {
            return;
        }

        execMcpInvoke(key);
    };

    // 播放器模块
    const VideoScreen = () => {
        return (
            <div className="page-screen video-screen">
                {!isLiveEndNoReview && (
                    <React.Fragment>
                        {/* 播放器 & 暂停遮罩 */}
                        <LiveVideo
                            {...data}
                            isBaijiahaoTag={isBaijiahaoTag}
                            updatePageData={refresh}
                            onClickShowModel={onClickShowModel}
                            setInitStatus={setInitStatus}
                            isInitStatus={isInitStatus}
                            isAutoPlay={isAutoPlay}
                            handleInvoke={handleInvoke}
                            isMatrixApp={isMatrixApp}
                            invokeInfo={invokeInfo}
                            isCNY={isCNY}
                            noreflow={noreflow}
                        />

                        {/* 头部信息模块 */}
                        <LiveRoomHead
                            {...data}
                            hasReflowBtn={hasReflowBtn}
                            updatePageData={refresh}
                            onClickShowModel={onClickShowModel}
                            swiperScreen={swiperScreen}
                            showRecMoreBtn={showRecMoreBtn}
                            componentPos='1'
                            handleInvoke={handleInvoke}
                            invokeInfo={invokeInfo}
                            isMatrixApp={isMatrixApp}
                            isCNY={isCNY}
                        />

                        {/* im消息模块 此处优先滑动事件 */}
                        <div className={cls('im-wrapper', {
                            'im-wrapper-cny': isCNY
                        })}
                        >
                            {showChatTab && !isInitStatus && <ChatMsg isCNY={isCNY} {...data} />}
                            <StaticPendant
                                {...data}
                                className="static-pendant-wrapper"
                                isNewVersion
                            />
                        </div>

                        {
                            isCNY && !(env.isMainBox || env.isLiteBox) // 手百、lite双端端内打开cny黄山音乐节分享页不展示底bar
                                ? (
                                    <div
                                        className={'bottom-bar-wrapper cny-bottom-bar-wrapper'}
                                    >
                                        <CNYBottomBar
                                            onClickShowModel={onClickShowModel}
                                            setShowTip={setShowTip}
                                            status={status}
                                            nid={nid}
                                            screen={screen}
                                            isMatrixApp={isMatrixApp}
                                            handleInvoke={handleInvoke}
                                        />
                                    </div>
                                )
                                : (
                                    <div className='bottom-bar-wrapper'>
                                        {/* 聊天框 */}
                                        {showChatTab && !ignoreReflow && (
                                            <ChatBar
                                                onClickShowModel={onClickShowModel}
                                                setShowTip={setShowTip}
                                                status={status}
                                                nid={nid}
                                                screen={screen}
                                                isMatrixApp={isMatrixApp}
                                                handleInvoke={handleInvoke}
                                            />
                                        )}

                                        <BottomAuthorInfo
                                            {...data}
                                            updatePageData={refresh}
                                            componentPos='2'
                                            handleInvoke={handleInvoke}
                                            isMatrixApp={isMatrixApp}
                                        />
                                    </div>
                                )
                        }
                        {/* 微信内 点击聊天框分享按钮 提示浏览器打开 */}
                        {showTip && <ShareTip setShowTip={setShowTip} />}
                    </React.Fragment>
                )}
            </div>
        );
    };

    // 推荐列表 & 关播模块
    const RecommendScreen = () => {
        return (
            <div className="page-screen recommend-screen">
                <div
                    ref={$innerScroll}
                    className="inner-scroll"
                    // onScroll={() => hasReflowBtn && run()}
                >
                    {/* 关播模块 */}
                    {isLiveEndNoReview && (
                        <React.Fragment>
                            {hasReflowBtn && (
                                <ReflowBtn
                                    type="top-btn"
                                    className="end-reflow-btn"
                                    text="APP内播放"
                                    onClickShowModel={onClickShowModel}
                                    status={status}
                                    nid={nid}
                                    screen={screen}
                                    outerSchemeParams={data.schemeParams}
                                    handleInvoke={handleInvoke}
                                    invokeInfo={invokeInfo}
                                />
                            )}
                            <LiveRoomClosed {...data} />
                        </React.Fragment>
                    )}

                    {/* 推荐模块 */}
                    {showRecommend && !isFromHonorBrowser && !isCNY && (
                        <LiveList
                            status={status}
                            nid={nid}
                            screen={screen}
                            recommendFirstShow={recommendFirstShow}
                            outerSchemeParams={data.schemeParams}
                            isMatrixApp={isMatrixApp}
                            handleInvoke={handleInvoke}
                            invokeInfo={invokeInfo}
                            noreflow={noreflow}
                        />
                    )}
                </div>

                {/* 回流按钮 */}
                {hasReflowBtn && showHoverReflow && (
                    <ReflowBtn
                        type="flot-btn"
                        className="hover-reflow-btn"
                        onClickShowModel={onClickShowModel}
                        status={status}
                        nid={nid}
                        screen={screen}
                        outerSchemeParams={data.schemeParams}
                        handleInvoke={handleInvoke}
                        invokeInfo={invokeInfo}
                    />
                )}
            </div>
        );
    };

    return (
        <div
            className="screen-wrapper-container"
            ref={$swiperWrapper}
        >
            {!isLiveEndNoReview && VideoScreen()}
            {showSecondScreen && RecommendScreen()}
        </div>
    );
}
function NewLiveShow({data, refresh}) {
    const {
        tiebaLiveCmd,
        isPaid,
        status,
        screen,
        nid,
        back_flow = {},
        has_recommend,
        background_url,
        coverImg,
        room_flag, // cny直播间
        concert_cny_config = {} // cny云控内容
    } = data;
    // 直播结束没有回放
    isLiveEndNoReview = status === LiveStatus.END_NO_REVIEW;
    // 是否为直播中
    const isLive = status === LiveStatus.LIVEING;
    // CNY 新增room_flag，concert_cny_config = {},有 该字段且为"concert_cny_new"标识是春日直播间
    const isCNY = !!room_flag && room_flag === 'concert_cny_new';
    // 是否展示推荐列表
    const showRecommend = !!+has_recommend && !ignoreReflow; // 更多推荐入口和列表展示逻辑统一
    // 是否展示更多直播按钮 荣耀白牌不展示 更多直播 按钮  CNY直播间不展示 更多直播 按钮
    const showRecMoreBtn = showRecommend && !isFromHonorBrowser && !isCNY;
    const [recommendFirstShow, setRecommendFirstShow] = useState(false);
    // 初始化 视频未播放之前
    const [isInitStatus, setInitStatus] = useState(true);
    // 是否展示回流弹窗
    const [showModel, setShowModel] = useState({
        show: false,
        type: 'reflow'
    });
    // 封面链接
    const backgroundUrl = background_url || coverImg;
    // 是否展示全局调起蒙层 默认不展示
    const [isShowWhole, setIsShowWhole] = useState(false);
    // 此处为触发更新子组件数据更新

    // eslint-disable-next-line no-unused-vars
    const [statusInfo, setStatusInfo] = useState({});
    const showNewBackground = background_url && +screen === 1 || isCNY;
    const getRootClass = cls({
        'white-screen-patrol': true,
        'live-wrapper': true,
        [appSchemeHead]: true,
        'is-hor-video': +screen === 1,
        'is-living-video': status === LiveStatus.LIVEING,
        'is-end-no-review': isLiveEndNoReview,
        'no-recommend-list': !showRecommend,
        'is-android': env.isAndroid,
        'is-cny': isCNY
    });

    useEffect(() => {
        watchOrientation((res) => {
            curBrowserScreen = res;
            if (!recommendFirstShow && res === 1) {
                setRecommendFirstShow(true);
            }
        });

        registerPageHidden(
            () => {
                commonEndTimingLog('15266');
            },
            () => {
                commonStartTimingLog(status, '', nid, screen, '15266', true);
            }
        );
    }, []);

    useEffect(() => {
        commonShowLog(status, '', nid, screen, '15267', true);
        const cnyLiveStatus = isLive ? '1' : (isLiveEndNoReview ? '3' : '2');
        isCNY && cnyShowLog(cnyLiveStatus, '15263');
        commonStartTimingLog(status, '', nid, screen, '15266', true);
    }, [status]);

    const generateInvokeInfo = scheme => {
        const res = {};

        invokeKeys.forEach(key => {
            res[`pos_${key}`] = [
                {
                    // 更多推荐页面 统一调起直播频道
                    'share_scheme': key === 'morelive' ? appLiveScheme : scheme
                }
            ];
        });

        return res;
    };

    // 统一创建微信开放标签
    const createWxTags = useCallback(invokeInfo => {
        const {action_rule, log_id} = invokeInfo;
        const partRuleInfo = action_rule.pos_part[0];
        const moreRuleInfo = action_rule.pos_morelive[0];
        const wholeRuleInfo = action_rule.pos_whole[0];

        const wholeDom = document.querySelector('.whole-invoke');
        const authorDom = document.querySelector('.author-container');
        const usersDom = document.querySelector('.users-container');
        const reflowDom = document.querySelector('.baidu-reflow-btn');
        let reviewDom = null;
        // 回放标签
        if (!isLive) {
            reviewDom = document.querySelector('.review-info');
        }

        // 底部输入框
        const inputDom = document.querySelector('.input-like');
        // 底部礼物
        const giftDom = document.querySelector('.icon-1');
        // 底部作者
        const bottomAuthorDom = document.querySelector('.bottom-author-info-wrapper');
        // IM消息块
        const msgDom = document.querySelector('.chat-msg-wrapper');
        // 吸底按钮
        const flotDom = document.querySelector('.hover-reflow-btn');
        // 列表标题按钮
        const listTitleDom = document.querySelector('.more-live-btn');
        // 回放直播间全屏按钮遮罩
        const fullscreenMaskDom = document.querySelector('.fullscreen-btn-mask');

        // 全局
        wholeDom && createMcpWxTag({targetDom: wholeDom}, 'pos_whole', wholeRuleInfo, log_id);

        // 部分
        isLive && usersDom && createMcpWxTag({targetDom: usersDom}, 'pos_part', partRuleInfo, log_id);
        !isLive && reviewDom && createMcpWxTag({targetDom: reviewDom}, 'pos_part', partRuleInfo, log_id);

        const partDoms = [authorDom, reflowDom, inputDom, giftDom, bottomAuthorDom, msgDom, flotDom, fullscreenMaskDom];
        partDoms.forEach(dom => {
            dom && createMcpWxTag({targetDom: dom}, 'pos_part', partRuleInfo, log_id);
        });

        // 更多直播，回流到直播Tab
        listTitleDom && createMcpWxTag({targetDom: listTitleDom}, 'pos_morelive', moreRuleInfo, log_id);

    }, [isLive]);

    useEffect(() => {
        // 设置通用调起信息
        setCommonParams({
            app: 'wise',
            scene: APP_NAME_SCENE[source] || 'sharepage',
            from: '',
            ext_sid: getUrlParam('ext_sid') || '' // 透传url上sid参数
        });
        const fetchMCPData = async () => {
            // 部分端未接入口令 无schema_info
            const scheme = data && data.schema_info && data.schema_info.enter_cmd || '';
            if (scheme) {
                const invokeParams = generateInvokeInfo(scheme);

                // 获取调起位详细信息
                const info = await getInvokeInfo({
                    invoke_info: invokeParams
                });
                invokeInfo = info;
                setStatusInfo(info);
                window.console.log(getExitInfo('pos_whole'));
                // 全局蒙层是否退场
                const wholeInfo = getExitInfo('pos_whole');
                const wholeRule = (info && info.action_rule && info.action_rule.pos_whole) || '';
                if (wholeInfo && wholeInfo.isExit !== undefined && Array.isArray(wholeRule)) {
                    // 全局蒙层是否退场
                    const wholeActionFlag = (wholeRule[0] && wholeRule[0].action_flag) || 0;

                    if (wholeInfo.isExit || +wholeActionFlag !== 1) {
                        setIsShowWhole(false);
                    }
                    else {
                        setIsShowWhole(true);
                        commonShowLog(status, 'whole_mask', nid, screen, '15263', '1');
                    }
                }

                // 存在调起信息 && 安卓 && 微信 && 回流主板 新增微信开放标签校验
                if (!isMatrixApp && env.isAndroid && env.isWechat
                        && info && info.action_rule) {
                    info && createWxTags(info);
                }
            }
        };

        // 回流手百 且 支持回流时执行 !isMatrixApp
        if (needGetMcpInfo && !noreflow) {
            fetchMCPData();
        }

    }, [createWxTags, data, nid, screen, status]);

    // 执行具体调起行为
    const execMcpInvoke = async (key) => {

        // 支持url参数判断不执行回流
        if (noreflow) {
            return;
        }

        if (invokeKeys.indexOf(key) === -1) {
            window.console.log('该调起位未登记,请先行登记');
            // 执行默认调起行为
            return;
        }
        const invokeInfoForPos = invokeInfo && invokeInfo.action_rule && invokeInfo.action_rule[`pos_${key}`];

        if (!invokeInfoForPos || !Array.isArray(invokeInfoForPos) || !invokeInfoForPos[0]) {
            window.console.log('无相关调起位信息');
            // 执行默认调起 兼容接口返回异常情况
            reflow({roomId: PageStore.roomId});
            return;
        }
        try {
            const res = await execInvoke(
                `pos_${key}`,
                invokeInfoForPos[0],
                invokeInfoForPos[0].log_id
            );
            // 调起失败后新增调起兜底行为
            if ([10000, 10001, 10002, 10003].indexOf(res.status) === -1) {
                // 默认调起行为
                reflow({roomId: PageStore.roomId});
            }
            window.console.log(res.msg);
        }
        catch (e) {
            window.console.log(e);
            // 执行默认调起行为
            reflow({roomId: PageStore.roomId});
        }
    };

    const onClickShowModel = res => {
        setShowModel({
            show: res.show,
            type: res.type
        });

        if (env.isMainBox) {
            return;
        }
    };

    const handleWholeClk = () => {

        commonClickLog(status, 'whole_mask', nid, screen, '15263', '1');

        execMcpInvoke('whole');

        // 全局蒙层是否退场
        const wholeInfo = getExitInfo('pos_whole');
        // 全局蒙层点击后退场逻辑
        window.console.log(wholeInfo);
        const wholeRule = (invokeInfo && invokeInfo.action_rule && invokeInfo.action_rule.pos_whole) || '';
        if (wholeInfo && wholeInfo.isExit !== undefined && Array.isArray(wholeRule)) {
            // 全局蒙层是否退场
            const wholeActionFlag = (wholeRule[0] && wholeRule[0].action_flag) || 0;

            if (wholeInfo.isExit || +wholeActionFlag !== 1) {
                setIsShowWhole(false);
            }
            else {
                setIsShowWhole(true);
            }
        }
    };

    const getMainContent = () => {
        // 百家号 直播结束有回放
        const isBjhReviewByTag = isBaijiahaoTag && status === LiveStatus.END_HAS_REVIEW;

        if (isBjhReviewByTag) {
            return (
                <LiveVideo
                    {...data}
                    className='center'
                    isBaijiahaoTag={isBaijiahaoTag}
                    updatePageData={refresh}
                    onClickShowModel={onClickShowModel}
                    setInitStatus={setInitStatus}
                    isInitStatus={isInitStatus}
                    isAutoPlay={isAutoPlay}
                    noreflow={noreflow}
                />
            );
        }

        // 从百度地图景点进入直播落地页
        if (isMapAttractions) {
            return isLiveEndNoReview
                ? <LiveRoomClosed className="is-map-attractions" {...data} />
                : (
                    <React.Fragment>
                        <LiveVideo
                            {...data}
                            className='center'
                            isBaijiahaoTag={isBaijiahaoTag}
                            updatePageData={refresh}
                            onClickShowModel={onClickShowModel}
                            setInitStatus={setInitStatus}
                            isInitStatus={isInitStatus}
                            isAutoPlay={isAutoPlay}
                            noreflow={noreflow}
                        />
                        {status === LiveStatus.END_HAS_REVIEW && (
                            <LiveRoomHead
                                {...data}
                                className="is-map-attractions"
                                showAuthorInfo={false}
                                hasReflowBtn={false}
                                showRecommend={false}
                                isCNY={isCNY}
                            />
                        )}
                    </React.Fragment>
                );
        }

        // 默认样式
        return (
            SwiperRender({
                data,
                refresh,
                showRecommend,
                showRecMoreBtn,
                recommendFirstShow,
                onClickShowModel,
                setRecommendFirstShow,
                setInitStatus,
                isInitStatus,
                invokeInfo, // invokeStatusInfo
                execMcpInvoke,
                isCNY // CNY直播间
            })
        );
    };

    return (
        <div className={getRootClass}>
            {/* 全局调起蒙层 */}
            {isShowWhole && !isMatrixApp && <div className='whole-invoke' onClick={handleWholeClk}></div>}
            {/* 背景图 */}
            {
                <div
                    className={cls({'screen-bg': !showNewBackground, 'screen-bg-new': showNewBackground})}
                    style={{backgroundImage: isCNY && concert_cny_config.background_cover
                        ? `url(${concert_cny_config.background_cover})`
                        : `url(${backgroundUrl})`}}
                ></div>
            }

            {/* 页面内容 */}
            {getMainContent()}

            {/* 回流弹窗 */}
            <Dialog
                showModel={showModel}
                tiebaLiveCmd={tiebaLiveCmd}
                isPaid={isPaid}
                onClickShowModel={onClickShowModel}
                status={status}
                nid={nid}
                screen={screen}
                outerSchemeParams={data.schemeParams}
                backFlow={back_flow}
                handleInvoke={() => execMcpInvoke('part')}
                invokeInfo={invokeInfo}
                isCNY={isCNY}
                concertCnyConfig={concert_cny_config}
            />
        </div>
    );
}

export default NewLiveShow;
