/**
 * @file 直播落地页弹窗组件（新版）
 * <AUTHOR>
 * @date 2022-04-19 14:00:18
 */

import React, { useEffect } from 'react';
import { Button, Dialog } from '@baidu/nano-react';
import { getAppName, getAppSchemeHead, getUrlParam} from '@/pages/liveshow/utils/getUrlParam';
import { reflow, matrixAppSchemeHeaderList} from '@/pages/liveshow/utils/reflow';
import { PageStore } from '@/pages/liveshow/service/register';
import { commonShowLog, commonClickLog, reflowCallbackLog, cnyClickLog} from '@/pages/liveshow/log';
import { env } from '@/utils/env';
import {isLiveDomain} from '@/utils/index';
import {isAndroid} from '@baidu/xbox-native/detect';
import {getWxConfig} from '../../../service/api';
import {createWxTag} from '@baidu/ug-invoke-app';
import {initWxTag} from '@/utils/initWxTag';
import {createMcpWxTag} from '@baidu/mcp-sdk';
import {supportMcpInvoke} from '@/pages/liveshow/config/const';
import cls from 'classnames';
import dialogImg from './img/dialogImg.png';
import './index.less';

const appName = getAppName();
let textMap = {
    reflow: {
        align: 'left',
        title: '直播邀请',
        content: `该直播使用最新版本${appName}APP观看体验更好，我在${appName}APP等你，快来看我的直播吧。`,
        btn_title: `打开${appName}APP`,
    },
    pay: {
        align: 'center',
        title: '购买付费直播',
        content: `该直播为付费内容\n请打开最新版${appName}APP进行购买`,
        btn_title: `打开${appName}APP`,
    },
};
// 回流矩阵场景
const schemeHead = getAppSchemeHead();
const isMatrixApp = matrixAppSchemeHeaderList.includes(schemeHead);

function LiveDialog(props) {
    const {
        showModel,
        onClickShowModel,
        isPaid,
        tiebaLiveCmd,
        status,
        nid,
        screen,
        outerSchemeParams = {},
        backFlow = {},
        handleInvoke,
        invokeInfo,
        isCNY,
        concertCnyConfig = {}
    } = props;
    const { show, type } = showModel;
    const isBackFlow = Object.keys(backFlow).length > 0; // 是否有云控

    if (!show) {
        return '';
    }
    // cny活动弹窗图片地址
    const cnyDialogImg = dialogImg;
    // 安卓微信场景新增relative属性
    const isShowWeTag = !isMatrixApp && isAndroid() && env.browserType === 'wechat';
    const reflowScheme = reflow({
        roomId: PageStore.roomId,
        tiebaCmd: tiebaLiveCmd,
        isPaid: isPaid,
        outerSchemeParams,
        status,
        queryParams: {
            pop_name: type === 'pay' ? 'pay_live_payment_plugin' : ''
        }
     }, false, true);

    useEffect(() => {
        commonShowLog(status, 'windows_btn', nid, screen, '15263', true);
    }, []);

    const onClose = () => {
        onClickShowModel({
            type: type,
            show: false,
            value: '2',
        });
    };

    const info = textMap[type];

    // 如果云控有下发分享信息，优先取云控下发数据
    if (isBackFlow) {
        info.title = backFlow.title || info.title;
        info.content = backFlow.content || info.content;
        info.btn_title = backFlow.btn_title || info.btn_title;
        info.dialog_img = backFlow.dialog_img || '';
    }

    const onClick = e => {
        if (e) {
            e.preventDefault();
            e.stopPropagation();
            commonClickLog(status, 'windows_btn', nid, screen, '15263', true);
            isCNY && cnyClickLog('3', '15263');
        }

        onClickShowModel({
            type: type,
            show: false,
            value: '1',
        });

        let obj = {
            roomId: PageStore.roomId,
            tiebaCmd: tiebaLiveCmd,
            isPaid: isPaid,
            outerSchemeParams,
            status
        };
        if (type === 'pay') {
            obj.queryParams = {
                pop_name: 'pay_live_payment_plugin',
            };
        }

        if (supportMcpInvoke) {
            handleInvoke('part');
        }
        else {
            reflow(obj);
        }
    };

    useEffect(() => {
        // async function addWeixinTag() {
        //     const targetDom = document.querySelector('.dialog-footer'); // `.${className}`
        //     window.console.log(targetDom)
        //     // 回流百度APP && 仅安卓 && 微信 && DOM节点有效时加入标签
        //     if (!isMatrixApp
        //            && isAndroid()
        //            && env.browserType === 'wechat'
        //            && targetDom
        //            && isLiveDomain
        //        ) {
        //        const res = await getWxConfig();
        //        if (+res.errno === 0 && res.data && res.data.appId) {
        //            const wxInfo = res.data;
        //            window.console.log(wxInfo);
        //            initWxTag({
        //             //    debug: true,
        //                appName: 'baiduboxapp',
        //                targetDom,
        //                wxTagId: 'weixin-dialog-footer', // 开放标签的Id
        //                wxInfo: wxInfo,
        //                scheme: reflowScheme,
        //                onClick: function () {
        //                    window.console.log('click');
        //                     commonClickLog(status, 'windows_btn', nid, screen, '15263', true);
        //                },
        //                onSucceed: function (e) {
        //                    // 微信开放标签成功
        //                 //    reflowCallbackLog(status, 'reflow_then', '', 0, '15263', true, '', 'wechat_suc');
        //                    window.console.log('success!', e);
        //                },
        //                onFailed: function (e) {
        //                    // 微信开放标签失败
        //                 //    reflowCallbackLog(status, 'reflow_then', '', 0, '15263', true, '', 'wechat_fail');
        //                    window.console.log('failed!', e);
        //                    onClick();
        //                }
        //            })
        //        }
        //     }
        // }
        // addWeixinTag();

        if (env.isAndroid && env.isWechat && !isMatrixApp && invokeInfo && invokeInfo.action_rule) {
            const {action_rule, log_id} = invokeInfo;
            // 创建微信开放标签
            const btnDom = document.querySelector('.dialog-footer');
            btnDom && createMcpWxTag({targetDom: btnDom}, 'pos_part', action_rule.pos_part[0], log_id);
        }
    }, [invokeInfo]);

    return (
        <Dialog
            className="dialog-wrapper"
            title= {isCNY || backFlow ? '' : info.title} // 通用能力title单写
            closeType='inside'
            showCancel
            showClose
            onClose={onClose}
        >
            {
                isCNY ? (
                    <div>
                        {
                            cnyDialogImg && (
                                <div
                                    className="CNY-bg"
                                    style={{backgroundImage: `url(${cnyDialogImg})`}}
                                    >
                                </div>
                            )
                        }
                        <div className='CNY-title'>黄山春日音乐嘉年华邀请</div>
                    </div>
                ) : (
                    <div>
                        {
                            info.dialog_img && (
                                <div
                                    className="CNY-bg"
                                    style={{backgroundImage: `url(${info.dialog_img})`}}
                                    >
                                </div>
                            )
                        }
                        <div className='CNY-title'>
                            {info.title}
                        </div>
                    </div>
                )
            }
            <div
                className="dialog-body"
                style={{ textAlign: info.align }}
            >
                {
                    isCNY && concertCnyConfig.totast_title
                    ? `${concertCnyConfig.totast_title}`
                    : `${info.content}`
                }
            </div>
            <div className="dialog-footer"
                style={{position: `${isShowWeTag ? 'relative' : 'none'}`}}
            >
                <Button
                    className={cls("dialog-btn", {
                        "dialog-btn-breath" : isCNY || isBackFlow,
                    })}
                    type='primary'
                    onClick={onClick}
                >
                    {
                        isCNY ? '去看嘉年华' : info.btn_title
                    }
                </Button>
            </div>
        </Dialog>
    );
}

export default LiveDialog;
