/**
 * @file 直播状态组件（新版）
 * <AUTHOR>
 * @date 2022-04-19 14:00:18
 */

import React from 'react';
import './index.less';
import { LiveStatusObj } from '@/pages/liveshow/config/const';

function LiveState(props) {
    const { status, text = '' } = props;
    const liveStatusStr = LiveStatusObj[String(status)];

    return (
        <div className={`live-state-wrapper ${liveStatusStr}`}>
            {liveStatusStr === 'living' && (
                <div className='living-icon'>
                    <span className='first'></span>
                    <span className='second'></span>
                    <span className='third'></span>
                </div>
            )}
            {text && <span className='text'>{text}</span>}
        </div>
    );
}

export default LiveState;
