.swiper-box {
    width: 3.74rem;
    display: flex;
    position: relative;
    margin-bottom: .09rem;

    .swiper {
        width: 3.74rem;
        display: flex;
        position: relative;

        .swiper-wrapper {
            position: relative;

            .swiper-slide {
                width: .75rem;
                margin-left: 0 !important;
            }
        }
    }
}

.prize-item {
    width: .75rem;
    height: .98667rem;
    background: url('../assets/img/prize_item_bg.png') no-repeat center/100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .prize-img {
        width: .47368rem;
        height: .55648rem;
        margin-top: .091rem;
        // background: url('../assets/img/red_pocket.png') no-repeat center/100% 100%;
        background-image: url('../assets/img/red_pocket.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
    }

    .prize-name {
        width: .64rem;
        color: #4D4D4D;
        font-family: "PingFang SC";
        font-size: .12667rem;
        line-height: .128rem;
        margin-top: .091rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: center;
    }
}

.draw-btn {
    width: 2.4948rem;
    height: .7689rem;
    margin: auto;
    background: url('../assets/img/btn_bg.png') no-repeat center;
    background-size: 100% 100%;

    .draw-btn-text {
        position: relative;
        top: .25rem;
        left: .7285rem;
        width: 1.076667rem;
        height: .25rem;
    }
}

.draw-btn-finished {
    background: url('../assets/img/draw_btn_finished_bg.png') no-repeat center;
    background-size: 100% 100%;

    .draw-btn-text-finished {
        left: .37rem;
        width: 1.75rem;
        height: .25rem;
    }
}

.prize-btn {
    position: absolute;
    top: 1.9rem;
    align-self: flex-end;
    width: .45333rem;
    height: .26667rem;
    background: url('../assets/img/prize_btn_bg.png') no-repeat center/100% 100%;
}
