/**
 * @file  武侠歌会-抽奖
 * <AUTHOR>
 */

import React, {useCallback, useEffect, useState} from 'react';
import cls from 'classnames/bind';
import Swiper from 'swiper/dist/js/swiper.min.js';
import 'swiper/dist/css/swiper.min.css';
import ShareBtn from '../ShareBtn';
import UserList from '../UserList';
import {getLotteryResult} from '../../../service/api';
import {wuXiaShowLog, wuXiaClickLog} from '@/pages/liveshow/log';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import style from './index.module.less';

const cx = cls.bind(style);

const Lottery = ({newVisual, data, setToastType, setShowGiftModel}) => {
    const {prize = [], invite_task = {}} = newVisual;

    const [drawNum, setDrawNum] = useState(invite_task.draw_number);
    const [inviteTask, setInviteTsak] = useState(invite_task);

    useEffect(() => {
        const click_btn = drawNum >= 1
            ? 'wait_choujiang'
            : inviteTask.button === '明日再来' || inviteTask.button === '抽奖机会已用完'
                ? 'choujiang_finish'
                : 'invite_friend';
        wuXiaShowLog('choujiang_btn', 15263, {
            click_btn: click_btn
        });
    }, [])

    useEffect(() => {
        new Swiper(`.swiper`, {
            autoplay: {
                delay: 500,
            },
            speed: 800,
            loop: true,
            initialSlide: 0,
            slidesPerView: 'auto',
            spaceBetween: '2.2%',
            allowTouchMove: false,
        });
    }, [])

    // 跳转到资产钱包页
    const jumpToWallet = () => {
        window.console.log('跳转钱包页');
        boxjsUtils.openEasyBrowse('https://ucenter.baidu.com/ucenterv4/page/act/e3NqA/assetIndex?serVer=v4&jumpHome=1&immerseBrowser=1', 0);
    };

    // 抽奖
    const drawLottery = useCallback(async () => {
        wuXiaClickLog('choujiang_btn', 15263, {
            click_btn: 'wait_choujiang'
        });
        const res = await getLotteryResult();

        // 以预约结果为准
        if (+res?.errno === 0 && res?.data) {
            setShowGiftModel({
                show: true,
                type: +res.data?.prize?.type === 1 ? 'money' : (+res.data?.prize?.type === 2 ? 'real' : 'prize'),  // 1现金2实体礼物3虚拟礼物
                data: res.data
            });
            setDrawNum(res.data?.invite_task.draw_number);
            setInviteTsak(res.data?.invite_task || {});
        }
        else {
            setToastType('lottery-fail');
        }
    }, [])


    return (
        <>
            <div className={cx('swiper-box')}>
                <div className={cx('swiper')}>
                    <div className={cx('swiper-wrapper')}>
                        {
                            prize.map((item, index) => {
                                return (
                                    <div key={index} className={cx('swiper-slide')}>
                                        <div className={cx('prize-item')}>
                                            <div
                                                className={cx('prize-img')}
                                                style={{backgroundImage: `url("${item.image}")`}}
                                            ></div>
                                            <div className={cx('prize-name')}>{item.name}</div>
                                        </div>
                                    </div>
                                )
                            })
                        }
                    </div>
                </div>
            </div>
            {
                drawNum >= 1
                    ? <div className={cx('draw-btn')} onClick={drawLottery}>
                        <img src='https://mpics.bdstatic.com/qm/202407/pic_XqgjPR_1720438071.png' className={cx('draw-btn-text')}></img>
                    </div>
                    : (
                        inviteTask.button === '明日再来' || inviteTask.button === '抽奖机会已用完'
                            ? (
                                <div className={cx('draw-btn', 'draw-btn-finished')}>
                                    <img
                                        src={
                                            inviteTask.button === '明日再来'
                                                ? 'https://mpics.bdstatic.com/qm/202407/pic_sYq4QC_1720753078.png'
                                                : 'https://mpics.bdstatic.com/qm/202408/pic_w74n0s_1723712726.png'
                                        }
                                        className={cx('draw-btn-text', {
                                            'draw-btn-text-finished': inviteTask.button === '抽奖机会已用完',
                                        })}></img>
                                </div>
                            )
                            : <ShareBtn data={data} setToastType={setToastType} inviteTask={inviteTask} />
                    )
            }
            <UserList
                allData={data}
                userList={inviteTask.list}
            />
            <div
                className={cx('prize-btn')}
                onClick={jumpToWallet}
            ></div>
        </>


    );
};

export default Lottery;
