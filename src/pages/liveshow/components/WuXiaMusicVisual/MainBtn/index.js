/**
 * @file  MainBtn 主按钮
 * <AUTHOR>
 */

import React, {useCallback, useEffect, useRef, useState} from 'react';
import cls from 'classnames/bind';
import {subscribe} from '@/pages/liveshow/service/api';
import {wuXiaShowLog, wuXiaClickLog} from '@/pages/liveshow/log';
import Lottery from '../Lottery';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import style from './index.module.less';

const cx = cls.bind(style);

const MainBtn = ({
    data,
    handleInvoke,
    entranceAPP,
    subStatus,
    setToastType,
    refresh,
    handleSubscribeAndFollow,
    setShowGiftModel,
    showCountDown,
    isAnti
}) => {
    const {
        new_visual = {},
        user_info = {}
    } = data;

    const isLogin = user_info.is_login; // 是否登录 0:未登录，1:已登陆
    const subscribeBtnStatus = useRef(subStatus);

    useEffect(() => {
        subscribeBtnStatus.current = subStatus;
    }, [subStatus]);

    // 打点用按钮状态
    let btnLogStatus = '';

    useEffect(() => {
        // 预约按钮展现打点（右上角按钮）
        const subscribeBtnStatus = subStatus === 1 ? 'subscribe_success' : 'subscribe';
        entranceAPP && wuXiaShowLog('subscribe_btn', 15263, {
            click_btn: subscribeBtnStatus
        });
    }, [subStatus]);



    const handleClk = useCallback(async () => {
        wuXiaClickLog('choujiang_btn', 15263, {
            click_btn: 'now_yuyue'
        });
        if (isLogin) {
            await handleSubscribeAndFollow();
        } else {
            // 没有登录去登录,登录成功后刷新数据
            boxjsUtils.login().then(() => {
                // 一定延时后调用(解决安卓11.20这个逻辑运行不稳定的问题)
                window.setTimeout(() => {
                    refresh();
                }, 500);
            });
        }
    }, [isLogin, refresh]);

    const invokeApp = useCallback(() => {
        // 按钮点击
        wuXiaClickLog('port_btn', 15263, {
            click_btn: btnLogStatus
        });

        handleInvoke && handleInvoke();
    }, []);


    // 按钮群
    const btnGroup = useCallback(() => {
        // 已预约
        if (+subStatus === 1) {
            // 矩阵外
            if (!entranceAPP) {
                wuXiaShowLog('port_btn', 15263, {
                    click_btn: 'subscribe_success'
                });
                btnLogStatus = 'subscribe_success';
                return (
                    <div className={cx('main-btn')} onClick={invokeApp}>
                    {/* // todo：按钮文案确认 */}
                    <span className={cx('txt-go-to-app')}></span>
                    <span className={cx('bubble')}></span>
                </div>
                );
            }

            return (
                <div className={cx('guide-share')}>
                    <div
                        className={cx('lottery-title')}
                        style={{backgroundImage: `url(${new_visual.lottery_title})`}}
                    ></div>
                    <Lottery
                        newVisual={new_visual}
                        data={data}
                        setToastType={setToastType}
                        setShowGiftModel={setShowGiftModel}
                    />
                </div>
            );
        }

        // 其他场景 均未预约
        // 非矩阵场景
        if (!entranceAPP) {
            wuXiaShowLog('port_btn', 15263, {
                click_btn: 'subscribe'
            });
            btnLogStatus = 'subscribe';

            return (
                <div className={cx('main-btn')} onClick={invokeApp}>
                    {/* // todo：按钮文案确认 */}
                    <span className={cx('txt-subscribe-for-gift')}></span>
                    <span className={cx('bubble')}></span>
                </div>
            );
        }
        wuXiaShowLog('choujiang_btn', 15263, {
            click_btn: 'now_yuyue'
        });

        return (
            <div className={cx('main-btn')} onClick={handleClk}>
                <div className={cx('txt-subscribe-now')}></div>
                <span className={cx('bubble')}></span>
            </div>
        );

    }, [subStatus, entranceAPP, handleClk]);

    const cancelSubscribe = () => {
        if (isLogin) {
            if (isAnti.current && subscribeBtnStatus.current === 0) {
                setToastType('subscribe-fail');
                return;
            }
            const requestStatus = subscribeBtnStatus.current === 1 ? 0 : 1; // 1:预约，0：取消预约

            const btnText = subscribeBtnStatus.current === 1 ? 'subscribe_success:' : 'subscribe';

            subscribe(requestStatus)
                .then(() => {
                    window.console.log('修改预约成功');
                    subscribeBtnStatus.current = requestStatus;
                    +requestStatus === 1
                        ? setToastType('subscribe-success')
                        : setToastType('cancel-subscribe-success');
                })
                .catch(() => window.console.log('修改预约失败'));
            // 取消预约按钮打点
            wuXiaClickLog('subscribe_btn', 15263, {
                click_btn: btnText
            });
        } else {
            // 没有登录去登录,登录成功后刷新数据
            boxjsUtils.login().then(() => {
                // 一定延时后调用(解决安卓11.20这个逻辑运行不稳定的问题)
                window.setTimeout(() => {
                    refresh();
                }, 500);
            });
        }
    }

    return (
        <div className={cx('wuxia-main-btn-wrap', {
            'lottery-btn': +subStatus === 1 && entranceAPP,
            'no-countdown-btn': entranceAPP && +showCountDown === 0 && +subStatus === 0
        })}>
            {
                entranceAPP && (
                    <div
                        className={cx('count-tips', {
                            'subscribed-btn': subStatus === 1,
                            'no-countdown-subscribed-btn': +showCountDown === 0 && subStatus === 0,
                            'subscribed-btn-color': subscribeBtnStatus.current === 1
                        })}
                        onClick={cancelSubscribe}
                    >
                        {subscribeBtnStatus.current === 1 ? '已预约' : '未预约'}
                    </div>
                )
            }
            {btnGroup()}
        </div>
    );
};

export default MainBtn;
