/**
 * @file 新版分享页视觉visual_id 为 5
 * <AUTHOR>
 */

import React, {useEffect, useCallback, useState, useRef} from 'react';
import {PageStore} from '@/pages/liveshow/service/register';
import {matrixAppSchemeHeaderList, refolwForConfig} from '@/pages/liveshow/utils/reflow';
import {getAppSchemeHead, getUrlParam} from '@/pages/liveshow/utils/getUrlParam';
import {getPushStatus, canIUseGetPushStatus, guidePush, canIUseGuidePush} from '@baidu/ug-matrix';
import {TemplateEnum, logTxtMap} from '@/pages/liveshow/config/const';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import {execInvoke, getInvokeInfo, setCommonParams, createMcpWxTag} from '@baidu/mcp-sdk';
import {env} from '@/utils/env';
import {SubscribeAndFollow} from '@/pages/liveshow/service/api';
import {wuXiaShowLog, wuXiaClickLog} from '@/pages/liveshow/log';
import Dialog from '@/pages/liveshow/components/NewLiveShow/Dialog';
import GiftDialog from './GiftDialog/index'
import {initShare} from '@/utils/share';
import cls from 'classnames/bind';
import LiveVideo from './LiveVideo';
import CountDown from './CountDown';
import Toast from './Toast';
import MainBtn from './MainBtn';
import styles from './index.module.less';


const cx = cls.bind(styles);
const roomId = getUrlParam('room_id');
// const canAutoSubscribe = getUrlParam('source') !== 'yxtz' && getUrlParam('source') !== 'dy_push';
const shareCuk = getUrlParam('share_cuk') || '';

// mcp 场景信息
const APP_NAME_SCENE = {
    'baiduboxapp': 'sharepage',
    'lite': 'sharepagelite', // 新增scene
    '': 'sharepage'
};

// 预约页外部资源
const sids = ['tiebak', 'wangpan', 'ditu'];

// 调起位
const posKey = 'pos_part';

const source = PageStore.queryParams.source;
const appSchemeHead = getAppSchemeHead();

// 回流矩阵场景
const isMatrixApp = matrixAppSchemeHeaderList.includes(appSchemeHead);
const entranceAPP = (env.isMainBox && !isMatrixApp) || (env.isLiteBox); // 武侠歌会：手百、极速版

// 仅在live.baidu.com 域名下安卓回手百新增微信开放标签
const isLiveDomain = location.host === 'live.baidu.com';

let invokeInfo = {}; // mcp调起信息

const WuXiaMusicVisual = ({data, refresh}) => {
    const {
        template,
        status,
        schema_info, // 回流NA直播间信息
        countdown,
        tiebaLiveCmd,
        screen,
        nid,
        back_flow = {},
        new_visual = {},
        share,
        user_info,
        appointment_status = 0, // 1:展示倒计时，0：不展示
    } = data;

    const {feedback_url, program_list, show_countdown, rule_url} = new_visual;
    const isLogin = user_info.is_login; // 登录状态 0:未登录，1：已登陆

    const [showToast, setShowToast] = React.useState(false);
    const [toastType, setToastType] = React.useState('');
    const [toastMessage, setToastMessage] = React.useState('');

    const isAnti = useRef(false); // 是否为黑产

    const [subStatus, setSubStatus] = useState(appointment_status); // 0 未预约 1 已预约

    window.console.log(user_info.is_login);

    useEffect(() => {
        // 进场预约
        if (entranceAPP && subStatus !== 1 && !!isLogin) {
            handleSubscribeAndFollow();
        }
    }, [user_info.is_login, appointment_status]);

    useEffect(() => {
        if (toastType) {
            setShowToast(true);
            setTimeout(() => {
                setShowToast(false);
                setToastType('');
                setToastMessage('');
            }, 3000);
        }
    }, [toastType]);


    const {enter_cmd = schema_info.enter_cmd} = new_visual;

    // 是否展示回流弹窗
    const [showModel, setShowModel] = useState({
        show: false,
        type: 'reflow',
    });

    // 是否展示礼物弹窗 预约 抽奖
    const [showGiftModel, setShowGiftModel] = useState({
        show: false,
        type: 'subscribe',
    });

    // 初始化 视频未播放之前
    const [isInitStatus, setInitStatus] = useState(true);
    const [invokeAppInfo, setInvokeAppInfo] = useState({});
    const isPreview = template === TemplateEnum.Preview; // 是否为预约直播

    useEffect(() => {
        // 初始化分享
        initShare({
            linkUrl: share.share_url,
            title: share.main_title,
            content: share.sub_title,
            source: 'huangshan_live',
            iconUrl: share.cover,
            imageUrl: share.cover,
            mediaType: 'all',
        });
    }, []);

    // 武侠歌会 预约页展示打点
    useEffect(() => {
        wuXiaShowLog('subscribe', 15263, {
            share_cuk: shareCuk
        });
    }, []);

    const jumpToNA = useCallback(() => {
        // 直播中、回放 符合条件自动跳转直播间
        if ([0, 3].indexOf(status) !== -1 && schema_info) {
            boxjsUtils.invokeSchema({schema: enter_cmd});
            return;
        }
    }, [status, schema_info]);

    const onClickShowModel = res => {
        setShowModel({
            show: res.show,
            type: res.type,
        });
    };

    const handleInvoke = () => {
        invokeApp(posKey);
    };

    useEffect(() => {
        // 非入口矩阵内 （回流主板 或 极速版）执行mcp调起逻辑
        if (!entranceAPP) {
            getMcpData(enter_cmd);
        }
    }, []);

    const getMcpData = useCallback(async schema => {
        if (!schema) {
            return;
        }

        // 设置通用调起信息
        setCommonParams({
            app: 'wise',
            scene: APP_NAME_SCENE[source] || 'sharepage',
            ext_sid: sids.indexOf(source) !== -1 ? source : '' // 贴吧、网盘、地图
        });

        // 获取调起位详细信息
        const info = await getInvokeInfo({
            'invoke_info': {
                [posKey]: [{
                    'share_scheme': schema
                }]
            }
        });
        invokeInfo = info;
        setInvokeAppInfo({...info});

        // 存在调起信息 && 安卓 && 微信 && 回流主板 新增微信开放标签校验 isLiveDomain &&
        if (isLiveDomain && !isMatrixApp && env.isAndroid && env.isWechat && info.action_rule) {
            createWxTags(info);
        }
    }, []);

    // 创建微信开放标签
    const createWxTags = invokeInfo => {
        const {action_rule: actionRule, log_id: logId} = invokeInfo;
        const liveShareInfo = actionRule[posKey][0];
        const shareContainer = document.querySelector('.huangshan-main-btn-wrap');
        if (shareContainer && liveShareInfo) {
            createMcpWxTag({targetDom: shareContainer}, posKey, liveShareInfo, logId);
        }
    };

     // mcp 调起
     const invokeApp = useCallback(async (pos) => {
        const invokeConfig = {
            appName: appSchemeHead,
            toStore: true,
            scheme: new_visual.enter_cmd,
            token: new_visual.share_token
        };
        const invokeInfoForPos = invokeInfo && invokeInfo.action_rule && invokeInfo.action_rule[pos];
        if (!invokeInfoForPos || !Array.isArray(invokeInfoForPos) || !invokeInfoForPos[0]) {
            // 执行默认调起 兼容接口返回异常情况
            refolwForConfig(invokeConfig);
            return;
        }

        try {
            const res = await execInvoke(
                pos,
                invokeInfoForPos[0],
                invokeInfoForPos[0].log_id
            );
        }
        catch (e) {
            // 执行默认调起行为
            refolwForConfig(invokeConfig);
        }
    }, [invokeInfo]);

    // 端内吊起反馈轻框
    const feedbackClick = () => {
        boxjsUtils.invokeSchema({schema: feedback_url});
    };

    // 预约并关注
    const handleSubscribeAndFollow = useCallback(async () => {
        if (isAnti.current) {
            setToastType('subscribe-fail');
            return;
        }
        const fetchStart = Date.now();

        wuXiaShowLog('port_btn', 15263, {
            api_name: 'subscribe_and_follow',
            room_id: roomId,
        });

        const res = await SubscribeAndFollow({
            roomId: roomId,
            followAnchor: 1
        });

        wuXiaShowLog('port_btn', 15263, {
            api_name: 'subscribe_and_follow_back',
            room_id: roomId,
            errno: res?.errno,
            time: Date.now() - fetchStart
        });

        // 以预约结果为准
        if (+res?.errno === 0 && +res?.data?.subscription === 1) {
            setSubStatus(1);
            // 首次预约、有弹窗、且发奖成功--弹预约弹窗
            if (res.data.first_subscription === 1 && res?.data?.tips && res.data.tips?.prize_name) {
                setShowGiftModel({
                    show: true,
                    type: 'subscribe',
                    data: res.data.tips
                });
            } else {
                // 非首次预约 ｜｜ 预约成功但发奖失败 --toast提示预约并关注成功
                if(!res.data.tips?.prize_name) {
                    wuXiaShowLog('subscribe_toast', 15263, {
                        content_txt: res?.data?.tips?.toast || "预约成功并关注主播~",
                    });
                }
                setToastType('subscribe-success');
                // setToastMessage(res?.data?.tips?.toast);
            }
            // check通知栏权限
            setTimeout(() => {
                checkPushStatus();
            }, 5000);
        }
        else {
            // 记录黑产状态
            if (res?.data?.anti === 1) {
                isAnti.current = true;
                return;
            }
            setToastType('subscribe-fail');
        }
    }, []);

    // 减产通知栏权限 端能力
    const checkPushStatus = async () => {
        const isSupport = canIUseGetPushStatus();

        if (!isSupport) {
            return;
        }
        try {
            // 成功获取是否打开
            const res = await getPushStatus();
            // 通知栏权限未打开
            if (+res.status === 0) {
                hendleGuidePush();
            }
        }
        // 获取失败
        catch(failData) {
            // 异常处理逻辑
            // {status: 101, message: 该版本不支持此端能力}
            console.log(failData);
        }
    }

    // 引导去设置页打开push
    const hendleGuidePush = async () => {
        const isSupport = canIUseGuidePush();

        if (!isSupport) {
            return;
        }
        try {
            // 成功获取是否打开
            const res = await guidePush({source: 'activity'});

            if (+res.status === 1) {
                console.log('打开PUSH失败');
            }
        }
        // 获取失败
        catch(failData) {
            // 异常处理逻辑
            // {status: 101, message: 该版本不支持此端能力}
            console.log(failData);
        }
    }

    // 打开规则页
    const openRulePage = () => {
        wuXiaClickLog('rule', 15263);
        if (!isLogin && entranceAPP) {
            boxjsUtils.login().then(() => {
                window.setTimeout(() => {
                    refresh();
                }, 500);
            });
            return;
        }
        if (!entranceAPP) {
            handleInvoke();
        }
        // todo跳转规则页
        boxjsUtils.openEasyBrowse(rule_url, 0);
    }


    return (
        <div className={`white-screen-patrol ${cx('wuxia-music-visual')}`}>
            <div className={cx('wuxia-music-cover1')}>
                <div className={cx('header')}>
                    <div className={cx('logo')}>
                        {/* todo替换logo图 */}
                        <img  src='https://mpics.bdstatic.com/qm/202407/pic_QnSwFX_1721283826.png' alt='logo'/>
                    </div>
                    <img className={cx('title')} src='https://mpics.bdstatic.com/qm/202407/pic_oviB9K_1721286176.png' alt='标题'/>
                    <div className={cx('teel-title')}></div>
                    <div
                        className={cx('rule-btn')}
                        onClick={openRulePage}
                    >规则</div>
                </div>
                <div className={cx('cover-montain')}></div>
                {/* <div className={cx('wuxia-shadow')}></div> */}
            </div>
            <div className={cx('cover-bird')}></div>

            <LiveVideo
                    {...data}
                    className='center'
                    updatePageData={refresh}
                    isBaijiahaoTag={false}
                    onClickShowModel={onClickShowModel}
                    setInitStatus={setInitStatus}
                    isInitStatus={isInitStatus}
                    isAutoPlay={false}
                    handleInvoke={handleInvoke}
                    jumpToNA={jumpToNA}
                    isPreview={isPreview}
                    showModel={showModel}
                    isLogin={isLogin}
                    entranceAPP={entranceAPP}

            />
            <div className={cx('wuxia-music-cover2', !entranceAPP && 'out-app')}>
                <CountDown
                    status={status}
                    countdown={countdown}
                    refresh={refresh}
                    subStatus={subStatus}
                    entranceAPP={entranceAPP}
                    showCountDown={show_countdown}
                />
                {
                    new_visual && (
                        <MainBtn
                            handleInvoke={handleInvoke}
                            data={data}
                            setToastType={setToastType}
                            entranceAPP={entranceAPP}
                            subStatus={subStatus}
                            setSubStatus={setSubStatus}
                            refresh={refresh}
                            setShowGiftModel={setShowGiftModel}
                            handleSubscribeAndFollow={handleSubscribeAndFollow}
                            isAnti={isAnti}
                            showCountDown={show_countdown}
                        />
                    )
                }
                {
                    entranceAPP && <div className={cx('program', {
                        'program-no-countdown': show_countdown !== 1 && +subStatus === 0
                    })}>
                        <img
                            className={cx('program-img')}
                            src={program_list.length > 0 && program_list[0].img}
                            alt='节目单'
                        />
                    </div>
                }

                <div className={cx('bottom-logo',!entranceAPP && 'no-show-program')}>
                    <img src={data.new_visual.acknowledge} alt='标题'/>
                </div>

                {
                    entranceAPP && (
                        <div className='feedback-wuxia'>
                            <img onClick={feedbackClick} src='https://mpics.bdstatic.com/qm/202403/pic_xksche_1710730962.png' alt='标题'/>
                        </div>
                    )
                }

            </div>
            {
                showToast && <Toast
                    toastType={toastType}
                    toastMessage={toastMessage}
                />

            }

            {/* 回流弹窗 */}
            <Dialog
                showModel={showModel}
                tiebaLiveCmd={tiebaLiveCmd}
                onClickShowModel={onClickShowModel}
                status={status}
                nid={nid}
                screen={screen}
                outerSchemeParams={data.schemeParams}
                backFlow={back_flow}
                handleInvoke={handleInvoke}
                invokeInfo={invokeAppInfo}
            />
            {/* 预约成功弹窗 */}
            <GiftDialog
                showGiftModel={showGiftModel}
                tiebaLiveCmd={tiebaLiveCmd}
                setShowGiftModel={setShowGiftModel}
                status={status}
                nid={nid}
                screen={screen}
            />
        </div>
    );
}

export default WuXiaMusicVisual;
