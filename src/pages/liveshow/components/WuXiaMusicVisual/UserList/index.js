/**
 * @file  武侠歌会-邀请用户list
 * <AUTHOR>
 */

import React, {useCallback} from 'react';
import {env} from '@/utils/env';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import {openShareBox} from '@/utils/share';
import cls from 'classnames/bind';

import style from './index.module.less';

const cx = cls.bind(style);

const UserList = ({userList, allData}) => {
    const {
        share, // 链接分享信息
        new_visual = {}, // 新视觉依赖物料
    } = allData;
    const data = userList || [];
    const {is_share_token = 0, share_token = ''} = new_visual;

    let list = [];
    let resData = [];
    list = Array(3).fill({});
    resData = data.length > 3 ? data.slice(0, 3) : data;
    list.splice(0, resData.length, ...resData);

    const clkShare = useCallback((avatar) => {
        if (avatar) {
            return;
        }
        if (!!is_share_token && share_token) {
            // 口令分享
            const schema = `baiduboxapp://share/doShare?params=${encodeURIComponent(JSON.stringify({'shareData': {
                'mediaType': 'all',
                'title': share.main_title,
                'content': share.sub_title,
                'linkUrl': share.share_url,
                'imageUrl': share.cover,
                'iconUrl': share.cover,
                'type': env.isIOS ? 'text' : 9,
                'textContent': share_token
            }}))}`;

            boxjsUtils.invokeSchema({schema});
        }
        else {
            openShareBox({
                linkUrl: env.isLiteBox ? share.share_url + '&source=lite' : share.share_url,
                title: share.main_title,
                content: share.sub_title,
                source: 'huangshan_live',
                iconUrl: share.cover,
            });
        }
    }, []);

    return (
        <div className={cx('user-list')}>
            {
                list.map((item, index) => {
                    return (
                        <div
                            key={index}
                            className={cx('userList-item',{
                                'userList-item-border': item?.avatar
                            })}
                            style={{backgroundImage: item?.avatar ? `url(${item?.avatar}` : `url('https://mpics.bdstatic.com/qm/202407/pic_38Ud11_1720436488.png')`}}
                            onClick={() => clkShare(item?.avatar)}
                        ></div>
                    )
                })
            }
        </div>
    );
};

export default UserList;
