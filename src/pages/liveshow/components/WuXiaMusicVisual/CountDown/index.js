/**
 * @file 倒计时组件
 * <AUTHOR> on 2024-02-01 20:59:32.
 */

import React, {useCallback, useEffect, useRef, useState} from 'react';
import {getEndStatus} from '@/pages/liveshow/service/api';
import {env} from '@/utils/env';
import styles from './index.module.less';
import cls from 'classnames';
const cx = cls.bind(styles);

const minuteSecond = 60;
const hourSecond = minuteSecond * 60;
const daySecond = hourSecond * 24;

function CountDownTimeStr({timestamp = 0}) {
    if (timestamp === null) {
        return '---';
    }
    if (timestamp < 0) {
        timestamp = 0;
    }
    const timeArray = [
        {
            unit: '天',
            secondCount: daySecond
        },
        {
            unit: '时',
            secondCount: hourSecond
        },
        {
            unit: '分',
            secondCount: minuteSecond
        },
        {
            unit: '秒',
            secondCount: 1
        }
    ];
    let time = Math.ceil(timestamp);
    let resultArray = timeArray.map((timeItem) => {
        const count = Math.floor(time / timeItem.secondCount);
        time -= count * timeItem.secondCount;
        return {
            count: count.toString().length < 2 ? `0${count}` : count,
            ...timeItem
        };
    });
    return resultArray.reduce((pre, result, index) => {
        if (pre === '') {
            if (result.count > 0 || result.unit === '秒') {
                return [
                    <span className={cx('countDownTimeNew', env.isAndroid && 'is-android')} key={`${index}-1`}>
                        {result.count.toString()[0]}
                    </span>,
                    <span className={cx('countDownTimeNew', env.isAndroid && 'is-android')} key={`${index}-3`}>
                        {result.count.toString()[1]}
                    </span>,
                    <span className={cx('countDownUnitNew')} key={`${index}-2`}>
                        {result.unit}
                    </span>
                ];
            }
            return pre;
        }
        return pre.concat([
            [
                <span className={cx('countDownTimeNew', env.isAndroid && 'is-android')} key={`${index}-1`}>
                    {result.count.toString()[0]}
                </span>,
                <span className={cx('countDownTimeNew', env.isAndroid && 'is-android')} key={`${index}-3`}>
                    {result.count.toString()[1]}
                </span>,
                <span className={cx('countDownUnitNew')} key={`${index}-2`}>
                    {result.unit}
                </span>
            ]
        ]);
    }, '');
}

function CountDown(props) {
    const {entranceAPP, showCountDown} = props;

    const [countdown, setCountdown] = useState(() => {
        return +props.countdown < 0 ? 0 : +props.countdown;
    });
    const [status, setStatus] = useState(-1);
    const refreshInterval = useRef(null);
    const [noStart, setNoStart] = useState(false); // 倒计时结束，直播未开始状态（待图）
    const timeOut = useRef(null);

    const update = useCallback(() => {
        return getEndStatus().then((data) => {
            // -1 表示直播还未开始,继续倒计时
            const newStatus = +data.status;
            const count = +data.countdown < 0 ? 0 : +data.countdown;
            const show = (newStatus === -1 && count <= 0) || newStatus === 0;
            props.setShowLiveBtn && props.setShowLiveBtn(!show);
            setNoStart(show);

            setStatus(newStatus); // 更新状态
            setCountdown(count); // 更新倒计时

            if (newStatus !== -1) {
                // 直播开始，停止定时 refresh
                clearTimeout(refreshInterval.current);
                props.refresh && props.refresh();
            }
        });
    }, [props]);

    useEffect(() => {
        update();
    }, []);

    useEffect(() => {
        if (countdown > 0) {
            timeOut.current = setTimeout(() => {
                setCountdown(countdown - 1);
            }, 1000);
        } else if (status === -1) {
            update();
            // 倒计时结束，开始每三秒请求一次直播状态
            refreshInterval.current = setInterval(() => {
                update();
            }, 3000);
        }

        // 清除计时器
        return () => {
            clearTimeout(timeOut.current);
            clearInterval(refreshInterval.current);
        };
    }, [countdown, status, update]);

    const hasCount = countdown > 0;

    return (
        <React.Fragment>
            <div className={cx('count-down-new-wuxia',{
                    'subscribed-wuxia': entranceAPP && props.subStatus === 1,
                    'no-countdown-wuxia': entranceAPP && showCountDown === 0 && props.subStatus === 0
                })}
            >
                {
                    showCountDown === 0 && (
                        <div className={cx('no-count-down', {
                            'no-count-down-inApp': entranceAPP && props.subStatus === 1,
                            'no-count-down-outApp': !entranceAPP,
                        })}></div>
                    )
                }
                {
                    showCountDown !== 0 && !noStart
                    ? (entranceAPP && props.subStatus === 1
                        ? <img  className={cx('count-text-wuxia2')} src='https://mpics.bdstatic.com/qm/202407/pic_I3Oi1o_1720763815.png'/>
                        : <img  className={cx('count-text-wuxia')} src='https://mpics.bdstatic.com/qm/202407/pic_1bwMgv_1720763875.png'/>)
                    : null
                }
                {
                    showCountDown !== 0 && (
                        noStart
                        ? <div className={cx('wuxia-waiting-box')}>
                            {
                                (!entranceAPP || props.subStatus !== 1) &&
                                    <img  className={cx('wuxia-waiting-img')} src='https://mpics.bdstatic.com/qm/202408/pic_mBxaef_1723110777.png'/>
                            }
                            <div
                                className={cx('wuxia-waiting-text', {
                                    'wuxia-waiting-text-inApp': entranceAPP && props.subStatus === 1
                                })}
                            >直播即将开始，主播正在赶来...</div>
                        </div>
                        : <div
                            className={cx(
                                'count-content-wuxia',
                                entranceAPP && props.subStatus === 1 && 'count-content-subscribed-wuxia'
                            )}
                        >
                            {hasCount && <CountDownTimeStr timestamp={countdown} />}
                        </div>
                    )
                }

            </div>
        </React.Fragment>
    );
}

export default CountDown;
