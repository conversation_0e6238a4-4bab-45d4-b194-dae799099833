@import '~@baidu/nano-theme/index.less';

@font-face {
    font-family: 'baidunumber-Medium';
    src: url(../assets/font/baidunumber-Medium.ttf) format('truetype');
}
.theme-root() !important;

.count-down-new-wuxia {
    position: relative;
    width: 100%;
    height: 2.07rem;
    background: url('../assets/img/countdown_bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin: -1.333rem auto 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-wrap: wrap;
    z-index: 2;

    &.subscribed-wuxia {
        height: 4.05667rem;
        background: url('../assets/img/countdown_bg_sub.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }

    &.no-countdown-wuxia {
        height: 2.98rem;
        background: url('../assets/img/no_countDown_bg.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }

    .count-tips {
        width: .43333rem;
        height: .18333rem;
        background: #FFF;
        border: .0067rem solid #D76700;
        border-radius: .36667rem;
        position: absolute;
        top: .177rem;
        right: .34rem;
        color: #D76700;
        font-family: "PingFang SC";
        font-size: .09333rem;
        line-height: .18333rem;
        align-content: center;
        text-align: center;
        z-index: 3;
    }

    .subscribed-btn {
        color: #fff;
        background: #FF856A;
        top: .9rem;
        right: .287rem;
    }

    .no-count-down {
        width: 2.68867rem;
        height: .49333rem;
        margin-top: .28rem;
        background: url('../assets/img/no_countdown_title.png') no-repeat center/100% 100%;
    }

    .no-count-down-inApp {
        margin-top: .113rem;
    }

    .no-count-down-outApp {
        width: 3.2264rem;
        height: .592rem;
    }

    .count-text-wuxia {
        height: .25rem;
        margin-top: .14rem;
    }

    .count-text-wuxia2 {
        width: .4rem;
        height: .4rem;
        position: absolute;
        top: .166rem;
        left: .46rem;
    }

    .count-content-wuxia {
        display: flex;
        flex-wrap: nowrap;
        flex-direction: row;
        margin-top: .16rem;

        .countDownTimeNew {
            width: .20281rem;
            height: .3713rem;
            border-radius: .0464rem;
            background: linear-gradient(0deg, #FFF1D6 -7.02%, #FFE298 109.65%);
            color: #212121;
            font-family: "baidunumber-Medium";
            font-size: .3733rem;
            font-weight: 500;
            line-height: .3713rem;
            margin-left: .0305rem;
            text-align: center;

            &.is-android {
                line-height: normal;
            }
        }

        .countDownUnitNew {
            color: #343434;
            text-align: center;
            font-family: "PingFang SC";
            font-size: .14rem;
            font-style: normal;
            font-weight: 600;
            line-height: .1933rem; /* 171.429% */
            margin-top: .1rem;
            margin-left: .0533rem;
            margin-right: .0533rem;
        }
    }

    .wuxia-waiting-box {
        position: relative;
        margin: .09rem auto 0;
        display: flex;
        flex-direction: column;
        align-items: center;

        .wuxia-waiting-img {
            width: .533rem;
            height: .701rem;
            opacity: .7;
        }

        .wuxia-waiting-text {
            color: #A2652C;
            font-family: "PingFang SC";
            font-weight: 500;
            font-size: .16667rem;
        }

        .wuxia-waiting-text-inApp {
            margin-top: .16rem;
        }
    }

    .count-content-subscribed-wuxia {
        margin-top: .18rem;
        position: relative;
        left: .282rem;
    }
}
