/**
 * @file  toast 提示组件
 * <AUTHOR>
 */

import React, {useCallback, useEffect} from 'react';
import cls from 'classnames';
import style from './index.module.less';

const cx = cls.bind(style);

const contentMap = {
    'share-success': {
        tip: '分享成功'
    },
    'subscribe-success': {
        tip: '预约成功\n并关注主播~'
    },
    'cancel-subscribe-success': {
        tip: '已取消预约'
    },
    'subscribe-fail': {
        tip: '活动火爆\n请稍后再试'
    },
    'handle-success': {
        tip: '操作成功'
    },
    'lottery-fail': {
        tip: '活动火爆\n稍后再试'
    },
}

const Toast = ({toastType, toastMessage}) => {

    const toastInfo = toastMessage || contentMap[toastType || 'handle-success']?.tip;

    return (
        <div className={cx('wuxia-toast-wrap')}>
            <div className={cx('tip')}>
                {toastInfo}
            </div>
        </div>
    );
};

export default Toast;
