.wuxia-toast-wrap {
    position: absolute;
    top: 5rem;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1.53rem;
    height: 1.41rem;
    flex-shrink: 0;
    border-radius: .18rem;
    background: rgba(26, 26, 26, .8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 3;

    .icon {
        width: .73rem;
        height: .73rem;
    }

    .tip {
        display: flex;
        justify-content: center;
        align-items: center;
        white-space: pre-wrap;
        text-align: center;
        width: 1.57rem;
        height: .65rem;
        font-family: "PingFang SC";
        font-size: .1833rem;
        line-height: .2708rem;
        color: #FFEECB;
        // background: linear-gradient(180deg, #FFFAF0 0%, #FFEECA 100%);
        // background-clip: text;
        // -webkit-background-clip: text;
        // -webkit-text-fill-color: transparent;
    }
}
