/**
 * @file  ShareBtn 分享按钮
 * <AUTHOR>
 */

import React, { useCallback } from 'react';
import cls from 'classnames';
import {numImgMap} from '@/pages/liveshow/config/const'
import {env} from '@/utils/env';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import {openShareBox} from '@/utils/share';
import {wuXiaClickLog} from '@/pages/liveshow/log';
import style from './index.module.less';
const cx = cls.bind(style);

const ShareBtn = ({data, setToastType, inviteTask}) => {
    const {
        share, // 链接分享信息
        new_visual = {}, // 新视觉依赖物料
    } = data;

    const {is_share_token = 0, share_token = ''} = new_visual;
    const invitedNum = +inviteTask?.list?.length || 0;
    const clkShare = useCallback(() => {
        wuXiaClickLog('choujiang_btn', 15263, {
            click_btn: 'invite_friend'
        });
        if (!!is_share_token && share_token) {
            // 口令分享
            const schema = `baiduboxapp://share/doShare?params=${encodeURIComponent(JSON.stringify({'shareData': {
                'mediaType': 'all',
                'title': share.main_title,
                'content': share.sub_title,
                'linkUrl': share.share_url,
                'imageUrl': share.cover,
                'iconUrl': share.cover,
                'type': env.isIOS ? 'text' : 9,
                'textContent': share_token
            }}))}`;

            boxjsUtils.invokeSchema({schema});
        }
        else {
            openShareBox({
                linkUrl: env.isLiteBox ? share.share_url + '&source=lite' : share.share_url,
                title: share.main_title,
                content: share.sub_title,
                source: 'huangshan_live',
                iconUrl: share.cover,
            });
        }
    }, []);

    return (
        <div className={cx('wuxia-share')} onClick={clkShare}>
            <img src='https://mpics.bdstatic.com/qm/202407/pic_bLzQ0B_1720429706.png' className={cx('share-btn-text')}></img>
            <img src={numImgMap[invitedNum > 3 ? 3 : invitedNum]} className={cx(`share-btn-num`)}></img>
        </div>
    );
};

export default ShareBtn;
