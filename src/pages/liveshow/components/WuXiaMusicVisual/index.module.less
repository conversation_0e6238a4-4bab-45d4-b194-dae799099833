.wuxia-music-visual {
    position: relative;
    width: 100%;
    min-height: 100%;
    background: #C0D7B9;

    .wuxia-music-cover1 {
        position: relative;
        width: 100%;
        min-height: 7.05rem;
        background-image: url('./assets/img/background_1.png');
        background-size: 100% 100%;
        z-index: 2;
        pointer-events: none;
    }

    .wuxia-music-cover2 {
        position: relative;
        display: flex;
        flex-direction: column;
        width: 100%;
        height: calc(100% - 7.05rem);
        background-image: url('./assets/img/background_2.png');
        background-size: 100% 100%;
        z-index: 2;
        padding-bottom: .677rem;

        &.out-app {
            background-image: url('./assets/img/background_out_app.jpg');
            padding-bottom: .4rem;
        }
    }

    .header {
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        z-index: 1;
        position: relative;
        pointer-events: auto;

        .logo {
            margin-top: .47rem;
            display: flex;
            justify-content: center;
            width: 100%;

            img {
                height: .1967rem;
            }
        }

        .title {
            width: 2.93333rem;
            height: .94319rem;
            margin: -.1208rem auto 0;
            pointer-events: none;
        }

        .teel-title {
            width: 3.16534rem;
            height: .27175rem;
            margin: 0 auto .46825rem;
            background: url('./assets/img/teel_title.png') no-repeat center/100% 100%;
        }

        .rule-btn {
            position: absolute;
            top: .57333rem;
            left: .13rem;
            width: .32rem;
            height: .32rem;
            border-radius: .32rem;
            background-color: rgba(0, 0, 0, .2);
            color: #FEFEFE;
            font-family: "PingFang SC";
            font-weight: 500;
            font-size: .1rem;
            line-height: .32rem;
            align-content: center;
            text-align: center;
        }
    }

    .wuxia-shadow {
        width: 100%;
        height: .37rem;
        position: absolute;
        top: 1.83rem;
        z-index: 1;
        background: linear-gradient(360deg, rgba(187, 189, 159, 0) 0%, #71724B 106.06%);
    }

    .cover-montain {
        background: url('./assets/img/cover_montain.png') no-repeat;
        background-size: 100% 100%;
        width: 3.82rem;
        height: 1.38rem;
        margin: 2.7067rem auto 0;
        position: relative;
        top: 0;
        z-index: 1;
    }

    .cover-bird {
        background: url('./assets/img/cover_bird.png') no-repeat;
        background-size: 100% 100%;
        width: .65566rem;
        height: .43591rem;
        position: absolute;
        top: 5.47rem;
        left: .4rem;
        z-index: 4;
    }

    .space_opt {
        height: 1.0433rem;
        margin-top: .35rem;
        display: flex;
        justify-content: center;

        img {
            height: 100%;
            border-radius: .25rem;
        }
    }

    .bottom-logo {
        display: flex;
        width: 4.14rem;
        position: relative;
        justify-content: center;
        margin-top: .1333rem;
        padding-bottom: .533rem;
        z-index: 1;

        img {
            width: 100%;
            border-radius: .25rem;
        }

        &.no-show-program {
            margin-top: 0;
            padding-bottom: 0;
        }
    }

    .feedback-wuxia {
        display: flex;
        position: relative;
        justify-content: center;
        z-index: 1;

        img {
            height: .233rem;
        }
    }

    .program {
        width: 4.14rem;
        margin: .18rem auto 0;
        position: relative;
        z-index: 2;
        background: url('./assets/img/program.png') no-repeat center/100% 100%;
        pointer-events: none;

        .program-img {
            width: 100%;
            background-repeat: no-repeat;
            background-size: cover;
        }
    }

    .program-no-countdown {
        background: url('./assets/img/program_no_countdown.png') no-repeat center/100% 100%;
    }
}
