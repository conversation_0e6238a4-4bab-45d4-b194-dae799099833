@import (reference) '../../../assets/style-util.less';

.dialog-wrapper-wu {
    .s-icon {
        display: none;
    }

    .light {
        position: absolute;
        top: .31rem;
        left: 50%;
        transform: translate(-50%);
        width: 4.092rem;
        height: 4.092rem;
        background: url('../assets/img/light.png') no-repeat center/100% 100%;
    }

    .light-money {
        top: .71rem;
        width: 3.592rem;
        height: 3.592rem;
    }

    .music-icon {
        position: absolute;
        top: .59rem;
        left: 73%;
        width: .50843rem;
        height: .68093rem;
        background: url('../assets/img/music_icon.png') no-repeat center/100% 100%;
    }

    .cloud {
        position: absolute;
        top: 1.543rem;
        left: 50%;
        transform: translate(-50%);
        width: 3.62067rem;
        height: 1.68683rem;
        background: url('../assets/img/cloud.png') no-repeat center/100% 100%;
    }

    .dialog-bg {
        position: absolute;
        top: .89rem;
        left: 50%;
        transform: translate(-50%);
        width: 3rem;
        height: 3.36rem;
        background: url('../assets/img/subscribe_bg.png') no-repeat center/100% 100%;
    }

    .money-bg {
        background: url('../assets/img/dialog_money_bg.png') no-repeat center/100% 100%;
    }

    .prize-title {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translate(-50%);
        width: 2.7rem;
        height: .27rem;
        background: url('../assets/img/dialog_prize_title.png') no-repeat center/100% 100%;
    }

    .real-sign-title {
        background: url('../assets/img/dialog_real_title.png') no-repeat center/100% 100%;
    }

    .real-ticket-title {
        width: 2.4633rem;
        height: .27rem;
        background: url('../assets/img/dialog_ticket_title.png') no-repeat center/100% 100%;
    }

    .money-title {
        position: absolute;
        top: .3rem;
        left: 50%;
        transform: translate(-50%);
        width: 2.4633rem;
        height: .27rem;
        background: url('../assets/img/dialog_money_title.png') no-repeat center/100% 100%;
    }

    .sub-title {
        position: relative;
        margin: .0567rem auto 0;
        white-space: pre;
        background: rgba(142, 112, 78, 1);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        font-family: "PingFang SC";
        font-size: .12333rem;
        line-height: .15667rem;
        font-weight: 400;
        z-index: 1;
    }

    .prize-sub-title {
        margin: .11rem auto 0;
        background-color: rgba(0, 0, 0, .5);
        font-size: .15rem;
        line-height: .21rem;
    }

    .s-dialog {
        padding-top: .89rem;
        width: 4.45rem;
        height: 4.40667rem;
        background: transparent;
        font-size: 0 !important;
    }

    .dialog-title {
        position: relative;
        padding-top: .3rem;
        white-space: pre;
        background: linear-gradient(180deg, #E59012 -26.12%, #BF5406 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        font-family: "PingFang SC";
        font-weight: 600;
        font-size: .2rem;
        line-height: .2267rem;
        z-index: 1;
    }

    .gift-img {
        position: relative;
        margin: .03rem auto 0;
        width: 1.24rem;
        height: 1.24rem;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        z-index: 1;
    }

    .prize-img {
        margin: -.41rem auto 0;
        width: 1.52rem;
        height: 1.60667rem;
    }

    .gift-name {
        position: relative;
        margin: .07rem auto 0;
        color: rgba(255, 78, 79, 1);
        font-family: "PingFang SC";
        font-weight: 500;
        font-size: .2rem;
        line-height: .2267rem;
        z-index: 1;
    }

    .real-name {
        margin: .4133rem auto 0;
        font-weight: 600;
        font-size: .27333rem;
        line-height: .31rem;
    }

    .prize-name {
        margin: .1533rem auto 0;
    }

    .price_str {
        position: relative;
        margin: .0533rem auto 0;
        color: rgba(255, 78, 79, 1);
        font-family: "PingFang SC";
        font-weight: 400;
        font-size: .2rem;
        line-height: .2rem;
        z-index: 1;
    }

    .s-dialog-mask {
        max-width: unset;
    }

    .amount {
        position: relative;
        width: 3rem;
        margin: .5833rem auto 0;
        z-index: 1;
        display: flex;
        flex-direction: row;
        align-items: end;
        justify-content: center;

        .amount-num {
            font-family: "Baidu Number";
            font-size: .6667rem;
            font-weight: 500;
            line-height: .6rem;
            text-align: center;
            background: linear-gradient(180deg, #FE2808 80%, #FF5B14 17.22%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .amount-unit {
            font-family: "PingFang TC";
            font-size: .1489rem;
            font-weight: 500;
            line-height: .1489rem;
            text-align: center;
            background: linear-gradient(180deg, #FE2808 80%, #FF5B14 17.22%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }

    .dialog-footer {
        padding: 0 81pr 84pr 81pr;
    }

    .dialog-btn {
        display: flex;
        margin: .13rem auto 0;
        justify-content: center;
        align-items: center;
        position: relative;
        width: 2.49667rem;
        height: .77rem;
        font-family: "PingFang SC";
        background: url('../assets/img/btn_bg.png') no-repeat center;
        background-size: 100% 100%;

        .dialog-btn-txt {
            font-size: .22rem;
            font-style: normal;
            font-weight: 600;
            letter-spacing: .013rem;
            background: linear-gradient(180deg, #FFFFF8 0%, #FFF4B0 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }

    .dialog-btn-prize {
        margin: .8067rem auto 0;
        justify-content: center;
        align-items: center;
        position: relative;
        width: 2.14667rem;
        height: .67667rem;
        font-family: "PingFang SC";
        background: url('../assets/img/dialog_btn_bg.png') no-repeat center;
        background-size: 100% 100%;

        .dialog-btn-txt {
            background: rgba(255, 242, 219, 1);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }
}
