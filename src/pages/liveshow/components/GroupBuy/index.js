/**
 * @file 付费拼团
 * <AUTHOR>
 */

import React, {useState, useEffect, Fragment} from 'react';
import _ from 'lodash';
import {PageStore} from '@/pages/liveshow/service/register';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import {backflow} from '@/utils/backflow';
import {buildParam} from '@/utils';
import GroupInvite from './GroupInvite';
import style from './index.less';

const minuteSecond = 60;
const hourSecond = minuteSecond * 60;
const daySecond = hourSecond * 24;

const GroupBuy = (props) => {
    const {group_buy_info, subscription, pay_info} = props;
    // 拼团信息
    const groupBuyInfo = group_buy_info;
    // 拼团状态 0拼团未开始、1拼团已开始未购买、2拼团已开始已购买、3拼团结束
    const groupStatus = (group_buy_info && group_buy_info.status) || 0;
    // 付费信息
    const payInfo = pay_info;

    const [marIndex, setMarIndex] = useState(0);
    const [resultArray, setResultArray] = useState([
        {
            unit: '天',
            count: '0',
        },
        {
            unit: '时',
            count: '0',
        },
        {
            unit: '分',
            count: '0',
        },
        {
            unit: '秒',
            count: '0',
        },
    ]);

    const [countTime, setCountTime] = useState(() => {
        if (+groupStatus === 0) {
            return +groupBuyInfo.group_start_count_down || 0;
        } else if (+groupStatus === 1) {
            return +groupBuyInfo.group_end_count_down || 0;
        }

        return (+props.countdown < 0 ? 0 : +props.countdown) || 0;
    });

    // 跑马灯
    let marList = [];
    let marLens = 0;

    if (+groupStatus === 2 && +groupBuyInfo.is_partner === 1) {
        marList = _.clone(groupBuyInfo.invite_list || []);
    } else if (+groupStatus !== 0) {
        marList = _.clone(groupBuyInfo.join_list || []);
    }
    marLens = marList.length;

    const getCountDownTimeStr = (timestamp = 0) => {
        timestamp = countTime;
        if (countTime === null) {
            return '---';
        }
        if (countTime < 0) {
            timestamp = 0;
        }
        const timeArray = [
            {
                unit: '天',
                secondCount: daySecond,
            },
            {
                unit: '时',
                secondCount: hourSecond,
            },
            {
                unit: '分',
                secondCount: minuteSecond,
            },
            {
                unit: '秒',
                secondCount: 1,
            },
        ];
        let time = Math.ceil(countTime);
        let resultArray = timeArray.map((timeItem) => {
            const count = Math.floor(time / timeItem.secondCount);
            time -= count * timeItem.secondCount;
            return {
                count: count.toString().length < 2 ? `0${count}` : count,
                ...timeItem,
            };
        });
        setResultArray(resultArray);
    };

    useEffect(() => {
        // 跑马灯
        let marTimer = setInterval(() => {
            setMarIndex((v) => {
                if (v < marLens - 1) {
                    return v + 1;
                }
                return 0;
            });
        }, 1500);

        // 拼团 / 直播倒计时
        let interval;
        if (countTime !== 0) {
            interval = setInterval(() => {
                setCountTime((time) => {
                    if (time >= 1) {
                        return time - 1;
                    }

                    clearInterval(interval);
                    location.reload();

                    return 0;
                });
            }, 1000);
        }

        return () => {
            marTimer && clearInterval(marTimer);
            interval && clearInterval(interval);
        };
    }, []);

    useEffect(() => {
        getCountDownTimeStr();
    }, [countTime]);

    const reflowBaidu = () => {
        let params = PageStore.queryParams;
        let baseUrl = location.origin;
        let url = baseUrl + `/m/media/multipage/liveshow/index.html?${buildParam(params)}#/`;
        if ((+groupStatus === 0 && +subscription.group_sub_status !== 1) || +groupStatus !== 0) {
            backflow({url});
        }
    };

    let renderMarquee = () => {
        return (
            <div className={style.maquee}>
                {marList.length !== 0 &&
                    marList.map((item, index) => {
                        return (
                            <div
                                key={index}
                                className='marqItem'
                                style={{
                                    transform: `translate3D(0, ${marIndex === index ? '0' : '100'}%, 0)`,
                                    transition: `${marIndex === index ? 'transform .8s ease' : 'none'}`,
                                }}
                            >
                                <img src={item.portrait} className='marPort' />
                                <div className='marMsg'>{item.message}</div>
                            </div>
                        );
                    })}
            </div>
        );
    };

    const renderCountDownTitle = () => {
        return (
            <Fragment>
                {/* 拼团未开始 */}
                {+groupStatus === 0 && <div>🎈距离拼团开始</div>}
                {/* 拼团已开始未购买 */}
                {+groupStatus === 1 && <div>🎈距离拼团结束</div>}
                {/* 已参加拼团 */}
                {+groupStatus === 2 && (
                    <div>
                        <span className='coloured'></span>参团成功!请您耐心等待
                    </div>
                )}
                {/* 拼团结束 */}
                {+groupStatus === 3 && (
                    <div>
                        <span className='coloured'></span>
                        拼团活动已结束，距离开播还有
                    </div>
                )}
            </Fragment>
        );
    };

    const renderBtns = () => {
        const iosMoney = +payInfo.is_discount === 1 ? payInfo.discount_money_ios_str : payInfo.pay_money_ios_str;
        const androidMoney =
            +payInfo.is_discount === 1 ? payInfo.discount_money_android_str : payInfo.pay_money_android_str;
        const money = boxjsUtils.platformInfo.isIOS ? iosMoney : androidMoney;
        const grpMoney = boxjsUtils.platformInfo.isIOS
            ? groupBuyInfo.group_buy_price_ios_str
            : groupBuyInfo.group_buy_price_android_str;
        return (
            <div className={style.btns}>
                {+groupStatus === 0 && (
                    <Fragment>
                        <div className={style.noStart}>
                            <div className='discount'>
                                {grpMoney}
                                <div className={style.noStartTip}>
                                    <span>原价</span>
                                    <span className={style.origin}>{money}</span>
                                    <div className='triangle'></div>
                                </div>
                            </div>
                            <div className={style.noStartBtn}>&nbsp;拼团未开始</div>
                            <div className='mantle'></div>
                        </div>
                        <div
                            className='subscribe'
                            onClick={() => {
                                reflowBaidu();
                            }}
                        >
                            {+subscription.group_sub_status === 1 ? '已订阅' : '订阅提醒'}
                            {+subscription.group_sub_status === 1 ? <div className='mantle'></div> : null}
                        </div>
                    </Fragment>
                )}
                {+groupStatus === 1 && (
                    <div
                        className={style.singleBtn}
                        onClick={() => {
                            reflowBaidu();
                        }}
                    >
                        <div className='discount'>
                            {grpMoney}
                            <div className={style.noStartTip}>
                                <span>原价</span>
                                <span className={style.origin}>{money}</span>
                                <div className='triangle'></div>
                            </div>
                        </div>
                        <div>&nbsp;立即拼团</div>
                    </div>
                )}
                {+groupStatus === 2 && (
                    <div
                        className={style.singleBtn}
                        onClick={() => {
                            reflowBaidu();
                        }}
                    >
                        {+groupBuyInfo.is_partner === 0 ? '分享好友' : '邀请好友拿现金!'}
                    </div>
                )}
                {+groupStatus === 3 && (
                    <div
                        className={style.singleBtn}
                        onClick={() => {
                            reflowBaidu();
                        }}
                    >
                        原价购买({money})
                    </div>
                )}
            </div>
        );
    };

    const jumpRule = () => {
        location.href = groupBuyInfo.rule_url;
    };

    return (
        <div className='groupBuy'>
            <div className={style.icon}></div>
            {/* 跑马灯 */}
            {+groupStatus !== 0 && renderMarquee()}
            {/* 倒计时区块 */}
            <div className={'countDown'}>
                <div className={style.buble}></div>
                {renderCountDownTitle()}
                <div className={`times ${+groupStatus === 0 || groupStatus === 1 ? 'downMargin' : ''}`}>
                    {resultArray.map((item, index) => {
                        return [
                            <span className={style.time} key={`${index}-1`}>
                                {item.count}
                            </span>,
                            <span className={`unit ${item.unit === '秒' ? 'noRightPad' : ''}`} key={`${index}-2`}>
                                {item.unit}
                            </span>,
                        ];
                    })}
                </div>
                {(+groupStatus === 0 || groupStatus === 1) && (
                    <div className={style.powerTip}>
                        <span>{groupBuyInfo.group_bottom}</span>
                        <span
                            className={style.tipIcon}
                            onClick={() => {
                                jumpRule();
                            }}
                        ></span>
                    </div>
                )}
            </div>
            <GroupInvite groupStatus={groupStatus} groupBuyInfo={groupBuyInfo} />
            {renderBtns()}
            {+groupStatus === 3 ? <div className='originTip'>原价购买订单不计入活动返现订单</div> : null}
        </div>
    );
};

export default GroupBuy;
