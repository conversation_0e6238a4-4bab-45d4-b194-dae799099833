.searchQuestion {
    padding-bottom: 60pr;
    font-family: PingFangSC-Medium;
    font-size: 45pr;
    color: #525252;
    margin-top: 114pr;

    .questionTitle {
        font-size: 48pr;
        font-weight: 700;
        padding-bottom: 12pr;
    }

    .questionList {
        .questionItem {
            width: 100%;
            padding: 57pr 51pr;
            box-sizing: border-box;
            background: #f6f8f9;
            border-radius: 24pr;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 36pr;

            .left {
                display: flex;
                margin-right: 17pr;
                justify-content: center;
                align-items: baseline;
            }

            .number {
                font-family: DINAlternate-Bold;
                font-size: 54pr;
                color: #1f1f1f;
                margin-right: 17pr;
            }

            .content {
                max-width: 720pr;
                font-size: 48pr;
                line-height: 66pr;
                color: #1f1f1f;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
            }

            .reflowBtn {
                width: 219pr;
                height: 85pr;
                border: 1pr solid rgba(78, 110, 242, .5);
                border-radius: 42pr;
                font-size: 39pr;
                color: #4e6ef2;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
    }
}
