/**
 * @file 付费拼团
 * <AUTHOR>
 */

import React from 'react';
import './index.less';
import { openReflowByUrl } from '@/pages/liveshow/utils/reflow';
import boxjsUtils from '../../../utils/boxjsUtils';

const QuestionList = ({ topic_select }) => {
    const reflow = () => {
        if (!boxjsUtils.platformInfo.isLiteBox) {
            openReflowByUrl();
        }
    };

    return (
        <div className='searchQuestion'>
            <div className='questionTitle'>简介</div>
            <div className='questionList'>
                {topic_select.map((item, index) => {
                    return (
                        <div className='questionItem' key={index}>
                            <div className='left'>
                                <div className='number'>{'0' + (index + 1)}</div>
                                <div className='content'>{item.ask_content}</div>
                            </div>
                            <div className='reflowBtn' onClick={reflow}>
                                想知道
                            </div>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default QuestionList;
