@import '~@baidu/nano-theme/index.less';

.theme-root() !important;

.groupBuy {
    // width: 3.8rem;
    width: 100%;
    margin-top: .35rem;
    padding: .4rem .15rem .15rem;
    background-image: linear-gradient(180deg, #f35 13%, #f05 81%);
    border-radius: .12rem;
    position: relative;
    text-align: center;

    .icon {
        position: absolute;
        top: -.07rem;
        left: 0;
        width: 1.15rem;
        height: .41rem;
        background: url('./assets/grpIcon.png') no-repeat;
        background-size: 100% 100%;
    }

    .maquee {
        width: 2.1rem;
        height: .4rem;
        position: absolute;
        right: .15rem;
        top: 0;
        font-family: PingFangSC-Regular;
        font-size: .14rem;
        color: #fff;
        overflow: hidden;

        .marqItem {
            width: 100%;
            height: .4rem;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            position: absolute;
        }

        .marPort {
            width: .14rem;
            height: .14rem;
            border-radius: 50%;
            margin-right: .04rem;
            border: 1px solid #fff;
        }

        .marMsg {
            font-family: PingFangSC-Regular;
            font-size: 14px;
            color: #fff;
        }
    }

    .mini-ui-broad-cast-wrapper .mini-ui-list-item {
        justify-content: flex-end;
    }

    .coloured {
        display: inline-block;
        width: .17rem;
        height: .17rem;
        background: url('./assets/coloured.png') no-repeat;
        background-size: 100% 100%;
        vertical-align: text-top;
    }

    .downMargin {
        margin-bottom: .25rem;
    }

    .countDown {
        // width: 3.5rem;
        padding: .17rem 0 .15rem;
        // transform: scaleY(-1);
        background-image: linear-gradient(0deg, #fff 75%, #ffd9df 100%);
        border: 1px solid #fff;
        border-radius: .12rem;
        margin: 0 auto;
        position: relative;
        font-family: PingFangSC-Medium;
        font-size: .14rem;
        color: #1f1f1f;

        .times {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: .17rem;

            .time {
                width: .44rem;
                height: .44rem;
                background: #fff4bf;
                border-radius: .06rem;
                font-family: DINAlternate-Bold;
                font-size: .3rem;
                color: #f35;
                line-height: .44rem;
            }

            .unit {
                padding: 0 .08rem;
                font-family: PingFangSC-Regular;
            }

            .noRightPad {
                padding: 0 0 0 .08rem;
            }
        }

        .buble {
            position: absolute;
            top: -.1rem;
            right: -.14rem;
            width: .51rem;
            height: .53rem;
            background: url('./assets/grpBulb.png') no-repeat;
            background-size: 100% 100%;
        }

        .powerTip {
            width: 2.16rem;
            height: .28rem;
            background: url('./assets/bgandriod.png') no-repeat;
            background-size: 100% 100%;
            font-family: PingFangSC-Regular;
            font-size: .12rem;
            color: #f35;
            margin-top: .16rem;
            display: flex;
            justify-content: center;
            align-items: center;
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
        }

        .tipIcon {
            display: inline-block;
            width: .14rem;
            height: .14rem;
            background: url('./assets/grpTip.png') no-repeat;
            background-size: 100% 100%;
            vertical-align: middle;
            margin-left: .03rem;
        }
    }

    .btns {
        // width: 3.5rem;
        width: 100%;
        // height: 0.48rem;
        // margin: 0 auto;

        font-family: PingFangSC-Semibold;
        font-size: .18rem;
        color: #f35;
        display: flex;
        justify-content: space-between;

        .singleBtn {
            width: 100%;
            height: .48rem;
            margin-top: .15rem;
            background-image: linear-gradient(180deg, #fff 0%, #fe9 100%);
            border-radius: .24rem;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .discount {
            position: relative;
        }

        .noStartBtn {
            font-size: .13rem;
        }

        .noStart {
            width: 1.8rem;
            height: .48rem;
            padding: 0 .06rem;
            box-sizing: content-box;
            background-image: linear-gradient(180deg, #fff 0%, #fe9 100%);
            border-radius: .24rem;
            color: #f35;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: .15rem;
            opacity: .9;
        }

        .mantle {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, .6);
            border-radius: .24rem;
        }

        .noStartTip {
            position: absolute;
            z-index: 1;
            min-width: 1rem;
            padding: 0 .02rem;
            box-sizing: content-box;
            height: .18rem;
            border-radius: .12rem;
            top: -.25rem;
            left: 50%;
            transform: translateX(-50%);
            font-family: PingFangSC-Medium;
            font-size: .12rem;
            color: #e62e4d;
            text-align: center;
            line-height: .18rem;
            background-image: linear-gradient(179deg, #fff2b3 0%, #ffd878 63%);
        }

        .triangle {
            position: absolute;
            width: .07rem;
            height: .05rem;
            background: url('./assets/grpTriangle.png') no-repeat;
            background-size: 100% 100%;
            bottom: -.05rem;
            left: 50%;
            transform: translateX(-50%);
        }

        .origin {
            text-decoration: line-through;
        }

        .subscribe {
            width: 1.2rem;
            height: .48rem;
            background-image: linear-gradient(180deg, #fff 66%, #ffccd5 100%);
            border-radius: .24rem;
            font-size: .16rem;
            color: #1f1f1f;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: .15rem;
            position: relative;
        }
    }

    .originTip {
        opacity: .8;
        font-family: PingFangSC-Medium;
        font-size: .12rem;
        color: #fff;
        margin-top: .08rem;
        text-align: center;
    }
}
