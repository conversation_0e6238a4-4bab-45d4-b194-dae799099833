/**
 * @file 付费拼团
 * <AUTHOR>
 */

import React, {Fragment, useState, useEffect} from 'react';
import {PageStore} from '@/pages/liveshow/service/register';
import {buildParam} from '@/utils';
import style from './index.less';

const minuteSecond = 60;
const hourSecond = minuteSecond * 60;
const daySecond = hourSecond * 24;

const GroupInvite = ({groupStatus, groupBuyInfo}) => {
    const [groupEndArray, setGroupEndArray] = useState([
        {
            unit: ':',
            count: '0',
        },
        {
            unit: ':',
            count: '0',
        },
        {
            unit: '',
            count: '0',
        },
    ]);
    // 拼团结束倒计时
    const [groupEndTime, setGroupEndTime] = useState(groupBuyInfo.group_end_count_down || 0);

    const getEndCountTimeStr = (timestamp = 0) => {
        timestamp = groupEndTime;
        if (timestamp === null) {
            return '---';
        }
        if (timestamp < 0) {
            timestamp = 0;
        }
        const timeArray = [
            {
                unit: ':',
                secondCount: hourSecond,
            },
            {
                unit: ':',
                secondCount: minuteSecond,
            },
            {
                unit: '',
                secondCount: 1,
            },
        ];
        let time = Math.ceil(groupEndTime);
        let resultArray = timeArray.map((timeItem) => {
            const count = Math.floor(time / timeItem.secondCount);
            time -= count * timeItem.secondCount;
            return {
                count: count.toString().length < 2 ? `0${count}` : count,
                ...timeItem,
            };
        });
        setGroupEndArray(resultArray);
    };

    useEffect(() => {
        // 拼团结束倒计时
        let endInterval;
        if (groupEndTime !== 0) {
            endInterval = setInterval(() => {
                setGroupEndTime((time) => {
                    if (time >= 1) {
                        return time - 1;
                    }

                    clearInterval(endInterval);
                    location.reload();

                    return 0;
                });
            }, 1000);
        }
        return () => {
            endInterval && clearInterval(endInterval);
        };
    }, []);

    useEffect(() => {
        getEndCountTimeStr();
    }, [groupEndTime]);

    const showList = () => {
        // 将所有query代入
        let params = PageStore.queryParams;
        let baseUrl = location.origin;
        location.href = baseUrl + `/m/media/sdk/grouplist/index.html?${buildParam(params)}#/`;
    };

    return (
        <Fragment>
            {+groupStatus === 2 && +groupBuyInfo.is_partner === 1 ? (
                <div className={style.invitInfo}>
                    <div className={style.invtTitle}>✌🏻成功邀请好友拼团得现金</div>
                    <div className={style.rewardShow}>
                        <span>最多 </span>
                        <span className={style.invtReward}>{groupBuyInfo.obtain_high || 0}</span>
                        <span> 元/人</span>
                    </div>
                    <div className={style.invtCount}>
                        剩余时间：
                        {groupEndArray.map((item, index) => {
                            return [
                                <span className={style.time} key={`${index}-1`}>
                                    {item.count}
                                </span>,
                                <span className={style.unit} key={`${index}-2`}>
                                    {item.unit}
                                </span>,
                            ];
                        })}
                    </div>
                    <div className={style.invtTotal}>
                        <div
                            className={style.jumpToLst}
                            onClick={() => {
                                showList();
                            }}
                        >
                            主播完成直播后可获得
                            <span className='jumpIcon'></span>
                        </div>
                        <div className={style.invtDetail}>
                            <div className={style.reward}>
                                <div>
                                    <span className={style.invtValue}>
                                        {groupBuyInfo.my_invite ? groupBuyInfo.my_invite.obtain_money : 0}
                                    </span>
                                    /元
                                </div>
                                <div className={style.invtTip}>预计可获得金额</div>
                            </div>
                            <div className={style.invtSpin}></div>
                            <div className={style.invtNumbers}>
                                <div>
                                    <span className={style.invtValue}>
                                        {groupBuyInfo.my_invite ? groupBuyInfo.my_invite.invite_num : 0}
                                    </span>
                                    /人
                                </div>
                                <div className={style.invtTip}>成功邀请人数</div>
                            </div>
                        </div>
                    </div>
                    <div className={style.grpGolden}></div>
                </div>
            ) : null}
        </Fragment>
    );
};

export default GroupInvite;
