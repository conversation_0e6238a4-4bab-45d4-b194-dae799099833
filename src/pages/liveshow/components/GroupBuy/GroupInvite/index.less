.invitInfo {
    // width: 3.5rem;
    height: 2.32rem;
    padding-top: .17rem;
    box-sizing: border-box;
    background-image: linear-gradient(0deg, #fff 75%, #ffd9df 100%);
    border: 1px solid #fff;
    border-radius: .12rem;
    margin: .15rem auto 0;
    font-family: PingFangSC-Regular;
    font-size: .14rem;
    color: #1f1f1f;
    text-align: center;
    position: relative;

    .invtTitle {
        font-family: PingFangSC-Medium;
        margin-bottom: .2rem;
    }

    .rewardShow {
        height: .4rem;

        .invtReward {
            font-family: DINAlternate-Bold;
            font-size: .4rem;
            color: #f35;
            vertical-align: bottom;
            line-height: .28rem;
        }
    }

    .invtCount {
        color: #f35;
        margin: .06rem 0 .15rem;
    }

    .invtTotal {
        width: 100%;
        height: 1.03rem;
        background-image: linear-gradient(0deg, #fff 41%, #fff5f7 100%);
        padding: .15rem 0;
        box-sizing: border-box;
        border-radius: 0 0 .12rem .12rem;

        .jumpToLst {
            color: #b8b8b8;
            margin-bottom: .22rem;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .jumpIcon {
            width: .1rem;
            height: .1rem;
            background: url('../assets/grpJump.png') no-repeat;
            background-size: 100% 100%;
            margin-top: .01rem;
        }
    }

    .invtDetail {
        display: flex;
        justify-content: space-around;
        align-items: flex-end;
        padding: 0 .15rem;
    }

    .invtTip {
        font-size: .12rem;
        color: #b8b8b8;
        margin-top: .09rem;
    }

    .invtValue {
        height: .24rem;
        font-family: DINAlternate-Bold;
        font-size: .24rem;
        color: #f35;
        vertical-align: bottom;
        line-height: .75;
    }

    .invtSpin {
        width: .02rem;
        height: .4rem;
        background-image:
            linear-gradient(
                166deg,
                rgba(255, 255, 255, .1) 8%,
                rgba(255, 51, 85, .1) 55%,
                rgba(255, 255, 255, .1) 100%
            );
        border-radius: .01rem;
    }

    .grpGolden {
        position: absolute;
        top: 40%;
        left: 0;
        transform: translateX(-40%);
        width: .52rem;
        height: .52rem;
        background: url('../assets/grpGolden.png') no-repeat;
        background-size: 100% 100%;
    }
}
