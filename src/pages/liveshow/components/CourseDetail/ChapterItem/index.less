@multiple: .905797;

.chapter-item {
    position: relative;
    width: 100%;
    padding: @multiple*.15rem 0;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;

    &::after {
        display: block;
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1px;
        background-color: #E0E0E0;
        transform: scaleY(.5);
    }

    .item-order,
    .simple-order {
        width: @multiple*.25rem;
        min-height: @multiple*.22rem;
        max-height: @multiple*.44rem;
        font-family: DINAlternate-Bold;
        font-size: @multiple*.16rem;
        color: #000;
        letter-spacing: 0;
        line-height: @multiple*.22rem;
        text-align: center;
        flex: none;
        white-space: nowrap;
        overflow: hidden;
    }

    .small {
        font-size: @multiple*.11rem;
    }

    .mid {
        font-size: @multiple*.14rem;
    }

    .item-content {
        margin-left: @multiple*.08rem;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;

        .item-top {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: flex-start;

            .free {
                margin-right: @multiple*.09rem;
                width: @multiple*.36rem;
                height: @multiple*.17rem;
                background: url('../assets/free.png') no-repeat center;
                background-size: 100% 100%;
            }

            .not-free {
                margin-right: @multiple*.09rem;
                width: @multiple*.56rem;
                height: @multiple*.17rem;
                background: url('../assets/not-free.png') no-repeat center;
                background-size: 100% 100%;
            }

            .item-title {
                display: inline-block;
                max-width: @multiple*2.25rem;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                font-family: PingFangSC-Medium;
                font-size: @multiple*.14rem;
                color: #1F1F1F;
                letter-spacing: 0;
                line-height: @multiple*.22rem;

                &.long {
                    max-width: @multiple*2.86rem;
                }
            }

            .item-new {
                margin-left: @multiple*.07rem;
                width: @multiple*.2rem;
                height: @multiple*.16rem;
                background: url('../assets/new.png') no-repeat center;
                background-size: 100% 100%;
            }
        }

        .item-bottom {
            position: relative;
            margin-top: @multiple*.07rem;
            display: flex;
            align-items: center;
            justify-content: flex-start;

            .item-status {
                height: @multiple*.14rem;
                font-family: PingFangSC-Regular;
                font-size: @multiple*.11rem;
                color: #858585;
                letter-spacing: 0;
                line-height: @multiple*.11rem;
                padding: @multiple*.015rem 0;
                display: flex;
                align-items: center;

                &.will {
                    padding-left: @multiple*.17rem;
                    background: url('../assets/will.png') no-repeat left center;
                    background-size: auto 100%;
                }

                &.ing {
                    color: #F35;
                    padding-left: @multiple*.17rem;
                    background: url('../assets/ing.png') no-repeat left center;
                    background-size: auto 100%;
                }

                &.bchapter-ing {
                    color: #F35;
                }
            }

            .item-time {
                margin-left: @multiple*.09rem;
                opacity: .85;
                font-family: PingFangSC-Regular;
                font-size: @multiple*.11rem;
                color: #858585;
                letter-spacing: 0;
                line-height: @multiple*.11rem;
            }
        }
    }

    .simple-status {
        margin-top: @multiple*.03rem;
        width: @multiple*.48rem;
        height: @multiple*.16rem;
        background-repeat: no-repeat;
        background-size: auto @multiple*.16rem;
        background-position: left center;

        &.status-1 {
            background-image: url('../assets/live-will.png');
        }

        &.status0 {
            background-image: url('../assets/live-ing.png');
        }

        &.status1 {
            background-image: url('../assets/live-end.png');
        }
    }

    .simple-order {
        margin-left: @multiple*.06rem;
    }

    .simple-title {
        margin-left: @multiple*.07rem;
        width: @multiple*3.03rem;
        height: @multiple*.44rem;
        font-family: PingFangSC-Medium;
        font-size: @multiple*.14rem;
        color: #1F1F1F;
        letter-spacing: 0;
        line-height: @multiple*.22rem;
        display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
    }

    &.cur {
        .simple-order,
        .simple-title {
            color: #F35;
            height: auto;
            display: block;
        }
    }
}

.no-border {
    &::after {
        background: none !important;
    }
}
