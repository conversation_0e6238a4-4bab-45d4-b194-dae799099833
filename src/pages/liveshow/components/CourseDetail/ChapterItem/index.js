import React, {useEffect, useState, useRef} from 'react';
import {openReflowByUrl} from '@/pages/liveshow/utils/reflow';
import boxjsUtils from '../../../utils/boxjsUtils';
import './index.less';

const ChapterItem = (props) => {
    const {data, hasPay, parRef, curRoomId} = props;
    const itemName = `chapter-item-${data.room_id}`;
    const itemRef = useRef(itemName);
    const showDone = useRef(false);
    const [showNewTag, setShowNewTag] = useState(false);

    useEffect(() => {
        if (!localStorage.getItem(itemName)) {
            setShowNewTag(true);
        }
        showLog();
        window.addEventListener('scroll', showLog, true);
        return () => {
            window.removeEventListener('scroll', showLog, true);
        };
    }, []);

    // 判断是否曝光
    const showLog = () => {
        const {is_latest} = data;
        try {
            const innerHeight = window.innerHeight;
            const itemTop = parRef.current.offsetTop + itemRef.current.offsetTop;
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            if (!showDone.current && itemTop <= innerHeight + scrollTop - 200) {
                showDone.current = true;
                // 如果room_id首次出现最新标签，本地缓存，下次不再展示
                if (+is_latest === 1 && !localStorage.getItem(itemName)) {
                    localStorage.setItem(itemName, true);
                }
            }
        } catch (e) {
            console.log(e);
        }
    };

    const FreeIcon = () => {
        if (hasPay) {
            return '';
        }
        const {is_free} = data;
        return is_free ? <div className='free'></div> : <div className='not-free'></div>;
    };

    const ChapterStatus = () => {
        const {live_status} = data;
        return +live_status === -1 ? (
            <div className='item-status will'>待直播</div>
        ) : +live_status === 0 ? (
            <div className='item-status ing'>直播中</div>
        ) : (
            <div className='item-status end'>看回放</div>
        );
    };

    const dealClick = () => {
        if (!boxjsUtils.platformInfo.isBox) {
            openReflowByUrl();
            return;
        }
        boxjsUtils.invokeSchema({
            schema: data.live_scheme,
        });
    };

    const dealNumClass = (number) => {
        return +number <= 99 ? '' : +number > 999 ? 'small' : 'mid';
    };

    // 格式化章节序号
    const formatOrderNum = (order) => {
        if (+order < 10) {
            return '0' + order;
        }
        return order;
    };

    return (
        <div className={`chapter-item ${curRoomId === data.room_id ? 'cur' : ''}`} ref={itemRef} onClick={dealClick}>
            <div className={`item-order ${dealNumClass(data.index)}`}>{formatOrderNum(data.index)}</div>
            <div className='item-content'>
                <div className='item-top'>
                    <FreeIcon />
                    <div className={`item-title ${hasPay ? 'long' : ''}`}>{data.title}</div>
                    {showNewTag && +data.is_latest === 1 && <div className='item-new'></div>}
                </div>
                <div className='item-bottom'>
                    <ChapterStatus />
                    {data.live_status !== 0 && <div className='item-time'>{data.live_time}</div>}
                </div>
            </div>
        </div>
    );
};

export default React.memo(ChapterItem);
