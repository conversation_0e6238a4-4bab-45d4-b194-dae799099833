@multiple: .905797;

.fold {
    position: relative;
    width: 100%;

    &-top {
        position: relative;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .fold-title {
            font-family: PingFangSC-Medium;
            font-size: @multiple*.14rem;
            color: #1F1F1F;
            letter-spacing: 0;
            line-height: @multiple*.22rem;
        }

        .fold-icon {
            width: @multiple*.2rem;
            height: @multiple*.12rem;
            background-position: right center;
            background-repeat: no-repeat;
            background-size: @multiple*.1rem @multiple*.06rem;
        }
    }

    &-words {
        position: relative;
        width: 100%;
        // height: @multiple*.21rem;
        margin-top: @multiple*.02rem;
        font-family: PingFangSC-Regular;
        font-size: @multiple*.14rem;
        color: #929292;
        letter-spacing: 0;
        line-height: @multiple*.24rem;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;

        &.more {
            height: auto;
        }
    }
}
