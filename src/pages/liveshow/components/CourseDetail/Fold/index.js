/**
 * <AUTHOR> l<PERSON><PERSON>
 * @File         : 折叠组件
 * @Interface    : 接口文档说明
 * @Product      : 需求文档说明
 * @Date         : 2021-10-26 20:19:45
 * @LastEditors Please set LastEditors
 * @LastEditTime 2021-12-16 12:20:00
 */
import React, {useState, useEffect} from 'react';
import down from '../assets/arrow-down.png';
import up from '../assets/arrow-up.png';

import './index.less';

const Fold = (props) => {
    const {classN, title, words, lines, fontSize = '.14rem', lineHeight = '.21rem'} = props;
    const [showMore, setShowMore] = useState(false);
    const [lackWords, setLackWords] = useState(false);
    // 默认1行
    const lineClamp = lines || 1;

    useEffect(() => {
        if (words) {
            try {
                // 行宽
                const lineWidth = document.getElementById(classN).clientWidth;
                // clone一个隐藏起来计算宽度
                const clonedNode = document.getElementById(classN).cloneNode(true);
                // 给clone的dom增加样式
                clonedNode.style.overflow = 'visible';
                clonedNode.style.display = 'inline-block';
                clonedNode.style.width = 'auto';
                clonedNode.style.whiteSpace = 'nowrap';
                clonedNode.style.visibility = 'hidden';
                const containerID = `${classN}_colne_node_id`;
                clonedNode.setAttribute('id', containerID);
                let tmpNode = document.getElementById(containerID);
                let newNode = clonedNode;
                if (tmpNode) {
                    document.body.replaceChild(clonedNode, tmpNode);
                } else {
                    newNode = document.body.appendChild(clonedNode);
                }
                // 比较宽度是否超过限制行数
                if (newNode.clientWidth <= lineWidth * lines) {
                    setLackWords(true);
                }
                document.body.removeChild(newNode);
            } catch (e) {
                console.log(e);
            }
        }
    }, [words]);

    return (
        <div className={`fold ${classN}`}>
            <div className='fold-top'>
                <span className='fold-title'>{title}</span>
                <span
                    className='fold-icon'
                    style={{
                        backgroundImage: `url(${showMore ? down : up})`,
                        display: lackWords ? 'none' : 'block',
                    }}
                    onClick={() => {
                        setShowMore(!showMore);
                    }}
                ></span>
            </div>
            <div
                className={`fold-words ${showMore ? 'more' : ''}`}
                id={classN}
                style={{
                    WebkitLineClamp: lackWords || showMore ? '' : lineClamp,
                    height: `${lackWords || showMore ? 'auto' : `${lineClamp * 0.21}rem`}`,
                    lineHeight: lineHeight,
                    fontSize: fontSize,
                }}
            >
                {words}
            </div>
        </div>
    );
};

export default Fold;
