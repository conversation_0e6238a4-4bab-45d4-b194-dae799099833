import React, { useState, useRef } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import { openReflowByUrl } from '@/pages/liveshow/utils/reflow';
import ChapterItem from '../ChapterItem';
import Empty from '../Empty';
import boxjsUtils from '../../../utils/boxjsUtils';

// 章节列表
const ChapterList = ({ payInfo, ChapterListRef, listData, getData, EndMessage, Loading }) => {
    const { has_pay } = payInfo;
    return (
        <div className='detail-item chapter-list' ref={ChapterListRef} id='chapter-list'>
            {
                <InfiniteScroll
                    dataLength={listData.list.length + listData.pn}
                    initialLoad={false}
                    pageStart={1}
                    next={getData}
                    hasMore={listData.hasMore}
                    scrollThreshold='50px'
                    endMessage={<EndMessage />}
                    loader={<Loading key={0} />}
                    scrollableTarget='root'
                >
                    {listData.list.length > 0 ? (
                        listData.list.map((item, index) => (
                            <ChapterItem
                                data={item}
                                hasPay={has_pay}
                                parRef={ChapterListRef}
                                key={`chapter-item-${index}`}
                            />
                        ))
                    ) : (
                        <div className='empty-list'>
                            <Empty text='暂时没有课程' />
                        </div>
                    )}
                </InfiniteScroll>
            }
        </div>
    );
};

const ChapterDetail = (props) => {
    const { payInfo, courseData, listData, getData } = props;
    const [showList, setShowList] = useState(true);
    const ChapterListRef = useRef('chapter-list-content');
    // 上拉加载时文案
    const Loading = () => <div className='list-loading'>加载中...</div>;

    // 到底文案
    const EndMessage = () => (listData.list.length > 0 ? <div className='list-loading'>已经到底啦~</div> : '');

    const toggleShowList = () => {
        setShowList(!showList);
    };

    // 购买须知
    const BuyNotes = () => {
        const notes = [
            {
                text: '本直播系列课著作权归属作者。',
            },
            {
                text: '直播系列课为虚拟消费，支付完成后不支持下载、转让和退款，购买前需谨慎选择。',
            },
            {
                text: '购买成功后，可从作者主页-【直播】-【直播系列课】中查看已购买直播系列课。',
            },
            {
                text: '直播系列课内容均由作者自主设置，包括直播系列课价格、直播时长、内容质量、更新频次等。您可以咨询作者获得详细解答。',
            },
            {
                text: '本直播系列课内容不构成任何投资建议，仅代表作者个人观点，不代表百度APP的立场。投资有风险，入市请谨慎。',
            },
            {
                text: '更多说明详见',
                aText: '《付费直播系列课购买说明》',
                href: 'https://activity.baidu.com/mbox/4a80aa9865/rule',
            },
        ];
        return (
            <div className='detail-item buy-notes'>
                {notes.map((item, index) => (
                    <div className='notes-item' key={`notes-item-${index}`}>
                        <span className='notes-order'>{index + 1}.</span>
                        <span className='notes-words'>
                            {item.text}
                            {item.aText && item.href && (
                                <a
                                    href='javascript:;'
                                    onClick={() => {
                                        jumpTo(item.href);
                                    }}
                                >
                                    {item.aText}
                                </a>
                            )}
                        </span>
                    </div>
                ))}
            </div>
        );
    };

    const jumpTo = (url) => {
        if (!boxjsUtils.platformInfo.isBox) {
            openReflowByUrl();
            return;
        }
        let opts = {
            extRequest: JSON.stringify({ type: 'home' }),
            source: 'index',
            enter_outlive_commonwebview: '1',
            enter_outlive_weburl: url,
        };
        let schema = `baiduboxapp://live/enterStreamRoom?params=${encodeURIComponent(JSON.stringify(opts))}`;
        boxjsUtils.invokeSchema({
            schema,
        });
    };
    return (
        <div className='chapter-detail'>
            <div className='chapter-detail-title'>
                <span className={`title-tab ${showList ? 'tab-cur' : ''}`} onClick={toggleShowList}>
                    课程章节(
                    {+courseData.status === 2
                        ? courseData.pub_chapter + '/' + courseData.total_chapter
                        : courseData.pub_chapter}
                    )
                </span>
                <span className={`title-tab tab-notes ${!showList ? 'tab-cur' : ''}`} onClick={toggleShowList}>
                    购买须知
                </span>
                {showList && courseData.update_rhythm && (
                    <span className='rhythm'>预计每{courseData.update_rhythm}天更新一章</span>
                )}
            </div>
            {showList ? (
                <ChapterList
                    payInfo={payInfo}
                    ChapterListRef={ChapterListRef}
                    listData={listData}
                    getData={getData}
                    EndMessage={EndMessage}
                    Loading={Loading}
                />
            ) : (
                <BuyNotes />
            )}
        </div>
    );
};

export default React.memo(ChapterDetail);
