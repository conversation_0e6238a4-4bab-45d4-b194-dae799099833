@multiple: .905797;

.course-detail {
    position: relative;
    width: 100%;
    margin: 0 auto;
    margin-top: @multiple*.2rem;
    padding-bottom: @multiple*.65rem;
    overflow: auto;
    background: #FFF;

    .info-title {
        position: relative;
        margin-bottom: @multiple*.14rem;
        font-family: PingFangSC-Medium;
        font-size: @multiple*.16rem;
        color: #1F1F1F;
        text-align: justify;
        line-height: @multiple*.224rem;
    }

    .detail-info {
        position: relative;
        width: 100%;
        background: #FFF;
        border-radius: @multiple*.12rem;
        margin-bottom: @multiple*.12rem;
        padding: @multiple*.17rem @multiple*.15rem;

        .course-info-box {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: flex-start;

            .portrait {
                position: relative;
                width: @multiple*1.18rem;
                height: @multiple*.66rem;
                display: flex;
                align-items: center;
                border-radius: @multiple*.09rem;
                overflow: hidden;

                img {
                    position: relative;
                    width: 100%;
                    height: 100%;
                }
            }

            .right {
                width: @multiple*2.24rem;
                margin-left: @multiple*.08rem;
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                justify-content: flex-start;

                .course-title {
                    max-height: @multiple*.42rem;
                    font-family: PingFangSC-Regular;
                    font-size: @multiple*.16rem;
                    line-height: @multiple*.21rem;
                    color: #000;
                    display: -webkit-box;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;

                    .icon {
                        display: inline-block;
                        width: @multiple*.4rem;
                        height: @multiple*.18rem;
                        margin: 0 @multiple*.09rem -@multiple*.03rem 0;
                        border-radius: .04rem;
                        background: url('./assets/course.png') no-repeat center;
                        background-size: 100% 100%;
                    }
                }

                .author-info {
                    width: 100%;
                    margin-top: @multiple*.07rem;
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    font-family: PingFangSC-Regular;
                    font-size: @multiple*.12rem;
                    color: #858585;
                    text-align: justify;
                    line-height: @multiple*.16rem;

                    .author-name {
                        max-width: @multiple*.99rem;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    .line {
                        margin-left: @multiple*.11rem;
                        height: @multiple*.1rem;
                        width: @multiple*.006rem;
                        background: #E0E0E0;
                    }

                    .chapter-nums {
                        margin-left: @multiple*.13rem;
                        width: @multiple*1.2rem;
                    }
                }

                .course-words {
                    margin-top: @multiple*.08rem;
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    font-family: PingFangSC-Regular;
                    font-size: @multiple*.12rem;
                    color: #858585;
                    letter-spacing: 0;
                    line-height: @multiple*.12rem;

                    span {
                        &:first-child {
                            margin-right: @multiple*.12rem;
                        }
                    }
                }

                .course-info {
                    width: 100%;
                    position: relative;
                    margin-top: @multiple*.08rem;
                    display: flex;
                    align-items: flex-end;
                    justify-content: space-between;

                    .price {
                        position: relative;
                        display: flex;
                        align-items: flex-end;
                        justify-content: flex-start;

                        .now-price {
                            display: flex;
                            align-items: flex-end;
                            justify-content: flex-start;
                            font-family: PingFangSC-Semibold;
                            font-size: @multiple*.18rem;
                            color: #F35;
                            text-align: justify;
                            line-height: @multiple*.18rem;

                            i {
                                font-style: normal;
                            }
                        }

                        .old-price {
                            margin: 0 0 0 @multiple*.06rem;
                            font-family: PingFangSC-Regular;
                            font-size: @multiple*.12rem;
                            color: #858585;
                            letter-spacing: 0;
                            line-height: @multiple*.14rem;
                            text-decoration-line: line-through;
                        }
                    }

                    .learn-status {
                        font-family: PingFangSC-Medium;
                        font-size: @multiple*.18rem;
                        line-height: @multiple*.18rem;
                        color: #F35;
                        text-align: justify;
                    }

                    .learn-nums {
                        font-family: PingFangSC-Regular;
                        font-size: @multiple*.14rem;
                        color: #858585;
                        letter-spacing: 0;
                        line-height: @multiple*.14rem;
                    }
                }
            }
        }

        .author-desc {
            margin-top: @multiple*.17rem;
        }

        .course-desc {
            margin-top: @multiple*.14rem;
        }
    }

    .bottom-btn {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        height: @multiple*.68rem;
        background: #FFF;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 100;

        .subscribe-btn {
            width: @multiple*1.34rem;
            height: @multiple*.48rem;
            border: @multiple*.01rem solid rgba(78, 110, 242, .3);
            border-radius: @multiple*.24rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: PingFangSC-Medium;
            font-size: @multiple*.16rem;
            color: #4E6EF2;
            letter-spacing: 0;
            text-align: center;
            line-height: @multiple*.18rem;

            &.done {
                color: #858585;
                border: @multiple*.01rem solid rgba(0, 0, 52, .3);
            }
        }

        .buy-btn,
        .login-btn {
            margin-left: @multiple*.16rem;
            width: @multiple*2.3rem;
            height: @multiple*.48rem;
            background: #4E6EF2;
            border-radius: @multiple*.24rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .now-price,
            .login {
                font-family: PingFangSC-Medium;
                font-size: @multiple*.16rem;
                color: #FFF;
                letter-spacing: 0;
                line-height: @multiple*.16rem;
            }

            .old-price {
                margin-bottom: -@multiple*.015rem;
                font-family: PingFangSC-Regular;
                font-size: @multiple*.13rem;
                color: #FFF;
                letter-spacing: 0;
                line-height: @multiple*.16rem;
                text-decoration-line: line-through;
            }
        }
    }

    .chapter-detail {
        &-title {
            position: relative;
            width: 100%;
            height: @multiple*.4rem;
            margin: @multiple*.14rem 0;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            font-family: PingFangSC-Regular;
            font-size: @multiple*.16rem;
            text-align: justify;
            line-height: @multiple*.22rem;

            .title-tab {
                position: relative;
                color: #858585;
            }

            .tab-notes {
                margin-left: @multiple*.28rem;
            }

            .tab-cur {
                font-family: PingFangSC-Medium;
                color: #1F1F1F;

                &::after {
                    position: absolute;
                    content: '';
                    bottom: -@multiple*.1rem;
                    left: 50%;
                    margin-left: -@multiple*.09rem;
                    width: @multiple*.18rem;
                    height: @multiple*.04rem;
                    background: #F35;
                    border-radius: @multiple*.02rem;
                }
            }

            .rhythm {
                position: absolute;
                right: 0;
                font-size: @multiple*.13rem;
                color: #929292;
                letter-spacing: 0;
                line-height: @multiple*.13rem;
            }
        }

        .detail-item {
            position: relative;
            width: 100%;
            background: #FFF;
            border-radius: @multiple*.12rem;
            margin-bottom: @multiple*.12rem;
        }

        .chapter-list {
            padding: @multiple*.02rem @multiple*.15rem 0 @multiple*.15rem;
            overflow: auto;

            .list-loading,
            .list-end {
                position: relative;
                width: 100%;
                margin: @multiple*.2rem 0;
                font-family: PingFangSC-Regular;
                font-size: @multiple*.13rem;
                color: #929292;
                letter-spacing: 0;
                line-height: @multiple*.2rem;
                text-align: center;
            }

            .empty-list {
                position: relative;
                width: 100%;
                height: @multiple* 3rem;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
    }

    .buy-notes {
        padding: @multiple*.2rem @multiple*.15rem @multiple*.17rem @multiple*.15rem;

        .notes-item {
            margin-bottom: @multiple*.07rem;
            display: flex;
            align-items: flex-start;
            justify-content: flex-start;
            font-family: PingFangSC-Regular;
            font-size: @multiple*.13rem;
            color: #1F1F1F;
            letter-spacing: 0;
            text-align: justify;
            line-height: @multiple*.185rem;

            .notes-order {
                margin-right: @multiple*.03rem;
            }

            .notes-words {
                a {
                    color: #F35;
                }
            }
        }
    }
}
