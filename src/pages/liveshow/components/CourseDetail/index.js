/**
 * <AUTHOR> lubiao
 * @File         : 用途
 * @Interface    : 接口文档说明
 * @Product      : 需求文档说明
 * @Date         : 2021-11-10 17:09:55
 * @LastEditors Please set LastEditors
 * @LastEditTime 2021-12-16 12:19:56
 */
import React, { useEffect, useState, useRef, Fragment } from 'react';
import Fold from './Fold';
import { getCourseDetail } from '../../service/api';
import boxjsUtils from '../../utils/boxjsUtils';
import { openReflowByUrl } from '@/pages/liveshow/utils/reflow';
import { env } from '@/utils/env';
import { subscribe } from '@/pages/liveshow/service/api';
import ChapterDetail from './ChapterDetail';
import './index.less';

const CourseDetail = (props) => {
    const { isLogin, series_nid, appointment_status, refresh } = props;
    const [isReady, setIsReady] = useState(false);
    const [courseData, setCourseData] = useState();
    const [authorData, setAuthorData] = useState();
    const [payInfo, setPayInfo] = useState();
    const [listData, setListData] = useState({
        pn: 1,
        rn: 10,
        hasMore: true,
        list: [],
    });

    const unit = env.isAndroid ? '元' : 'Y币';

    useEffect(() => {
        getData();
        document.getElementById('root').style.overflow = 'auto';
        return () => {
            document.getElementById('root').style.overflow = 'inherit';
        };
    }, []);

    const getData = async () => {
        try {
            const res = await getCourseDetail({
                series_nid: series_nid,
                has_sublist: 1,
                page: listData.pn,
                page_size: listData.rn,
            });
            if (+res.errno === 0) {
                if (listData.pn === 1) {
                    setCourseData(res.data.series_info || {});
                    setAuthorData(res.data.anchor_info || {});
                    setPayInfo(res.data.pay_info || {});
                    setIsReady(true);
                }
                setListData((obj) => ({
                    ...obj,
                    pn: obj.pn + 1,
                    hasMore: !!res.data.has_more,
                    list: [...obj.list, ...res.data.list],
                }));
            }
        } catch (e) {
            console.log(e);
        }
    };

    // 处理数值，强制保留2位小数
    const dealNum = (number) => {
        return (+number / 100).toFixed(2);
    };

    // 课程+作者信息
    const CourseDetails = () => {
        const { has_pay } = payInfo;
        const { nick_name, avatar } = authorData;
        const { lecturer_desc, title, is_discount, price, discount_price, sales_num, description, total_chapter } =
            courseData;
        return (
            <div className='detail-info' onClick={() => detailClick(event)}>
                <div className='course-info-box'>
                    <div className='portrait'>
                        <img src={avatar} />
                    </div>
                    <div className='right'>
                        <div className='course-title'>
                            <span className='icon'></span>
                            {title}
                        </div>
                        {/* <div className='author-info'>
                            <span className='author-name'>主讲人：{nick_name}</span>
                            <span className='line'></span>
                            <span className='chapter-nums'>共{total_chapter}章节</span>
                        </div>
                        <div className='course-words'>
                            <span>准时开播</span>
                            <span>无限次回看</span>
                        </div> */}

                        <div className='course-info'>
                            {+has_pay !== 1 ? (
                                <div className='price'>
                                    {is_discount ? (
                                        <Fragment>
                                            <span className='now-price'>
                                                <i>{dealNum(discount_price)}</i>
                                                {unit}
                                            </span>
                                            <span className='old-price'>
                                                {dealNum(price)}
                                                {unit}
                                            </span>
                                        </Fragment>
                                    ) : (
                                        <span className='now-price'>
                                            <i>{dealNum(price)}</i>
                                            {unit}
                                        </span>
                                    )}
                                </div>
                            ) : (
                                <div className='learn-status'>学习中</div>
                            )}
                            {sales_num > 0 && <div className='learn-nums'>{sales_num}人正在学</div>}
                        </div>
                    </div>
                </div>
                <Fold classN='author-desc' title='讲师介绍' words={lecturer_desc} lines={1} />

                <Fold classN='course-desc' title='课程简介' words={description} lines={2} />
            </div>
        );
    };

    // 悬浮按钮
    const BottomBtn = () => {
        const { has_pay } = payInfo;
        const { is_discount, price, discount_price } = courseData;
        return !has_pay ? (
            <div className='bottom-btn'>
                {appointment_status ? (
                    <div className='subscribe-btn done' onClick={subscriptionClick}>
                        取消预约
                    </div>
                ) : (
                    <div className='subscribe-btn' onClick={subscriptionClick}>
                        预约
                    </div>
                )}
                <div className='buy-btn' onClick={buyClick}>
                    {!isLogin ? (
                        <span className='now-price'>登录百度APP学习</span>
                    ) : is_discount ? (
                        <Fragment>
                            <span className='now-price'>
                                {dealNum(discount_price)}
                                {unit}购买
                            </span>
                            <span className='old-price'>
                                {dealNum(price)}
                                {unit}
                            </span>
                        </Fragment>
                    ) : (
                        <span className='now-price'>
                            {dealNum(price)}
                            {unit}购买
                        </span>
                    )}
                </div>
            </div>
        ) : (
            ''
        );
    };

    const resSubEnd = (success, requestStatus) => {
        const sucToast = requestStatus ? '预约成功' : '取消预约成功';
        const failToast = requestStatus ? '预约失败' : '取消预约失败';
        boxjsUtils.showToast(success ? sucToast : failToast);

        // 通知开启系统通知的弹窗
        if (requestStatus === 1) {
            setTimeout(() => {
                boxjsUtils.systemNotify('live_sub');
            }, 500);
        }

        if (success) {
            refresh && refresh();
        }
    };

    // 订阅按钮点击
    const subscriptionClick = () => {
        if (!boxjsUtils.platformInfo.isBox) {
            openReflowByUrl();
            return;
        }
        if (isLogin) {
            const requestStatus = +appointment_status === 0 ? 1 : 0;
            subscribe(requestStatus)
                .then((success) => resSubEnd(success, requestStatus))
                .catch(() => resSubEnd(false, requestStatus));
        } else {
            boxjsUtils.login().then(() => {
                // 一定延时后调用(解决安卓11.20这个逻辑运行不稳定的问题)
                window.setTimeout(() => {
                    refresh && refresh();
                }, 500);
            });
        }
    };

    // 购买按钮点击端外回流端内跳转详情页
    const buyClick = () => {
        if (!boxjsUtils.platformInfo.isBox) {
            openReflowByUrl();
            return;
        }
        const protocol = window.location.protocol;
        const port = window.location.port;
        /* eslint-disable max-len */
        const url = `//live.baidu.com/m/media/sdk/payliveclass/cclassdetail.html?source=share_from_outside&series_nid=${series_nid}`;
        /* eslint-enable max-len */
        let opts = {
            extRequest: JSON.stringify({ type: 'home' }),
            source: 'index',
            enter_outlive_commonwebview: '1',
            enter_outlive_weburl: url,
        };
        let schema = `baiduboxapp://live/enterStreamRoom?params=${encodeURIComponent(JSON.stringify(opts))}`;
        boxjsUtils.invokeSchema({
            schema,
        });
    };

    // 卡片信息空白处点击
    const detailClick = (e) => {
        if (e.target.className !== 'fold-icon') {
            buyClick();
        }
    };

    return (
        <div className='course-detail'>
            {isReady && (
                <Fragment>
                    <div className='info-title'>章节来自系列课</div>
                    <CourseDetails />
                    <ChapterDetail payInfo={payInfo} courseData={courseData} listData={listData} getData={getData} />
                    <BottomBtn />
                </Fragment>
            )}
        </div>
    );
};

export default React.memo(CourseDetail);
