/**
 * @file loading
 */

import React, {Component} from 'react';
import {Flash} from '@baidu/nano-react';
import { isFromHonorBrowser } from '@/pages/liveshow/service/register';
import Lottie from 'react-lottie';
import animationData from './lottie/loading_day.json';
import './index.less';

const Loading = props => {

    const defaultOptions = {
        loop: true,
        autoplay: true,
        animationData: animationData,
        rendererSettings: {
          preserveAspectRatio: 'xMidYMid slice',
        },
      };
    if(isFromHonorBrowser) {
        return(
            <div className='s-flash s-flash-fullpage'>
                <Lottie
                    options={defaultOptions}
                    height={72}
                    width={72}
                    style={{overflow:'visible',transform: 'translate3d(0px, -20px, 0px)'}}
                >
                </Lottie>
                <div className='loadingWords'>正在加载</div>

            </div>

        );
    }


    return (
        <Flash layout="full" />
    );

}

export default Loading;
