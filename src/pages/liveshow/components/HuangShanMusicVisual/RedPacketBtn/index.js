/**
 * @file  RedPacketBtn 签到导流按钮
 * <AUTHOR>
 */

import React, {useCallback, useEffect} from 'react';
import cls from 'classnames/bind';
import {huangshanShowLog, huangshanClickLog} from '@/pages/liveshow/log';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import style from './index.module.less';

const cx = cls.bind(style);

const RedPacketBtn = ({newVisual}) => {
    const {red_packet_url = ''} = newVisual;

    useEffect(() => {
        huangshanShowLog('qianghongbao_btn');
    }, []);

    const jumpToRedPacket = useCallback(() => {

        huangshanClickLog('qianghongbao_btn');

        // 跳转到红包页面
        red_packet_url && boxjsUtils.invokeSchema({schema: red_packet_url});
    }, []);

    return (
        <div className={cx('huangshan-red-btn')} onClick={jumpToRedPacket}></div>
    );
};

export default RedPacketBtn;
