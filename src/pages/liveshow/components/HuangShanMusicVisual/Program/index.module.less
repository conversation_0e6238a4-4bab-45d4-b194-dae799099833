.program-hu {
    background: url('https://mpics.bdstatic.com/qm/202403/pic_xw7eVg_1710491426.png') no-repeat;
    background-size: 100% 100%;
    width: 3.74rem;
    height: 5.0133rem;
    margin: auto;
    margin-top: .15rem;

    .program-title-hu {
        height: .18rem;
        display: flex;
        justify-content: center;
        // padding-top: .182rem;

        img {
            margin-top: .182rem;
            height: 100%;
        }
    }

    .program-content-hu {
        margin-top: .316rem;

        .program-img {
            width: 3.58rem;
            height: 4rem;
            overflow-x: scroll;
            margin: auto;
            border-radius: .15rem;
        }

        img {
            width: 100%;
        }

        .am-tabs {
            // background: yellow;
            .am-tabs-tab-bar-wrap {
                .am-tabs-default-bar {
                    background: url('https://mpics.bdstatic.com/qm/202403/pic_50KYdZ_1710745990.png') no-repeat;
                    background-size: cover;
                    width: 3.74rem;
                    padding-bottom: .1766rem;
                }

                .am-tabs-default-bar-content {
                    margin: auto;
                    height: .246rem;
                    width: 1.7066rem;
                    border: #56CFBA solid .86px;
                    border-radius: .392rem;
                    background: linear-gradient(90deg, #E7FFFC 59.24%, #E8FFFD 100%);
                }

                .am-tabs-default-bar-tab {
                    color: #56CFBA;
                    font-family: "PingFang SC";
                    font-size: .14rem;
                    font-style: normal;
                    font-weight: 600;
                    line-height: normal;
                    height: .246rem;
                    opacity: .7;
                }

                .am-tabs-default-bar-tab::after {
                    content: none;
                }

                .am-tabs-default-bar-tab-active {
                    color: #FFF;
                    font-family: "PingFang SC";
                    font-size: .14rem;
                    font-style: normal;
                    font-weight: 600;
                    line-height: normal;
                    opacity: .7;
                    width: .9233rem;
                    height: .23rem;
                    flex-shrink: 0;
                    border-radius: .392rem;
                    background: linear-gradient(90deg, #80E3D1 0%, #52CEB8 100%);
                }

                .am-tabs-default-bar-underline {
                    border: 0;
                }
            }
        }
    }
}
