/**
 * @file 节目单
 * <AUTHOR>
 */

import React from 'react';
import cls from 'classnames/bind';
import {Tabs} from 'antd-mobile';
import style from './index.module.less';

const cx = cls.bind(style);

const Program = ({programList}) => {
    const tabs = programList.map((item) => {
         return  { title: <span>{item.date}</span> }
    })
    return (
        <div className={cx('program-hu')}>
            <div className={cx('program-title-hu')}>
                <img src='https://mpics.bdstatic.com/qm/202403/pic_szupgs_1710491909.png' alt='标题'/>
            </div>
           <div className={cx('program-content-hu')}>
            {
                programList.length > 0 && <Tabs tabs={tabs} initialPage={programList[0].is_focus === 1 ? 0 : 1}>
                  {
                      programList.map((item) =>
                         (
                          <div className={cx('program-img')} key={item.date}>
                              <img src={`${item.img}`} alt='节目单'/>
                          </div>
                          )
                      )
                  }
              </Tabs>
            }
           </div>
        </div>
    );
};

export default Program;
