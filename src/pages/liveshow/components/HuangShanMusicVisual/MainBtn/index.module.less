.huangshan-main-btn-wrap {
    display: flex;
    justify-content: center;
    margin-top: -.3rem;
    padding: 0 .2rem;
    position: relative;
    z-index: 1;

    .main-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        width: 3.09rem;
        height: .65rem;
        font-family: "PingFang SC";
        background: url('https://mpics.bdstatic.com/qm/202403/pic_NHabpP_1710418658.png') no-repeat center;
        background-size: 100% 100%;

        .txt {
            font-size: .25rem;
            font-style: normal;
            font-weight: 600;
            letter-spacing: .013rem;
            background: linear-gradient(180deg, #FFFFF8 0%, #FFF4B0 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .bubble {
            position: absolute;
            top: -.057rem;
            right: -.157rem;
            width: .796rem;
            height: .236rem;
            background: url('https://mpics.bdstatic.com/qm/202403/pic_8vWhbN_1710419746.png') no-repeat center center;
            background-size: 100% 100%;
        }
    }

    .sub-success {
        width: 3.12rem;
        height: .65rem;
        background: url('https://mpics.bdstatic.com/qm/202403/pic_HN6Kvv_1710419946.png') no-repeat center center;
        background-size: 100% 100%;
    }

    .guide-share {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
}
