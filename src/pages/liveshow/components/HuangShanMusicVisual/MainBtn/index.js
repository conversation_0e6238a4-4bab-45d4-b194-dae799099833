/**
 * @file  MainBtn 主按钮
 * <AUTHOR>
 */

import React, {useCallback, useState} from 'react';
import cls from 'classnames/bind';
import {env} from '@/utils/env';
import {huangshanShowLog, huangshanClickLog} from '@/pages/liveshow/log';
import ShareBtn from '../ShareBtn';
import RedPacketBtn from '../RedPacketBtn';
import {batchSubscribeLive} from '@/pages/liveshow/service/api';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import style from './index.module.less';

const cx = cls.bind(style);

const MainBtn = ({data, handleInvoke, entranceAPP, subStatus, setSubStatus, setToastType, refresh}) => {
    const {
        new_visual = {},
        user_info
    } = data;

    // 直播间预约态
    const {sub_status = {}} = new_visual;

    const isLogin = user_info.is_login; // 是否登录

    const roomIds = Object.keys(sub_status);

    // 打点用按钮状态
    let btnLogStatus = '';

    window.console.log(subStatus);

    // 已预约直播间个数
    const hasSubNum = isLogin ? Object.values(sub_status).filter(item => +item === 1).length : 0;

    const batchSubscribe = useCallback(async () => {
        const res = await batchSubscribeLive(roomIds.join(','));
        if (+res.errno === 0) {
            setSubStatus(2);
            setToastType(+hasSubNum === 0 ? 'subscribe_success_all' : 'subscribe_success');
        }
        else {
            setToastType('subscribe-fail');
        }
    }, []);

    const handleClk = useCallback(async () => {
        // 按钮点击
        huangshanClickLog('subscribe_btn', {
            click_btn: btnLogStatus
        });
        if (isLogin) {
            await batchSubscribe();
        } else {
            // 没有登录去登录,登录成功后刷新数据
            boxjsUtils.login().then(() => {
                // 一定延时后调用(解决安卓11.20这个逻辑运行不稳定的问题)
                window.setTimeout(() => {
                    refresh();
                }, 500);
            });
        }
    }, [isLogin, refresh]);

    const invokeApp = useCallback(() => {
        // 按钮点击
        huangshanClickLog('subscribe_btn', {
            click_btn: btnLogStatus
        });

        handleInvoke && handleInvoke();
    }, []);


    // 按钮群
    const btnGroup = useCallback(() => {
        // 两场直播均预约
        if (+subStatus === 2) {
            // 矩阵外
            if (!entranceAPP) {
                huangshanShowLog('subscribe_btn', {
                    click_btn: 'subscribe_success'
                });
                btnLogStatus = 'subscribe_success';
                return (
                    <div className={cx('sub-success')} onClick={invokeApp}></div>
                );
            }

            return (
                <div className={cx('guide-share')}>
                    <RedPacketBtn newVisual={new_visual}/>
                    <ShareBtn data={data} setToastType={setToastType} />
                </div>
            );
        }

        // 其他场景 均未预约 / 已预约一场
        if (!entranceAPP) {

            huangshanShowLog('subscribe_btn', {
                click_btn: 'subscribe'
            });
            btnLogStatus = 'subscribe';

            return (
                <div className={cx('main-btn')} onClick={invokeApp}>
                    <span className={cx('txt')}>一键预约直播</span>
                </div>
            );
        }

        huangshanShowLog('subscribe_btn', {
            click_btn: +subStatus === 0 ? 'subscribe_all' : 'subscribe'
        });
        btnLogStatus = +subStatus === 0 ? 'subscribe_all' : 'subscribe';

        return (
            <div className={cx('main-btn')} onClick={handleClk}>
                <span className={cx('txt')}>{+subStatus === 0 ? '一键预约听两场' : '一键预约直播'}</span>
                {/* 端外不展示 */}
                <span className={cx('bubble')}></span>
            </div>
        );

    }, [subStatus, entranceAPP]);

    return (
        <div className={cx('huangshan-main-btn-wrap')}>
            {btnGroup()}
        </div>
    );
};

export default MainBtn;
