/**
 * @file 新版分享页视觉visual_id 为 3
 * <AUTHOR>
 */

import React, {useEffect, useCallback, useState} from 'react';
import {PageStore} from '@/pages/liveshow/service/register';
import {matrixAppSchemeHeaderList, refolwForConfig} from '@/pages/liveshow/utils/reflow';
import {getAppSchemeHead} from '@/pages/liveshow/utils/getUrlParam';
import {TemplateEnum, logTxtMap} from '@/pages/liveshow/config/const';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import {execInvoke, getInvokeInfo, setCommonParams, createMcpWxTag} from '@baidu/mcp-sdk';
import {env} from '@/utils/env';
import {huangshanShowLog, huangshanClickLog} from '@/pages/liveshow/log';
import Dialog from '@/pages/liveshow/components/common/Dialog';
import {initShare} from '@/utils/share';
import cls from 'classnames/bind';
import LiveVideo from './LiveVideo';
import CountDown from './CountDown';
import Toast from './Toast';
import Program from './Program'
import MainBtn from './MainBtn';
import styles from './index.module.less';


const cx = cls.bind(styles);

// mcp 场景信息
const APP_NAME_SCENE = {
    'baiduboxapp': 'sharepage',
    'lite': 'sharepagelite', // 新增scene
    '': 'sharepage'
};

// 预约页外部资源
const sids = ['tiebak', 'wangpan', 'ditu'];

// 调起位
const posKey = 'pos_part';

const source = PageStore.queryParams.source;
const appSchemeHead = getAppSchemeHead();

// 回流矩阵场景
const isMatrixApp = matrixAppSchemeHeaderList.includes(appSchemeHead);
const entranceAPP = (env.isMainBox && !isMatrixApp) || (env.isLiteBox);

// 仅在live.baidu.com 域名下安卓回手百新增微信开放标签
const isLiveDomain = location.host === 'live.baidu.com';

let invokeInfo = {}; // mcp调起信息

const HuangShanMusicVisual = ({data, refresh}) => {
    const {
        template,
        status,
        schema_info, // 回流NA直播间信息
        countdown,
        tiebaLiveCmd,
        screen,
        nid,
        back_flow = {},
        new_visual = {},
        share,
        user_info
    } = data;

    const {sub_status = {}, space_opt, feedback_url} = new_visual; // 双直播间预约态、资源位、反馈

    const [showToast, setShowToast] = React.useState(false);
    const [toastType, setToastType] = React.useState('');

    const [subStatus, setSubStatus] = useState(0); // hasSubNum 0 均未预约 1 已预约一场 2 均预约

    window.console.log(user_info.is_login);

    useEffect(() => {
        // 已预约直播间个数
        const hasSubNum = user_info.is_login ? Object.values(sub_status).filter(item => +item === 1).length : 0;
        setSubStatus(hasSubNum);
    }, [user_info.is_login, sub_status]);

    useEffect(() => {
        if (toastType) {
            setShowToast(true);
            setTimeout(() => {
                setShowToast(false);
                setToastType('');
            }, 3000);
        }
    }, [toastType]);


    const {enter_cmd = schema_info.enter_cmd} = new_visual;

    // 是否展示回流弹窗
    const [showModel, setShowModel] = useState({
        show: false,
        type: 'reflow',
    });
    // 初始化 视频未播放之前
    const [isInitStatus, setInitStatus] = useState(true);
    const [invokeAppInfo, setInvokeAppInfo] = useState({});
    const isPreview = template === TemplateEnum.Preview; // 是否为预约直播

    useEffect(() => {
        // 初始化分享
        initShare({
            linkUrl: share.share_url,
            title: share.main_title,
            content: share.sub_title,
            source: 'huangshan_live',
            iconUrl: share.cover,
            imageUrl: share.cover,
            mediaType: 'all',
        });
    }, []);

    useEffect(() => {
        huangshanShowLog('subscribe');

        // 导流位打点
        if (space_opt.hide === 0 && entranceAPP) {
            huangshanShowLog('banner');
        }
    }, []);

    const jumpToNA = useCallback(() => {
        // 直播中、回放 符合条件自动跳转直播间
        if ([0, 3].indexOf(status) !== -1 && schema_info) {
            boxjsUtils.invokeSchema({schema: enter_cmd});
            return;
        }
    }, [status, schema_info]);

    const onClickShowModel = res => {
        setShowModel({
            show: res.show,
            type: res.type,
        });
    };

    const handleInvoke = () => {
        invokeApp(posKey);
    }

    useEffect(() => {
        // 非入口矩阵内 （回流主板 或 极速版）执行mcp调起逻辑
        if (!entranceAPP) {
            getMcpData(enter_cmd);
        }
    }, []);

    // 获取MCP信息
    const getMcpData = useCallback(async schema => {
        if (!schema) {
            return;
        }

        // 设置通用调起信息
        setCommonParams({
            app: 'wise',
            scene: APP_NAME_SCENE[source] || 'sharepage',
            ext_sid: sids.indexOf(source) !== -1 ? source : '' // 贴吧、网盘、地图
        });

        // 获取调起位详细信息
        const info = await getInvokeInfo({
            'invoke_info': {
                [posKey]: [{
                    'share_scheme': schema
                }]
            }
        });
        invokeInfo = info;
        setInvokeAppInfo({...info});

        // 存在调起信息 && 安卓 && 微信 && 回流主板 新增微信开放标签校验 isLiveDomain &&
        if (isLiveDomain && !isMatrixApp && env.isAndroid && env.isWechat && info.action_rule) {
            createWxTags(info);
        }
    }, []);

    // 创建微信开放标签
    const createWxTags = invokeInfo => {
        const {action_rule: actionRule, log_id: logId} = invokeInfo;
        const liveShareInfo = actionRule[posKey][0];
        const shareContainer = document.querySelector('.huangshan-main-btn-wrap');
        if (shareContainer && liveShareInfo) {
            createMcpWxTag({targetDom: shareContainer}, posKey, liveShareInfo, logId);
        }
    };

     // mcp 调起
     const invokeApp = useCallback(async (pos) => {
        const invokeConfig = {
            appName: appSchemeHead,
            toStore: true,
            scheme: new_visual.enter_cmd,
            token: new_visual.share_token
        };
        const invokeInfoForPos = invokeInfo && invokeInfo.action_rule && invokeInfo.action_rule[pos];
        if (!invokeInfoForPos || !Array.isArray(invokeInfoForPos) || !invokeInfoForPos[0]) {
            // 执行默认调起 兼容接口返回异常情况
            refolwForConfig(invokeConfig);
            return;
        }

        try {
            const res = await execInvoke(
                pos,
                invokeInfoForPos[0],
                invokeInfoForPos[0].log_id
            );
        }
        catch (e) {
            // 执行默认调起行为
            refolwForConfig(invokeConfig);
        }
    }, [invokeInfo]);

    // 端内吊起反馈轻框
    const feedbackClick = () => {
        boxjsUtils.invokeSchema({schema: feedback_url});
    };

    const jumpGuideLoc = useCallback(() => {
        huangshanClickLog('banner');
        boxjsUtils.invokeSchema({schema: space_opt.url});
    }, []);

    return (
        <div className={`white-screen-patrol ${cx('huang-music-visual',!entranceAPP && 'out-app')}`}>
            <div className={cx('header')}>
                <div className={cx('logo')}>
                    <img  src='https://mpics.bdstatic.com/qm/202403/pic_dWZIVh_1710472809.png' alt='logo'/>
                 </div>
                    <img className={cx('title')} src='https://mpics.bdstatic.com/qm/202403/pic_42y25o_1710406255.png' alt='标题'/>
             </div>
             <img className='video-back' src='https://mpics.bdstatic.com/qm/202403/pic_rVrJtS_1710417048.png' alt='背景'/>
             <div className={cx('video-screen-hu')}></div>
             <LiveVideo
                {...data}
                className='center'
                updatePageData={refresh}
                isBaijiahaoTag={false}
                onClickShowModel={onClickShowModel}
                setInitStatus={setInitStatus}
                isInitStatus={isInitStatus}
                isAutoPlay={false}
                handleInvoke={handleInvoke}
                jumpToNA={jumpToNA}
                isPreview={isPreview}
                showModel={showModel}
             />
              <CountDown
                status={status}
                countdown={countdown}
                refresh={refresh}
                subStatus={subStatus}
                entranceAPP={entranceAPP}
            />
              {
                new_visual && (
                    <MainBtn
                        handleInvoke={handleInvoke}
                        data={data}
                        setToastType={setToastType}
                        entranceAPP={entranceAPP}
                        subStatus={subStatus}
                        setSubStatus={setSubStatus}
                        refresh={refresh}
                        />
                )
              }
             {
                space_opt.hide === 0 && entranceAPP ?
                 <div className={cx('space_opt')}>
                   <img onClick={jumpGuideLoc}  src={space_opt.img} alt='标题'/>
                 </div>
                 : null
             }
            {
                 entranceAPP && <Program programList={data.new_visual.program_list}/>
            }
                <div className={cx('bottom-logo',!entranceAPP && 'no-show-program')}>
                    <img src={data.new_visual.acknowledge} alt='标题'/>
                </div>
            {
                showToast && <Toast toastType={toastType} />

            }
            {
                entranceAPP && (
                <div className='feedback'>
                    <img onClick={feedbackClick} src='https://mpics.bdstatic.com/qm/202403/pic_xksche_1710730962.png' alt='标题'/>
                </div>
                )
            }
             {/* 回流弹窗 */}
            <Dialog
                showModel={showModel}
                tiebaLiveCmd={tiebaLiveCmd}
                onClickShowModel={onClickShowModel}
                status={status}
                nid={nid}
                screen={screen}
                outerSchemeParams={data.schemeParams}
                backFlow={back_flow}
                handleInvoke={handleInvoke}
                invokeInfo={invokeAppInfo}
            />
        </div>
    );
}

export default HuangShanMusicVisual;
