/**
 * @file  ShareBtn 分享按钮
 * <AUTHOR>
 */

import React, { useCallback } from 'react';
import cls from 'classnames';
import {env} from '@/utils/env';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import {openShareBox} from '@/utils/share';
import style from './index.module.less';
const cx = cls.bind(style);

const ShareBtn = ({data, setToastType}) => {
    const {
        share, // 链接分享信息
        new_visual = {} // 新视觉依赖物料
    } = data;

    const {is_share_token = 0, share_token = ''} = new_visual; // 是否微信分享 分享口令

    const clkShare = useCallback(() => {
        // NewH5ClickLog(4,'subscribe_btn','15263','share');
        if (!!is_share_token && share_token) {
            // 口令分享
            const schema = `baiduboxapp://share/doShare?params=${encodeURIComponent(JSON.stringify({'shareData': {
                'mediaType': 'all',
                'title': share.main_title,
                'content': share.sub_title,
                'linkUrl': share.share_url,
                'imageUrl': share.cover,
                'iconUrl': share.cover,
                'type': env.isIOS ? 'text' : 9,
                'textContent': share_token
            }}))}`;

            boxjsUtils.invokeSchema({schema});
        }
        else {
            openShareBox({
                linkUrl: env.isLiteBox ? share.share_url + '&source=lite' : share.share_url,
                title: share.main_title,
                content: share.sub_title,
                source: 'huangshan_live',
                iconUrl: share.cover,
            });
        }
    }, []);

    return (
        <div className={cx('huangshan-share')} onClick={clkShare}></div>
    );
};

export default ShareBtn;
