.huang-music-visual {
    position: relative;
    width: 100%;
    min-height: 100%;
    // min-height: 100vh;
    background: url('https://mpics.bdstatic.com/qm/202403/pic_6MRD29_1710851997.jpg') no-repeat;
    background-size: 100% 100%;

    &.out-app {
        background: url('https://mpics.bdstatic.com/qm/202403/pic_LUXw2g_1710852058.jpg') no-repeat;
        background-size: 100% 100%;
    }

    .header {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;

        .logo {
            margin-top: .573rem;
            display: flex;
            justify-content: center;
            width: 100%;

            img {
                height: .18rem;
            }
        }

        .title {
            height: .44rem;
            margin-top: .04rem;
        }
    }

    .video-back {
        width: 3.5266rem;
        height: .69rem;
        position: absolute;
        top: 1.39rem;
        left: .2rem;
    }

    .video-screen-hu {
        background: url('https://mpics.bdstatic.com/qm/202403/pic_9xsMhn_1710473057.png') no-repeat;
        background-size: 100% 100%;
        height: 2.1233rem;
        width: 3.74rem;
        position: absolute;
        left: 50%;
        top: 1.77rem;
        transform: translateX(-50%);
        border-radius: .25rem;
    }

    .space_opt {
        height: 1.0433rem;
        margin-top: .35rem;
        display: flex;
        justify-content: center;

        img {
            height: 100%;
            border-radius: .25rem;
        }
    }

    .bottom-logo {
        display: flex;
        justify-content: center;
        margin-top: .6866rem;
        padding-bottom: .533rem;

        img {
            height: 1.18rem;
            border-radius: .25rem;
        }

        &.no-show-program {
            margin-top: .68rem;
        }
    }

    .feedback {
        display: flex;
        justify-content: center;
        padding-bottom: .53rem;

        img {
            height: .233rem;
        }
    }
}
