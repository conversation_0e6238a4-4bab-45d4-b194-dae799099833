/**
 * @file  toast 提示组件
 * <AUTHOR>
 */

import React, {useCallback, useEffect} from 'react';
import cls from 'classnames';
import {huangshanShowLog} from '@/pages/liveshow/log';
import style from './index.module.less';

const cx = cls.bind(style);

const contentMap = {
    'share-success': {
        icon: 'https://mpics.bdstatic.com/qm/202403/pic_xHhbkr_1710405324.png',
        tip: '分享成功'
    },
    'subscribe_success_all': {
        icon: 'https://mpics.bdstatic.com/qm/202403/pic_xHhbkr_1710405324.png',
        tip: '已成功预约两场'
    },
    'subscribe_success': {
        icon: 'https://mpics.bdstatic.com/qm/202403/pic_xHhbkr_1710405324.png',
        tip: '已成功预约直播'
    },
    'subscribe-fail': {
        icon: 'https://mpics.bdstatic.com/qm/202403/pic_l1xyYB_1710405299.png',
        tip: '预约失败\n请重新尝试'
    },
    'handle-success': {
        icon: 'https://mpics.bdstatic.com/qm/202403/pic_xHhbkr_1710405324.png',
        tip: '操作成功'
    },
}

const Toast = ({toastType, refresh}) => {

    const toastInfo = contentMap[toastType || 'handle-success'];

    useEffect(() => {
        huangshanShowLog('subscribe_toast', {
            subscribe_tosat_status: toastType
        });
    }, []);

    return (
        <div className={cx('huangshan-toast-wrap')}>
            <img className={cx('icon')} src={toastInfo.icon} />
            <div className={cx('tip')}>
                {toastInfo.tip}
            </div>
        </div>
    );
};

export default Toast;
