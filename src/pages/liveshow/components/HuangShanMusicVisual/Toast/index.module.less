.huangshan-toast-wrap {
    position: absolute;
    top: 4rem;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1.8rem;
    height: 1.8rem;
    flex-shrink: 0;
    border-radius: .18rem;
    background: rgba(26, 26, 26, .8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 3;

    .icon {
        width: .73rem;
        height: .73rem;
    }

    .tip {
        display: flex;
        justify-content: center;
        align-items: center;
        white-space: pre-wrap;
        text-align: center;
        width: 1.57rem;
        height: .55rem;
        font-family: "PingFang SC";
        font-size: .167rem;
        font-style: normal;
        font-weight: 600;
        color: #FFF6BA;
        // background: linear-gradient(180deg, #FFFEF0 14.46%, #FFF6BA 81.93%);
        // background-clip: text;
        // -webkit-background-clip: text;
        // -webkit-text-fill-color: transparent;
    }
}
