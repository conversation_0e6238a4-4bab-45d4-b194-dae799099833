/**
 * @file 播放器组件（新版）
 * <AUTHOR>
 * @date 2024-03-14 14:00:18
 */

import React, {useEffect, useRef, useState} from 'react';
import cls from 'classnames';
import HPlayer from '@baidu/hplayer';
import Hls from 'hls.js';
import Chat, {MessageType} from '@/pages/liveshow/utils/chat';
import {env} from '@/utils/env';
import {isBox, isAndroid} from '@baidu/boxx/env';
import {LiveStatus} from '@/pages/liveshow/config/const';
import {PageStore} from '@/pages/liveshow/service/register';
import styles from './index.module.less';
const cx = cls.bind(styles);

const VideoMask = props => {
    if (props.isBaijiahaoTag && props.isNoVideo) {
        return '';
    }
    const {updatePageData} = props;

    return (
        <div className={cx('video-mask-hu')} onClick={updatePageData}>
            视频加载异常
        </div>
    );
}

let isClickPlay = false; // 是否点击过播放
let innerInitStatus = true; // 视频第一帧播放成功（用于初始化im消息）
let hasShowModal = false; // 是否已展示过调起弹窗
const LiveVideo = props => {
    const {
        status,
        liveHlsUrl,
        updatePageData,
        onClickShowModel,
        screen,
        isBaijiahaoTag,
        setInitStatus = () => { },
        isInitStatus, // 首帧播放成功后值为false
        isAutoPlay = false,
        isMatrixApp,
        watch_video_duration = 5, // 默认播放5s
        isPreview,
        jumpToNA,
        new_visual
    } = props;
    innerInitStatus = isInitStatus;
    const player = useRef();
    const chat = useRef(Chat.getInstance());
    const liveEndedTimerRef = useRef();
    const timeupdateTimerRef = useRef();
    const [showMask, setShowMask] = useState(false);
    const [videoLoadErr, setVideoLoadErr] = useState(false); // 是否加载异常
    const [currentTime, setCurrentTime] = useState(0); // 重新加载时记录播放时间
    const [showLoading, setShowLoading] = useState(false); // 是否显示加载中
    const isLive = status === LiveStatus.LIVEING;
    const isNoVideo = !liveHlsUrl && !new_visual.preview_video;
    const videoType = Hls.isSupported() && !isPreview ? 'hls' : 'normal';
    // eslint-disable-next-line max-len
    const [videoUrl, setVideoUrl] = useState(+status === -1 ? new_visual.preview_video : liveHlsUrl);
    const backImage = new_visual.preview_video_cover || 'https://mpics.bdstatic.com/qm/202403/pic_3QVWBV_1710749755.png';
    useEffect(() => {
        if (isNoVideo) {
            setShowMask(true);
            return;
        }
        setVideoLoadErr(false);
        initMediaPlayer(isAutoPlay);
        +status === -1 ? setVideoUrl(new_visual.preview_video) : setVideoUrl(liveHlsUrl);
        return destroyPlayer;
    }, [status]);

    useEffect(() => {
        if (videoLoadErr === true) {
            destroyPlayer();
        }
    }, [videoLoadErr]);

    const initMediaPlayer = (startPlay = true) => {
        setVideoLoadErr(false);
        player.current = new HPlayer({
            container: document.getElementById('video-box-new'),
            playsinline: true, // 是否内联播放
            theme: '#ff3355', // 主体色
            lang: 'zh-cn', // 本地语言
            loop: !isLive, // 循环
            live: isLive, // 是否直播中
            speed: false, // 倍速播放
            screenshot: false, // 不启用截屏
            hotkey: false, // 不启用热键
            preload: 'metadata', // video preload
            showFullScreen: false,
            disablepictureinpicture: 'true',
            pressSpeed: isBox && isAndroid ? 2 : 3, // android手百仅支持到2倍速
            video: {
                url: videoUrl,
                bgpic: !isBox ? backImage : '', // 手百里容器层有探测图片弹NA View，手百里不添加兜底poster了
                pic: backImage,
                type: videoType, // hls模式应用在尽可能的规避各浏览器强制接管h5 video
                customType: {
                    hls: function (video, player) {
                        if (player.plugins.hls) {
                            player.plugins.hls.destroy();
                        }

                        const hls = new Hls();
                        hls.on(Hls.Events.MEDIA_ATTACHED, console.info.bind(console));
                        hls.on(Hls.Events.MANIFEST_PARSED, console.info.bind(console));
                        hls.on(Hls.Events.FRAG_PARSING_USERDATA, console.info.bind(console));
                        hls.on(Hls.Events.FRAG_PARSING_METADATA, console.info.bind(console));
                        hls.on(Hls.Events.ERROR, console.info.bind(console));

                        hls.attachMedia(video);

                        hls.on(Hls.Events.MEDIA_ATTACHED, (event, data) => {
                            hls.loadSource(videoUrl);

                            hls.on(Hls.Events.ERROR, (event, data) => {
                                if (data.fatal) {
                                    switch (data.type) {
                                        case Hls.ErrorTypes.NETWORK_ERROR:
                                            if (data.url === videoUrl) {
                                                hls.detachMedia();
                                                onError();
                                            }
                                            break;
                                        case Hls.ErrorTypes.MEDIA_ERROR:
                                            hls.swapAudioCodec();
                                            hls.recoverMediaError();
                                            break;
                                        default:
                                            break;
                                    }
                                }
                            });
                        });

                        player.plugins.hls = hls;
                        player.events.on('destroy', () => {
                            hls.destroy();
                            delete player.plugins.hls;
                        });
                    },
                },
            },
        });

        videoSectionEvent();

        player.current.on('play', () => {
            isClickPlay = true;
            setShowLoading(true);
            // 直播中 回放 在矩阵内 直接调起NA
            if (!isPreview && (env.isMainBox || (env.isLiteBox))) {
                player.current && player.current.pause && player.current.pause();
                jumpToNA();
                return;
            }
        });

        player.current.on('playing', () => {
            window.console.log('playing');

            clearTimeout(liveEndedTimerRef.current);
            clearTimeout(timeupdateTimerRef.current);
            setInitStatus(false);
            setShowLoading(false);

            try {
                // 直播中 或 回放 播放5s 后暂停播放并展示调起弹窗, 播放时长为0时 不限制 (目前仅回流主板 和 Lite版支持)
                if ((!env.isMainBox && !env.isLiteBox) && (!isMatrixApp || PageStore.queryParams.source === 'lite')
                    && +watch_video_duration !== 0 && !hasShowModal) {
                    setTimeout(() => {
                        player.current && player.current.pause && player.current.pause();
                        onClickShowModel({
                            show: true,
                            type: 'reflow',
                            from: 'video',
                        });
                        hasShowModal = true;
                    }, watch_video_duration * 1000);
                }
            }
            catch (e) {
                window.console.log(e);
            }
        });

        player.current.on('waiting', () => {
            setShowLoading(true);
        });

        player.current.on('pause', () => {
            clearTimeout(liveEndedTimerRef.current);
            clearTimeout(timeupdateTimerRef.current);
            setShowLoading(false);
        });

        player.current.on('ended', () => {
            clearTimeout(liveEndedTimerRef.current);
            clearTimeout(timeupdateTimerRef.current);
            !isLive && setCurrentTime(0);
        });

        player.current.on('error', onError);

        player.current.on('timeupdate', () => {
            setShowLoading(false);
            clearTimeout(liveEndedTimerRef.current);
            clearTimeout(timeupdateTimerRef.current);

            if (!innerInitStatus) {
                // 直播中状态 && 播放过程中8s加载不出资源，请求接口获取直播结束状态
                if (isLive) {
                    liveEndedTimerRef.current = setTimeout(() => {
                        updatePageData && updatePageData();
                    }, 8000);
               }

               // 15s加载不出资源，展示网络异常
               timeupdateTimerRef.current = setTimeout(() => {
                    setVideoLoadErr(true);
               }, 15000);
            }
        });

        player.current.on('loadstart', () => {
            if (player.current) {
                startPlay && player.current.play();
                if (!isLive && !isClickPlay) {
                    // 回放视频，重新加载时设置上一次播放位置
                    player.current.video.currentTime = currentTime;
                }
            }
        });

        if (isLive) {
            // 直播中 监听im消息切流状态 然后重新设置播放链接
            chat.current.on(MessageType.LIVE_CHANGE_FLOW, live_hls_url => {
                if (live_hls_url && player.current) {
                    player.current.switchVideo({ url: live_hls_url });
                }
            });
        }
    }

    // 视频切片设置
    const videoSectionEvent = () => {
        // 是否需要视频切片，start有可能是零
        const { start: videoStartTime, end: videoEndTime } = PageStore.queryParams;
        const isNeedSetVideoStarTime = !isLive && +videoStartTime >= 0 && +videoEndTime >= 0;

        if (isNeedSetVideoStarTime) {
            // 切片是否播放完成
            let isEndedBySection = false;

            player.current.on('loadeddata', () => {
                // 初始化 设置视频切片开始时间
                if (+videoStartTime > 0) {
                    player.current.video.currentTime = +videoStartTime;
                }
            });
            player.current.on('timeupdate', () => {
                // 监听视频切片结束时间 自动暂停
                if (!isEndedBySection && player.current.video.currentTime >= +videoEndTime) {
                    player.current.pause();
                    isEndedBySection = true;
                }
            });
        }
    }

    const onError = () => {
        if (isClickPlay) {
            setVideoLoadErr(true);
            setShowLoading(false);
        }
    }

    // 销毁播放器及im监听
    const destroyPlayer = () => {
        clearTimeout(liveEndedTimerRef.current);
        clearTimeout(timeupdateTimerRef.current);

        isClickPlay = false;
        setInitStatus(true);
        setShowLoading(false);

        if (player.current) {
            !isLive && setCurrentTime(player.current.video.currentTime);
            player.current.destroy();
            player.current = null;
        }

        if (chat.current) {
            chat.current.off(MessageType.LIVE_CHANGE_FLOW);
        }
    }

    const getRootClass = cls({
        'live-video-hu': true,
        'is-living': isLive,
    });

    return (
        <React.Fragment>
            <div className={cx(getRootClass)}>
                {/* 遮罩层 */}
                {(showMask || videoLoadErr) && (
                    <VideoMask
                        {...props}
                        isBaijiahaoTag={isBaijiahaoTag}
                        onClickShowModel={onClickShowModel}
                        updatePageData={updatePageData}
                        isNoVideo={isNoVideo}
                    />
                )}

                {/* 播放器 */}
                <div
                    id='video-box-new'
                    style={{ backgroundImage: isNoVideo ? `url(${backImage})` : 'none' }}
                ></div>
            </div>
        </React.Fragment>
    );
};

export default LiveVideo;
