/**
 * @file 倒计时组件
 * <AUTHOR> on 2024-02-01 20:59:32.
 */

import React, {useCallback, useEffect, useRef, useState} from 'react';
import {getEndStatus} from '@/pages/liveshow/service/api';
import {env} from '@/utils/env';
import styles from './index.module.less';
import cls from 'classnames';
const cx = cls.bind(styles);

const minuteSecond = 60;
const hourSecond = minuteSecond * 60;
const daySecond = hourSecond * 24;

function CountDownTimeStr({timestamp = 0}) {
    if (timestamp === null) {
        return '---';
    }
    if (timestamp < 0) {
        timestamp = 0;
    }
    const timeArray = [
        {
            unit: '天',
            secondCount: daySecond
        },
        {
            unit: '时',
            secondCount: hourSecond
        },
        {
            unit: '分',
            secondCount: minuteSecond
        },
        {
            unit: '秒',
            secondCount: 1
        }
    ];
    let time = Math.ceil(timestamp);
    let resultArray = timeArray.map((timeItem) => {
        const count = Math.floor(time / timeItem.secondCount);
        time -= count * timeItem.secondCount;
        return {
            count: count.toString().length < 2 ? `0${count}` : count,
            ...timeItem
        };
    });
    return resultArray.reduce((pre, result, index) => {
        if (pre === '') {
            if (result.count > 0 || result.unit === '秒') {
                return [
                    <span className={cx(styles.countDownTimeNew, env.isAndroid && 'is-android')} key={`${index}-1`}>
                        {result.count.toString()[0]}
                    </span>,
                    <span className={cx(styles.countDownTimeNew, env.isAndroid && 'is-android')} key={`${index}-3`}>
                        {result.count.toString()[1]}
                    </span>,
                    <span className={cx(styles.countDownUnitNew)} key={`${index}-2`}>
                        {result.unit}
                    </span>
                ];
            }
            return pre;
        }
        return pre.concat([
            [
                <span className={cx(styles.countDownTimeNew, env.isAndroid && 'is-android')} key={`${index}-1`}>
                    {result.count.toString()[0]}
                </span>,
                <span className={cx(styles.countDownTimeNew, env.isAndroid && 'is-android')} key={`${index}-3`}>
                    {result.count.toString()[1]}
                </span>,
                <span className={cx(styles.countDownUnitNew)} key={`${index}-2`}>
                    {result.unit}
                </span>
            ]
        ]);
    }, '');
}

function CountDown(props) {
    const {entranceAPP} = props;

    const [countdown, setCountdown] = useState(() => {
        return +props.countdown < 0 ? 0 : +props.countdown;
    });
    const timeOut = useRef(null);
    const update = useCallback(() => {
        return getEndStatus().then((data) => {
            // -1 表示直播还未开始,继续倒计时
            const status = +data.status;
            const count = +data.countdown < 0 ? 0 : +data.countdown;
            const show = (status === -1 && count > 0);
            props.setShowLiveBtn && props.setShowLiveBtn(!show);
            if (status === -1) {
                setCountdown(count);
            }
            else {
                // 主播开始,刷新数据前往直播间
                props.refresh && props.refresh();
            }
        });
    }, [props]);

    useEffect(() => {
        update();
    }, []);

    useEffect(() => {
        if (countdown <= 0) {
            // 倒计时结束逻辑
            update();
        }
        else {
            timeOut.current = setTimeout(() => {
                setCountdown(countdown - 1);
            }, 1000);
        }
        return () => {
            clearTimeout(timeOut.current);
        };
    }, [countdown]);

    const hasCount = countdown > 0;

    return (
        <React.Fragment>
            <div className={cx('count-down-new-hu')}>
                {entranceAPP && props.subStatus !== 0 && <img  className={cx('count-tips')} src='https://mpics.bdstatic.com/qm/202403/pic_djVPny_1710751806.png'/>}
                <img  className={cx('count-text-hu')} src='https://mpics.bdstatic.com/qm/202403/pic_pOja1T_1710469832.png'/>
                <div className={cx('count-content-hu')}>{hasCount && <CountDownTimeStr timestamp={countdown} />}</div>
            </div>
        </React.Fragment>
    );
}

export default CountDown;
