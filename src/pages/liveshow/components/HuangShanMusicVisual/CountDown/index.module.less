@import '~@baidu/nano-theme/index.less';

@font-face {
    font-family: 'baidunumber-Medium';
    src: url(../assets/font/baidunumber-Medium.ttf) format('truetype');
}
.theme-root() !important;

.count-down-new-hu {
    position: relative;
    width: 3.74rem;
    height: 1.33rem;
    padding: 0;
    background: url('https://mpics.bdstatic.com/qm/202403/pic_0kooW0_1710420470.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-wrap: wrap;
    margin-top: .1466rem;

    .count-tips {
        height: .2533rem;
        position: absolute;
        top: .1rem;
        right: .12rem;
    }

    .count-text-hu {
        height: .18rem;
        margin-bottom: .11666rem;
        margin-top: .18rem;
    }

    .count-content-hu {
        display: flex;
        flex-wrap: nowrap;
        flex-direction: row;

        .countDownTimeNew {
            width: .238rem;
            height: .3713rem;
            border-radius: .0464rem;
            background: linear-gradient(0deg, #FFDBFF -7.02%, #FFACF2 109.65%);
            color: #212121;
            font-family: "baidunumber-Medium";
            font-size: .3733rem;
            font-weight: 500;
            line-height: .3713rem;
            margin-left: .0305rem;
            text-align: center;

            &.is-android {
                line-height: normal;
            }
        }

        .countDownUnitNew {
            color: #343434;
            text-align: center;
            font-family: "PingFang SC";
            font-size: .14rem;
            font-style: normal;
            font-weight: 600;
            line-height: .1933rem; /* 171.429% */
            margin-top: .1rem;
            margin-left: .0533rem;
            margin-right: .0533rem;
        }
    }
}
