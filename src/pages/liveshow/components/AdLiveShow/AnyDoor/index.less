/*
 * @file 任意门样式
 * <AUTHOR>
 * @date 2025-01-10 17:01:58
 */

/* 从左到右闪光动画 - 修复版 */
@keyframes shine {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(100%);
    }
}

/* 上下移动动画 - 匀速，跳两次停顿2秒 */
@keyframes moveUpDown {
    /* 第一次上移 */
    0% {
        transform: translateY(0);
    }

    10% {
        transform: translateY(calc(-1 * var(--move-distance, 12px)));
    }

    /* 第一次下移 */
    20% {
        transform: translateY(0);
    }

    /* 第二次上移 */
    30% {
        transform: translateY(calc(-1 * var(--move-distance, 12px)));
    }

    /* 第二次下移 */
    40% {
        transform: translateY(0);
    }

    /* 停顿2秒 */
    100% {
        transform: translateY(0);
    }
}

/* 按钮闪光效果类 */
.anydoor-button-shine .bottom-button {
    position: relative;
    overflow: hidden;
    background-color: var(--button-color, #F35);
}

.anydoor-button-shine .bottom-button::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(
            45deg,
            transparent 0%,
            transparent 40%,
            var(--shine-color, rgba(255, 255, 255, .4)) 50%,
            transparent 60%,
            transparent 100%
        );
    width: 200%;
    animation: shine var(--shine-duration, 3s) infinite linear;
    pointer-events: none;
}

/* 上下移动动画类 */
.anydoor-top-bottom-move {
    animation: moveUpDown 4000ms linear infinite;
}

@keyframes breathing {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(var(--breathing-scale, 1.1));
    }

    100% {
        transform: scale(1);
    }
}

/* 呼吸动画类 */
.anydoor-breath {
    animation: breathing var(--animation-duration, 2s) infinite;
}

.anydoor-portal-container {
    width: 1.09rem;
    border-radius: .06rem;
    position: fixed;
    background: #fff;
    bottom: .4rem;
    right: .1rem;
    text-align: center;
    padding: .03rem;
    margin-bottom: .86rem;
    font-size: .16rem;
    cursor: pointer;
}

.anydoor-portal-container-new {
    position: static;
    display: flex;
    flex-direction: column;
    width: 1.09rem;
    border-radius: .06rem;
    background: #fff;
    text-align: center;
    padding: .03rem;
    font-size: .16rem;
    cursor: pointer;
    box-sizing: border-box;
}

.overview-image {
    width: 1.03rem;
    border-radius: .08rem .08rem 0 0;
    object-fit: cover; /* 保证图片适配 */
}

/* 让文本部分能撑开 */
.desc-text {
    margin-top: .05rem;
    font-size: .12rem;
    font-weight: 500;
    line-height: 1.4;
    text-align: left;
    color: #1F1F1F;
    flex-grow: 1; /* 让文本部分撑开容器 */
}

/* 动态人数部分 */
.dynamic-people {
    margin-top: .04rem;
    font-size: .1rem;
    font-weight: 500;
    line-height: 1.2;
    text-align: left;
    color: #F35;
    flex-grow: 1; /* 让动态内容撑开 */
}

/* 让按钮始终在底部 */
.bottom-button {
    position: relative;
    margin-top: auto; /* 让按钮推到底部 */
    width: 100%;
    height: .21rem;
    font-size: .12rem;
    border-radius: .04rem;
    background-color: #F35;
    color: #fff;
    padding: .04rem 0;
}

.anydoor-to-top {
    margin-bottom: .42rem;
}

.anydoor-to-top-convenhance5 {
    margin-bottom: -.12rem;
}

/* Lottie动画定位 - 点击底部按钮 */
.anydoor-lottie-animation {
    // ! 会影响整体容器高度
    position: absolute;
    left: 65%;
    top: -20%;
}
