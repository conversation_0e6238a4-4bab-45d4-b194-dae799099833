/*
 * @file 任意门工具
 * <AUTHOR>
 * @date 2025-01-10 17:02:46
 */
import Cookies from 'js-cookie';
import md5 from 'md5';
import {randomString} from '@/utils/utils';
import SettingItem from '@/utils/db';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';

export const getCuid = () => {
    const BDUID = 'BIDUPSID';
    const key = 'deviceId';
    let db = new SettingItem(key);
    let deviceId = db.get();

    if (!deviceId) {
        deviceId = Cookies.get(BDUID) || randomString(32);
        deviceId = md5(deviceId);
        db.set(deviceId);
    }

    return deviceId;
};

export const requestUa = `${window.innerWidth}_${window.innerHeight}_${boxjsUtils.platformInfo.isIOS ? 'IOS'
    : boxjsUtils.platformInfo.isAndroid ? 'ANDROID' : '0'}_${boxjsUtils.platformInfo.boxVersion}`;

export const baiduMonitorUa = navigator.userAgent;

// 网络类型，1_0代表wifi 0_2代表2g、0_3代表3G、0_13代表4g、0_16代表5g,前端拿不到可不传
export function getNetwork() {
    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
    const networkMap = {
        'slow-2g': '0_2',
        '2g': '0_2',
        '3g': '0_3',
        '4g': '0_13',
        '5g': '0_16' // 部分设备可能不会返回 5G
    };

    if (connection && connection.effectiveType) {
        return networkMap[connection.effectiveType] || '1_0';
    }
    return '1_0';
}
