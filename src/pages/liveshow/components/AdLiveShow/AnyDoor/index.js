/*
 * @file 任意门组件
 * <AUTHOR>
 * @date 2025-01-10 17:01:23
 */
import React, {useEffect} from 'react';
import {getUrlAllParam} from '@/pages/liveshow/utils/getUrlParam';
import classNames from 'classnames';
import {LiveStatus} from '@/pages/liveshow/config/const';
import {useABTest, useABTestValue} from '@/abtest-sdk';
import {sendMonitor} from '../monitor';
import {adCommonShowLog} from '../monitor/ad';
import {convEnhanceValueMap, shouldAnydoorOptimize} from '../monitor/abtest';
import {baiduMonitorUa} from './utils';
import {dpSwitchMap} from './api';
import LottieAnimation from './LottieAnimation';
import './index.less';


const AnyDoor = ({
    anyMountCardBtn,
    anyMountCardTitle,
    anyMountCardImg,
    dynamicPeoplesSwitch,
    dynamicPeoplesText,
    status,
    nid,
    screen,
    anyMountCardUrlH5,
    hasClickShowModel,
    handleDoorOpen,
    anyMountCardUrlType
}) => {
    const urlParams = getUrlAllParam(location.href);
    const {userstat} = useABTest();
    const {
        bd_vid = '',
        ch = '',
        livesource = '',
        source = '',
        room_id
    } = urlParams || {};
    useEffect(() => {
        const text = `${anyMountCardTitle}，${anyMountCardBtn}`;
        adCommonShowLog({
            page: status,
            type: 'show',
            value: 'trade_recommend',
            nid,
            screen,
            serverId: '19091',
            isNewVersion: true,
            text,
            anyMountCardUrlH5,
            userstat,
            from: 'card',
            anyMountCardUrlType
        });
        // sendMonitor('click', {
        //     level: 'any-door-show',
        //     info: baiduMonitorUa,
        //     value: status,
        //     bd_vid, root: room_id, livesource, source, ch,
        //     action: `${hasClickShowModel ? 'inner' : 'out'}_card`,
        //     item: text
        // });
        // try {
        //     // Ensure performance API exists
        //     if (!window.performance) {
        //         sendMonitor('click', {level: 'performanceNotSupported', info: baiduMonitorUa});
        //         return;
        //     }

        //     const mountEnd = performance.now();
        //     let totalTime = mountEnd; // default fallback

        //     // Try modern Navigation Timing API first
        //     const navigationTiming = performance.getEntriesByType('navigation')[0];
        //     if (navigationTiming?.startTime !== undefined) {
        //         totalTime = mountEnd - navigationTiming.startTime;
        //     }
        //     // Fallback to legacy Performance Timing API
        //     else if (performance.timing?.navigationStart) {
        //         totalTime = mountEnd - performance.timing.navigationStart;
        //     }

        //     adCommonShowLog({
        //         page: status, type: 'enter', value: 'entry', nid, screen, serverId: '19334', isNewVersion: true,
        //         appToAnyDoorPerformance: Math.round(totalTime),
        //         timingApi: navigationTiming ? 'navigation' : (performance.timing ? 'legacy' : 'none'),
        //         userstat
        //     });
        // }
        // catch (error) {
        //     // Log error but don't break the component
        //     sendMonitor('click', {level: 'performanceError', info: baiduMonitorUa});
        // }
    }, [
        bd_vid, room_id, livesource, source, ch, status, nid, screen,
        anyMountCardTitle, anyMountCardBtn, anyMountCardUrlH5, userstat
    ]);

    const isOpen = dynamicPeoplesSwitch === dpSwitchMap.open;


    // const convEnhanceValue = useABTestValue('convEnhance');
    // const inConvEnhance5 = convEnhanceValue === convEnhanceValueMap.test5;

    // 根据AB测试值确定动画类型
    const animationType = (() => {
        return 'click-pointer'; // 手势动画

    })();

    // 根据动画类型生成className
    const getAnimationClass = () => {
        const classes = [];

        // 只有在类型不为'none'时才添加动画类
        if (animationType !== 'none') {
            switch (animationType) {
                case 'breath':
                    classes.push('anydoor-breath');
                    break;
                case 'top-bottom-move':
                    classes.push('anydoor-top-bottom-move');
                    break;
                case 'click-pointer':
                    // 对于click-pointer类型，我们使用Lottie动画，不需要额外的CSS类
                    break;
                // 其他类型可以在这里添加
            }
        }

        return classes.join(' ');
    };

    return (
        <div
            onClick={() => handleDoorOpen('card')}
            className={classNames('anydoor-portal-container', {
                'anydoor-to-top': status === LiveStatus.LIVEING,
                // 'anydoor-to-top-convenhance5': inConvEnhance5,
                'anydoor-portal-container-new': shouldAnydoorOptimize(hasClickShowModel)
            }, getAnimationClass())}
        >
            <img
                src={anyMountCardImg}
                className="overview-image"
            />
            <div className="desc-text">
                {anyMountCardTitle}
            </div>
            {isOpen && <div className="dynamic-people">
                {dynamicPeoplesText}
            </div>}
            <div className="bottom-button">
                {anyMountCardBtn}
                {/* 当动画类型为click-pointer时，显示Lottie动画，每2秒间隔 */}
                {animationType === 'click-pointer' && (
                    <LottieAnimation
                        type="click-pointer"
                        width={40}
                        height={40}
                        interval={500}
                        className="anydoor-lottie-animation"
                    />
                )}
            </div>
        </div>
    );
};

export default AnyDoor;
