/*
 * @file iframe弹窗组件
 * <AUTHOR>
 * @date 2025-03-15 10:00:00
 */
import React from 'react';

/**
 * iframe弹窗组件
 * @param {Object} props - 组件属性
 * @param {boolean} props.showIframe - 是否显示iframe
 * @param {boolean} props.isClosing - 是否正在关闭
 * @param {string} props.iframeUrl - iframe地址
 * @param {Function} props.closeIframe - 关闭iframe的方法
 * @returns {JSX.Element} iframe弹窗组件
 */
const IframeDoor = ({
    showIframe,
    isClosing,
    iframeUrl,
    closeIframe
}) => {
    return (
        <>
            {showIframe && (
                <>
                    {/* 背景蒙层 */}
                    <div
                        className={`door-iframe-overlay ${isClosing ? 'closing' : ''}`}
                        onClick={closeIframe}
                    />
                    {/* iframe容器 */}
                    <div className={`door-iframe-wrapper ${isClosing ? 'closing' : ''}`}>
                        {/* 圆角区域的额外蒙层 */}
                        <div className="door-iframe-corner-mask" />
                        <iframe
                            src={iframeUrl}
                            className="door-iframe"
                            title="落地页"
                        />
                    </div>
                </>
            )}
        </>
    );
};

export default IframeDoor;
