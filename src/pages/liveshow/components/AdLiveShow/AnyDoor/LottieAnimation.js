/*
 * @file 任意门Lottie动画组件
 * <AUTHOR>
 * @date 2025-03-15 10:00:00
 */
import React, {useEffect, useRef} from 'react';
import LottieManager from '@/utils/lottie';

/**
 * Lottie动画组件 - 使用LottieManager
 * @param {Object} props - 组件属性
 * @param {string} props.type - 动画类型
 * @param {number} props.width - 宽度
 * @param {number} props.height - 高度
 * @param {number} props.interval - 动画播放完成后的间隔时间（毫秒）
 * @param {boolean} props.loop - 是否循环播放
 * @param {string} props.className - 自定义类名
 * @returns {JSX.Element} Lottie动画容器
 */
const LottieAnimation = ({
    type,
    width,
    height,
    interval,
    loop = false, // 默认不循环
    className = ''
}) => {
    const animationContainer = useRef(null);

    useEffect(() => {
        if (animationContainer.current && type) {
            // 使用LottieManager初始化动画
            LottieManager.initLottie(animationContainer.current, type, {
                interval,
                loop,
                autoplay: true,
                renderer: 'svg'
            });

            // 清理函数
            return () => {
                LottieManager.destroyLottie(type);
            };
        }
    }, [type, interval, loop]);

    return (
        <div
            ref={animationContainer}
            className={className}
            style={{
                width: `${width}px`,
                height: `${height}px`,
                zIndex: 10001,
                pointerEvents: 'none'
            }}
        />
    );
};

export default LottieAnimation;
