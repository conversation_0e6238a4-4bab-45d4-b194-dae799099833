/*
 * @file 广告通用请求方法
 * <AUTHOR>
 * @date 2025-01-10 17:02:32
 */
import axios from 'axios';
import qs from 'qs';
import md5 from 'md5';
import {isDev} from '../utils/env';
import {sendMonitor} from '../monitor';

const online =  'https://cluelive.baidu.com';
// eslint-disable-next-line no-unused-vars
const dev = 'http://cluelive.baidu-int.com:8910'; // 联调使用

axios.defaults.withCredentials = true;

const axiosInstance = axios.create({
    baseURL: isDev ? online : online,
    withCredentials: true
});
// 支持cookie
axios.defaults.withCredentials = true;

export async function request({path, params}) {
    const defaultParams = {
        reqid: guid()
    };
    const finalParams = {
        ...defaultParams,
        path,
        params: JSON.stringify(params)
    };
    finalParams.sign = signGenerator(finalParams);
    const res = await axiosInstance.request({
        method: 'POST',
        url: path,
        data: qs.stringify(finalParams),
        withCredentials: true
    });
    return res || {};
}

axiosInstance.interceptors.response.use(
    response => {
        // 处理响应数据
        return response?.data?.data || {};
    },
    error => {
        return Promise.reject(error);
    }
);

// 计算规则，根据除sign参数外的其他参数进行key排序，key1=value1key2=vlaue2.. 最后再拼接一个cluelivebapikey!!!   将这个字符串进行md5
function signGenerator(formData) {
    // 按 key 进行排序
    const sortedKeys = Object.keys(formData).sort();
    // 生成排序后的字符串
    let sortedString = '';
    sortedKeys.forEach(key => {
        sortedString += `${key}=${formData[key]}`;
    });
    sortedString += 'tiebaclient!!!';
    return md5(sortedString);
}
function rand16Num(len) {
    const result = [];
    for (let i = 0; i < len; ++i) {
        result.push('0123456789abcdef'.charAt(Math.floor(Math.random() * 16)));
    }
    return result.join('');
}
function guid() {
    const curr = new Date().valueOf().toString();
    return ['4b534c47', rand16Num(4), '4' + rand16Num(3), rand16Num(4), curr.substring(0, 12)].join('-');
}

export const getGuid = () => {
    const sessionGuid = sessionStorage.getItem('ad-h5-guid');
    if (sessionGuid) {
        return sessionGuid;
    }
    const id = guid();
    sessionStorage.setItem('ad-h5-guid', id);
    return id;
};

export async function prefetchRequest(prefetchedReq, req) {
    if (!window.__prefetchDatasource__?.isPrefetchAvailable) {
        sendMonitor('click', {level: 'prefetch_no_available'});
        return await req();
    }
    if (prefetchedReq) {
        try {
            return await prefetchedReq;
        }
        catch (error) {
            // 预加载的请求错误忽略，无事发生🙂，当作未命中重新发请求
            sendMonitor('click', {level: 'prefetch_error', item: error});
        }
    }
    sendMonitor('click', {level: 'no_prefetch'});
    return await req();
}

export const getPvid = () => guid();
