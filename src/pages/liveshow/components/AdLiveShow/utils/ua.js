/*
 * @file ua判断
 * <AUTHOR>
 * @date 2025-02-25 19:27:25
 */

/**
* 综合判断是否为华为生态设备（华为/鸿蒙）
*/
export function isHuaweiPhone() {
    return isHuaweiDevice() || isHarmonyOS();
}

/**
 * 判断是否为华为设备
 */
export function isHuaweiDevice() {
    const userAgent = navigator.userAgent.toLowerCase();
    return /android/.test(userAgent) && /huawei/.test(userAgent);
}

/**
   * 判断是否为荣耀设备
   */
export function isHonorDevice() {
    const userAgent = navigator.userAgent.toLowerCase();
    return /android/.test(userAgent) && /honor/.test(userAgent);
}

/**
   * 判断是否为鸿蒙操作系统
   */
export function isHarmonyOS() {
    const userAgent = navigator.userAgent.toLowerCase();
    return /android/.test(userAgent) && /harmonyos/.test(userAgent);
}


// 百度地图 会全量开启自动播放
export function isBaiduMap() {
    return /baidumap/i.test(navigator.userAgent);
}

export const isXiaomiPhone = (ua = navigator.userAgent.toLowerCase()) => {
    return /xiaomi|redmi|miuibrowser/.test(ua);
};

export const isVivoPhone = (ua = navigator.userAgent.toLowerCase()) => {
    return /vivo|vivobrowser/.test(ua);
};

// 判断是否是夸克浏览器（Quark Browser）
export function isQuarkBrowser() {
    const ua = navigator.userAgent.toLowerCase();
    return ua.includes('quark');
}

// 判断是否是 QQ 浏览器（QQ Browser）
export function isQQBrowser() {
    const ua = navigator.userAgent.toLowerCase();
    return ua.includes('qqbrowser');
}

// 判断是否是 UC 浏览器（UC Browser）
export function isUCBrowser() {
    const ua = navigator.userAgent.toLowerCase();
    return ua.includes('ucbrowser');
}


export function isHonorWhiteLabelBrowser() {
    const ua = navigator.userAgent;

    const match = ua.match(/HonorBrowser\/([\d.]+)/i);

    if (match) {
        const version = match[1]; // 获取版本号字符串
        return compareVersion(version, '9.2.0') >= 0;
    }

    return false;
}

// 比较版本号函数（返回 1 表示 v1 > v2，-1 表示 v1 < v2，0 表示相等）
function compareVersion(v1, v2) {
    const s1 = v1.split('.').map(Number);
    const s2 = v2.split('.').map(Number);
    const len = Math.max(s1.length, s2.length);

    for (let i = 0; i < len; i++) {
        const n1 = s1[i] || 0;
        const n2 = s2[i] || 0;
        if (n1 > n2) {
            return 1;
        }
        if (n1 < n2) {
            return -1;
        }
    }
    return 0;
}
