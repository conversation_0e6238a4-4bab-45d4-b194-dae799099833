/*
 * @file ua判断
 * <AUTHOR>
 * @date 2025-02-25 19:27:25
 */

/**
* 综合判断是否为华为生态设备（华为/鸿蒙）
*/
export function isHuaweiPhone() {
    return isHuaweiDevice() || isHarmonyOS();
}

/**
 * 判断是否为华为设备
 */
export function isHuaweiDevice() {
    const userAgent = navigator.userAgent.toLowerCase();
    return /android/.test(userAgent) && /huawei/.test(userAgent);
}

/**
   * 判断是否为荣耀设备
   */
export function isHonorDevice() {
    const userAgent = navigator.userAgent.toLowerCase();
    return /android/.test(userAgent) && /honor/.test(userAgent);
}

/**
   * 判断是否为鸿蒙操作系统
   */
export function isHarmonyOS() {
    const userAgent = navigator.userAgent.toLowerCase();
    return /android/.test(userAgent) && /harmonyos/.test(userAgent);
}


// 百度地图 会全量开启自动播放
export function isBaiduMap() {
    return /baidumap/i.test(navigator.userAgent);
}

export const isXiaomiPhone = (ua = navigator.userAgent.toLowerCase()) => {
    return /xiaomi|redmi|miuibrowser/.test(ua);
};

export const isVivoPhone = (ua = navigator.userAgent.toLowerCase()) => {
    return /vivo|vivobrowser/.test(ua);
};

// 判断是否是夸克浏览器（Quark Browser）
export function isQuarkBrowser() {
    const ua = navigator.userAgent.toLowerCase();
    return ua.includes('quark');
}

// 判断是否是 QQ 浏览器（QQ Browser）
export function isQQBrowser() {
    const ua = navigator.userAgent.toLowerCase();
    return ua.includes('qqbrowser');
}

// 判断是否是 UC 浏览器（UC Browser）
export function isUCBrowser() {
    const ua = navigator.userAgent.toLowerCase();
    return ua.includes('ucbrowser');
}
