/**
 * @file isAppRedirectBlocked.js
 * <AUTHOR>
 * @date 2025-2-25 15:27:54
 */

// 商业页面 不跳转native的条件
// 1. 荣耀
// 2. 手机百度内且isAppRedirectBlocked
function getUrlAllParam(url) {
    const params = {};
    const queryString = url.split('?')[1];
    if (!queryString) return params;

    queryString.split('&').forEach(param => {
        const [key, value] = param.split('=');
        params[decodeURIComponent(key)] = value !== undefined ? decodeURIComponent(value) : undefined;
    });

    return params;
}

export function isAppRedirectBlocked() {
    const {is_jump_app} = getUrlAllParam(location.href) || {};
    return is_jump_app !== undefined && +is_jump_app === 0;
}

// 商业直播H5路径拼接
// 情况1：room_id=xx&livesource=xx&source=xx&bd_vid=xx&ch=xx&bd_bxst=xx&fid=xx
// 情况2：room_id=9997353272&ch=4&livesource=xxx&is_jump_app=0
// bd_vid 有值或 is_jump_app=0 时支持在H5内完成转化
export function isAdApp() {
    const urlParams = getUrlAllParam(location.href)
    const {bd_vid = ''} = urlParams || {};
    return bd_vid || isAppRedirectBlocked();
}
