import React from 'react';
import {Image} from '@baidu/nano-react';
import useTotalUsers from '@/pages/liveshow/components/common/TotalUsers';
import './index.less';

function LiveRoomClosed(props) {
    const totalUsers = useTotalUsers(props.totalUsers);
    let {
        className = '',
        hostAvatar,
        hostName
    } = props;

    return (
        <div className={`ad-live-room-closed-wrapper ${className}`}>
            <p className='live-status'>直播已结束</p>
            <p className='total-users'>本场累积观看人数 {totalUsers}</p>

            {hostAvatar && <Image className='auther-avatar' url={hostAvatar} />}
            <p className='auther-name'>{hostName}</p>
        </div>
    );
}

export default LiveRoomClosed;
