/*
 * @file 埋点
 * <AUTHOR>
 * @date 2025-01-10 17:03:47
 */
import {initABTestsConfig} from '@/abtest-sdk';
import {getUrlAllParam} from '@/pages/liveshow/utils/getUrlParam';
import {abTestConfig} from './abtest';

const accountKey = Object.freeze({
    FENGCHAO: '863779a7a3a15a5b2bff4338a01f002e'
});


export const sendMonitor = (target, extra = {}, source = '_trackCustomEvent') => {
    try {
        window._hmt = window._hmt || [];
        if (window._hmt) {
            const key = accountKey.FENGCHAO;
            window._hmt.push(['_setAccount', key]);
            window._hmt.push(['_setAutoPageview', false]);
            // 使用最新的userstat
            extra = {...extra, userstat: window.$abtestUserstat};
            console.log(target, source, extra, 'monitor');
            initABTestsConfig(abTestConfig);
            if (target) {
                window._hmt.push([source, target, extra]);
            }
            else {
                window._hmt.push([source, extra]);
            }
        }
    }
    catch (e) {
        console.log(e, 'error monitor');
    }
};

const {bd_vid} = getUrlAllParam(location.href) || {};
export function initMonitor(userId = bd_vid, {hmKey = accountKey.FENGCHAO} = {}) {
    try {
        // 百度统计，启用用户ID统计策略
        sendMonitor(userId, {}, '_setUserId');
        // 增加optId
        sendMonitor(null, {optid: userId}, '_setUserProperty');
        const hm = document.createElement('script');
        hm.src = `https://hm.baidu.com/hm.js?${hmKey}`;
        hm.async = 'async';
        const s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(hm, s);

    }
    catch (e) {
        // do nothing
    }
}
