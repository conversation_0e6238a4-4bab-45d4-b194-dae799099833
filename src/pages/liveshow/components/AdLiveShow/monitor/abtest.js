/* eslint-disable max-len */
/*
 * @file 直播中AB测试配置
 * <AUTHOR>
 * @date 2025-02-11 16:00:00
 */
import {isIOS} from '@baidu/xbox-native/detect';
import {isOppoBrowser} from '@/utils/env';
import {getUrlParam} from '@/utils';
import {getUserstat} from '@/abtest-sdk';
import {isBaiduMap, isQQBrowser, isQuarkBrowser, isUCBrowser, isVivoPhone, isXiaomiPhone} from '../utils/ua';

function getExperimentGroupFromBaiduid() {
    try {
        const cookie = document.cookie;
        const match = cookie.match(/BAIDUID=([^;]+)/);
        if (!match) {
            return 'empty';
        }
        const baiduid = match[1];
        const firstChar = baiduid.charAt(0).toUpperCase();

        if ('01234567'.includes(firstChar)) {
            return 'test';
        }
        if ('89ABCDEF'.includes(firstChar)) {
            return 'origin';
        }
        return 'empty';
    }
    catch (error) {
        return 'empty';
    }
}


export const prerenderExp = getExperimentGroupFromBaiduid();

const commonTestMap = {
    origin: 0,
    test: 1
};

// AB测试配置管理
export const abTestConfig = Object.fromEntries(
    [
        'confirmJump', // 1001
        'autoPlayStrategy', // 1002
        'isJump', // 1003
        'isAutoPlay', // 1004
        'isHonorNotJumpApp', // 1005
        'isJumpTip', // 1006
        'anydoorOptimize', // 1007
        'autoPlayAndMiniScreen', // 1008
        'playIconFull', // 1009
        'newAutoPlay', // 1010
        'anydoorAnimation', // 1011 - 新增任意门动效实验
        'bqtAutoPlay', // 1012 百青藤自动播放实验
        'newAnimation', // 1013-任意门动效-手势/呼吸/上下移动
        'chatBar', // 1014-互动
        'chatBarPro', // 1015-互动II
        'screenIframe', // 1016-内嵌iframe落地页
        'baiduAutoPlay', // 1017-手百自动播放
        'convEnhance' // 1018-端外直播间底部互动区域转化增强优化
    ].map((paramName, index) => [
        paramName,
        {key: 1001 + index, value: -1, paramName}
    ])
);

export const shouldAnydoorOptimize = (hasClickShowModel = false) => hasClickShowModel && (isMultiMiniScreen() || location.href.includes('door_dev_optimize'));

export const bqtUrlLimit = getUrlParam('livesource') === 'bes_card';

export const isMultiAutoPlay = (bqtAutoPlay) => {
    const urlLimit = getUrlParam('livesource') === 'bes_card';
    let oldBqtAutoPlay = bqtAutoPlay === commonTestMap.test && urlLimit;
    const hasBqtConfKey = location.href.includes('bqt_conf');
    if (hasBqtConfKey) {
        oldBqtAutoPlay = false;
    }
    return isBaiduMap() || isIOS() || oldBqtAutoPlay || shouldBqtAutoPlay;
};

export const isMultiMiniScreen = () => {
    const urlLimit = location.href.includes('&xl=1');
    return isOppoBrowser() || isVivoPhone() || isXiaomiPhone() || isQuarkBrowser() || isQQBrowser() || isUCBrowser() || urlLimit;
};

export const commonInTest = (value) => value === commonTestMap.test;

export const chatBarProValueMap = {
    /**
     * 对照组
     */
    origin: 0,
    /**
     * 任意门+点赞
     */
    test1: 1,
    /**
     * 任意门+分享
     */
    test2: 2,
    /**
     * 任意门+评论
     */
    test3: 3,
    /**
     * 任意门+ALL */
    test4: 4
};


export const convEnhanceValueMap = {
    /**
     * 对照组
     */
    origin: 0,
    /**
     * 实验组①-默认展开保留图标轮播
     */
    test1: 1,
    /**
     * 实验组②-默认展开不保留图标轮播
     */
    test2: 2,
    /**
     * 实验组③-延迟展开不保留图标不轮播
     */
    test3: 3,
    /**
     * 实验组④-实验组①基础上，去掉评论和分享
     */
    test4: 4,
    /**
     * 实验组⑤-任意门+胶囊左对齐
     */
    test5: 5
};


// 百青藤、搜索场景是否命中某功能
const commonFeatureConfig = {
    test: 1,
    origin: 0
};

/**
 * 参数设计：
    fc_conf=0=00
    fc_conf=1=01
    fc_conf=2=10
    fc_conf=3=11
    解出来的二进制的每一位代表某个功能是否开启，1:开启；0:未开启。
 *  url携带的时候携带十进制，这样参数可以短一些，也方便后续技术复用和扩展
 */
export function parseFeatureValue(paramKey) {
    const urlParams = new URLSearchParams(window.location.search);
    const configValue = urlParams.get(paramKey);
    if (+configValue === commonFeatureConfig.test) {
        return commonFeatureConfig.test;
    }
    return commonFeatureConfig.origin;
}
// url params出现了fc_conf=1时，直接打开url
// eslint-disable-next-line no-unused-vars
const fcConfValue = parseFeatureValue('fc_conf');

// eslint-disable-next-line no-unused-vars
const bqtValue = parseFeatureValue('bqt_conf');
export const shouldDirectOpenUrl = fcConfValue === commonFeatureConfig.test;
export const shouldBqtAutoPlay = bqtValue === commonFeatureConfig.test;

export const q2cTestUserstat = () => getUserstat({q2c: {
    key: '5001',
    paramName: 'q2c',
    value: window.isInQ2CTest ? commonFeatureConfig.test : commonFeatureConfig.origin
}});