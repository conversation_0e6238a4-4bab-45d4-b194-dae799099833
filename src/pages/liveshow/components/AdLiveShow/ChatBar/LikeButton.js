import React, {useRef} from 'react';
import './LikeButton.less';
import {useABTest} from '@/abtest-sdk';
import {once} from 'lodash';
import {adCommonClickLog} from '../monitor/ad';
import s1 from './img/s1.svg';
import s2 from './img/s2.svg';
import s3 from './img/s3.svg';
import good from './img/good.svg';

const iconList = [s1, s2, s3];
const adCommonClickLogOnce = once(adCommonClickLog);
export default function LikeButton(props) {
    const {
        status,
        screen,
        nid,
        setLikeAction
    } = props;
    const iconIndexRef = useRef(0);
    const btnRef = useRef(null);
    const {userstat} = useABTest();
    const handleClick = () => {
        setLikeAction(true);
        adCommonClickLogOnce({
            page: status,
            value: 'entry',
            type: 'enter',
            nid,
            screen,
            serverId: '19533',
            userstat
        });

        if (!btnRef.current) {
            return;
        }

        const rect = btnRef.current.getBoundingClientRect();

        const icon = iconList[iconIndexRef.current % iconList.length];
        iconIndexRef.current++;

        const leftOffset = Math.random() * 30 - 15;
        const x = rect.left + rect.width / 2 + leftOffset;
        const y = rect.top;

        // 创建图片DOM
        const el = document.createElement('img');
        el.src = icon;
        el.className = 'like-bubble';

        Object.assign(el.style, {
            position: 'fixed',
            left: `${x}px`,
            top: `${y}px`,
            width: '24px',
            height: '24px',
            animation: 'like-float 1.2s ease-out forwards',
            pointerEvents: 'none',
            userSelect: 'none',
            zIndex: '9999',
            filter: 'drop-shadow(0 0 2px rgba(0, 0, 0, 0.4))'
        });

        document.body.appendChild(el);

        // 动画结束后移除
        el.addEventListener('animationend', () => {
            document.body.removeChild(el);
        });
    };

    return (
        <>
            <div
                ref={btnRef}
                onClick={handleClick}
                style={{
                    fontSize: 32,
                    width: 40,
                    height: 40,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    cursor: 'pointer',
                    position: 'relative',
                    zIndex: 1
                }}
            >
                <img src={good} style={{width: 40, height: 40}} />
            </div>
        </>
    );
}