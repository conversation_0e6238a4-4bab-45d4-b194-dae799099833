import {PageStore} from '@/pages/liveshow/service/register';
import {getUrlParam} from '@/utils';
import login from '@/utils/login';
import {jsonp} from '@/utils/request';
import {message} from 'antd';
import React, {useEffect, useRef, useState} from 'react';
import {useABTest} from '@/abtest-sdk';
import {withBoundary} from 'react-suspense-boundary';
import classNames from 'classnames';
import {isIOS} from '@baidu/boxx/env';
import {adCommonShowLog} from '../monitor/ad';

export function sendMsgWithXaf({inputValue, userInfo, setChatList}) {
    // xaf 风控校验
    let jsToken = '';
    try {
        window.xaf
                && window.xaf.report({
                    aid: '5969',
                    biz: {
                        roomid: PageStore.roomId
                    },
                    complete: res => {
                        if (+res.code === 0) {
                            jsToken = res.jt;
                        }
                        sendMsg({jsToken, inputValue, userInfo, setChatList});
                    }
                });
    }
    catch (e) {
        sendMsg({jsToken, inputValue, userInfo, setChatList});
    }
}
const config = {
    // video最多等待加载时长
    videoMaxWait: 15000,
    // 发送消息最大长度
    maxLength: 50,
    // 是否前端限制发送频率
    isLockMsg: true,
    // 发送频率限制时长
    lockTime: 5000,
    // cdn拉取消息同步间隔时长
    messageRefreshTime: 2000,
    // 聊天室最大存在消息数量
    maxChatListLength: 300
};

// 发送消息时Tip
export const ChatMsgTip = {
    MAX_LENGTH: '您的评论字数已超过最大长度',
    ONLINE: '请检查网络后重试',
    DISABLE_MSG: '您已被当前主播禁言，暂不可发送聊天内容',
    NO_CHAT_ROOM: '评论功能已关闭',
    CHAT_LOCK: '说的太快了,请稍后再发吧'
};
export const SendMsgErrorEnum = {
    NO_MSGID: 1,
    NO_CHAT_ROOM: 2,
    DISABLE_MSG: 3,
    CHAT_LOCK: 4
};
function sendMsg({jsToken, inputValue, userInfo, setChatList}) {
    let text = inputValue;
    if (!text) {
        return;
    }
    let tip = '';
    if (text.length > config.maxLength) {
        tip = ChatMsgTip.MAX_LENGTH;
    }
    if (tip) {
        message.config({top: '90%'});
        message.warning(tip);
        return;
    }
    sendMsgAPI({
        text,
        jt: jsToken,
        callback: data => {
            if (+data.has_chat_room === 0) {
                message.config({top: '90%'});
                message.warning(ChatMsgTip.NO_CHAT_ROOM);
            }
            else if (+data.is_user_block === 1) {
                // 被审核禁言
                message.config({top: '90%'});
                message.warning(ChatMsgTip.DISABLE_MSG);
            }
            else if (+data.is_frequency_send === 1) {
                // 限制发言频率
                message.config({top: '90%'});
                message.warning(ChatMsgTip.CHAT_LOCK);
            }
            else {
                if (userInfo.user_name) {
                    const newLikeMessage = {
                        msgId: `text-${Date.now()}`,
                        username: userInfo.user_name,
                        content: text,
                        avatarSrc: userInfo.avatar,
                        publishTime: new Date().toISOString(),
                        responseUser: null
                    };
                    setChatList(prev => [...prev, newLikeMessage]);
                }
            }
        }
    });
}
const osname = getUrlParam('source');
const HOST = `${location.protocol}//mbd.baidu.com`;
const sendMsgAPI = ({callback, ...msgInfo}) => {
    let url = `${HOST}/searchbox`;
    let data = {
        cmd: 366,
        action: 'star',
        service: 'bdbox',
        osname,
        data: encodeURIComponent(
            JSON.stringify({
                data: {
                    roomid: PageStore.roomId,
                    device_id: PageStore.deviceId,
                    source_type: 1,
                    biz: {
                        roomid: PageStore.roomId
                    },
                    ...msgInfo
                }
            }),
        )
    };
    return jsonp(url, data).then(resData => {
        if (+resData.errno === 0) {
            const ans =  (resData.data && resData.data[366]) || {};
            callback?.(ans);
            return ans;
        }
        return {};
    });
};
function ChatInputTrigger(props) {
    const {
        userInfo, setChatList, status, nid, screen,
        showInput, setShowInput
    } = props;
    const inputRef = useRef(null);
    const isLogin = userInfo?.is_login; // 登录状态 0:未登录，1：已登陆

    const [inputValue, setInputValue] = useState('');
    const {userstat} = useABTest();

    const handleClick = () => {
        adCommonShowLog({
            page: status,
            type: 'enter',
            value: 'entry',
            nid,
            screen,
            serverId: '19534',
            isNewVersion: true,
            userstat,
            isLogin,
            action: 'click'
        });
        if (!isLogin) {
            login();
            return;
        }
        setShowInput(true);

        if (isIOS) {
            inputRef.current?.focus();
        }
        else {
            // 等待 input 渲染后聚焦
            requestAnimationFrame(() => {
                inputRef.current?.focus();
            });
        }

    };


    const handleBlur = () => {
        setShowInput(false);
    };
    const handleSend = (e) => {
        try {
            e.preventDefault();
            adCommonShowLog({
                page: status,
                type: 'enter',
                value: 'entry',
                nid,
                screen,
                serverId: '19534',
                isNewVersion: true,
                userstat,
                isLogin,
                action: 'send',
                text: inputValue
            });
            if (!inputValue.trim()) {
                return;
            }
            sendMsgWithXaf({
                inputValue,
                userInfo,
                setChatList
            });
            setInputValue('');
            setShowInput(false);
            requestAnimationFrame(() => {
                inputRef.current?.focus();
            });
        }
        catch (error) {
            console.error('发送失败', error);
        }
    };

    const resizeTextarea = () => {
        const textarea = inputRef.current;
        if (!textarea) {
            return;
        }

        // 先重设高度为最小行数
        textarea.style.height = 'auto';

        const lineHeight = parseInt(window.getComputedStyle(textarea).lineHeight, 10) || 20;
        const maxHeight = lineHeight * 3; // 限制为最多3行

        textarea.style.overflowY = textarea.scrollHeight > maxHeight ? 'auto' : 'hidden';
        textarea.style.height = Math.min(textarea.scrollHeight, maxHeight) + 'px';
    };

    useEffect(() => {
        resizeTextarea();
    }, [inputValue]);

    return (
        <>
            {!showInput && (
                <div className="input-like" onClick={handleClick}>
                    聊一聊
                </div>
            )}
            <textarea
                // enterKeyHint='send' // warning
                enterkeyhint='send'
                ref={inputRef}
                type="text"
                rows={1}
                style={{
                    width: '100%',
                    resize: 'none',
                    lineHeight: '1.5',
                    overflowY: 'hidden',
                    fontSize: '16px',
                    padding: '8px',
                    ...(!showInput ? {
                        opacity: 0,
                        position: 'absolute',
                        pointerEvents: 'none'
                    } : {
                        height: 'auto'
                    })
                }}
                value={inputValue}
                // 原input样式: className='focus-input-box'
                className='chat-input-field'
                onChange={(e) => setInputValue(e.target.value)}
                onBlur={handleBlur}
                onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                        handleSend(e);
                    }
                }}
            />
            {showInput && <div
                className={classNames('send-btn', {
                    'disabled-btn': !inputValue.trim()
                })}
                // blur 事件先于 click 事件，所以这里不能click
                onMouseDown={handleSend}
            >发送</div>}
        </>
    );
}

export default withBoundary({
    renderError() {
        return null;
    }
})(ChatInputTrigger);
