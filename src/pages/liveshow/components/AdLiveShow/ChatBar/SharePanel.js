import React, {useState, useEffect, useCallback} from 'react';
import copy from 'copy-to-clipboard';
import {message} from 'antd';
import {useABTest} from '@/abtest-sdk';
import {adCommonShowLog} from '../monitor/ad';
import close from './img/close.svg';

const SharePanel = (props) => {
    const {visible, onClose, shareUrl = location.href, mainTitle} = props;
    const [isVisible, setIsVisible] = useState(visible);
    const {status, nid, screen} = props;
    const {userstat} = useABTest();

    useEffect(() => {
        setIsVisible(visible);
        if (visible) {
            adCommonShowLog({
                page: status,
                type: 'enter',
                value: 'entry',
                nid,
                screen,
                serverId: '19536',
                isNewVersion: true,
                userstat,
                action: 'share_panel_show'
            });
        }
    }, [nid, screen, status, userstat, visible]);


    function launchWeixin() {
        setTimeout(() => {
            location.href = 'weixin://';
        }, 500);
    }
    const handleShare = useCallback(async (type) => {
        const shareContent = '【' + mainTitle + '】' + ' ' + shareUrl;
        adCommonShowLog({
            page: status,
            type: 'enter',
            value: 'entry',
            nid,
            screen,
            serverId: '19536',
            isNewVersion: true,
            userstat,
            action: 'share_panel_click',
            text: type
        });
        switch (type) {
            case 'wechat':
                copy(shareContent);
                message.config({top: '90%'});
                message.success('直播链接复制成功');
                launchWeixin('weixin');
                break;
            case 'copy':
                copy(shareContent);
                message.config({top: '90%'});
                message.success('直播链接复制成功');
                break;
            default:
                break;
        }
        onClose?.();
    }, [mainTitle, nid, screen, shareUrl, status, userstat]);

    if (!isVisible) {
        return null;
    }

    return (
        <div className="share-panel">
            <div className="share-panel-mask" onClick={onClose} />
            <div className="share-panel-content">
                <div className="share-top">
                    <div className="text">分享到</div>
                    <div
                        className="close"
                        onClick={onClose}
                    >
                        <img
                            src={close}

                        />
                    </div>
                </div>
                <div className="share-options">
                    <div className="share-option" onClick={() => handleShare('wechat')}>
                        <div className="share-icon wechat-icon" />
                        <span>微信</span>
                    </div>
                    <div className="share-option" onClick={() => handleShare('copy')}>
                        <div className="share-icon copy-icon" />
                        <span>复制链接</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SharePanel;
