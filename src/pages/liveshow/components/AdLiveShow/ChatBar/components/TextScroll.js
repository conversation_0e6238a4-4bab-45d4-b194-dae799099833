/*
 * @file 文字滚动组件
 * <AUTHOR> Assistant
 * @date 2025-01-07
 */
import React from 'react';
import {useAdLiveShowContext} from '../../context';

/**
 * 文字滚动组件 - 可复用的文字轮播组件
 * @param {Object} props - 组件属性
 * @param {Array} props.textItems - 文字项数组
 * @param {string} props.className - 额外的CSS类名
 * @returns {JSX.Element} 文字滚动组件
 */
const TextScroll = ({textItems, className = ''}) => {
    // 从context获取数据或使用传入的textItems
    let {textItems: originalItems} = useAdLiveShowContext();
    if (textItems) {
        originalItems = textItems;
    }

    // 处理循环逻辑：确保平滑循环，避免抖动
    const getProcessedItems = (items) => {
        if (items.length === 1) {
            // 单项：不需要滚动，直接显示
            return items;
        }
        else if (items.length === 2) {
            // 两项：重复一次形成4项循环
            return [...items, ...items];
        }
        else if (items.length === 3) {
            // 三项：添加第一项作为循环项
            return [...items, items[0]];
        }
        // 四项或更多：添加第一项作为循环项
        return [...items, items[0]];

    };

    const processedItems = getProcessedItems(originalItems);

    // 根据items数量动态设置动画类名
    const getAnimationClass = (itemCount) => {
        if (itemCount === 1) {
            return 'no-scroll'; // 单项不滚动
        }
        if (itemCount === 2) {
            return 'scroll-2-items'; // 2项滚动
        }
        if (itemCount === 3) {
            return 'scroll-3-items'; // 3项滚动
        }
        return 'scroll-4-items'; // 4项或更多
    };

    const animationClass = getAnimationClass(originalItems.length);

    return (
        <div className={`text-scroll ${animationClass} ${className}`}>
            {processedItems.map((Text, index) => (
                <div key={`${Text}-${index}`} className='text-item'>
                    {typeof Text === 'function' ? <Text /> : Text}
                </div>
            ))}
        </div>
    );
};

export default TextScroll;
