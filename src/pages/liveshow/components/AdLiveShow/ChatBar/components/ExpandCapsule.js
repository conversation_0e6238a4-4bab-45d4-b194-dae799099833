/*
 * @file 展开模式胶囊组件 (inConvEnhance4)
 * <AUTHOR> Assistant
 * @date 2025-01-07
 */
import React, {useState, useEffect} from 'react';
import classNames from 'classnames';
import {useAdLiveShowContext} from '../../context';
import TextScroll from './TextScroll';

/**
 * 展开模式胶囊组件 - 从图标展开到文字
 * @param {Object} props - 组件属性
 * @param {Function} props.onDoorOpen - 点击回调函数
 * @returns {JSX.Element} 展开胶囊组件
 */
const ExpandCapsule = ({onDoorOpen}) => {
    const [animationState, setAnimationState] = useState('collapsed');

    useEffect(() => {
    // 5秒后开始展开动画
        const expandTimer = setTimeout(() => {
            setAnimationState('expanding');
        }, 5000);

        // 5秒 + 0.8秒后设置为展开完成状态
        const completeTimer = setTimeout(() => {
            setAnimationState('expanded');
        }, 5800); // 或根据实际动画时长调整

        return () => {
            clearTimeout(expandTimer);
            clearTimeout(completeTimer);
        };
    }, []);


    const {textItems = []} = useAdLiveShowContext();
    const preState = animationState === 'collapsed';
    return (
        <div className='door-wrap'>
            <div
                className={`icon-1 ${animationState}`}
                onClick={onDoorOpen}
            >
                <div className='icon'></div>
                <div className={classNames('text-container', {
                    'pre-expanded-state': preState
                })}
                >
                    <TextScroll textItems={[textItems[0]]} />
                </div>
            </div>
        </div>
    );
};

export default ExpandCapsule;
