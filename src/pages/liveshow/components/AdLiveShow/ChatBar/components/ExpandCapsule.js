/* eslint-disable max-len */
/*
 * @file 展开模式胶囊组件 (inConvEnhance4)
 * <AUTHOR> Assistant
 * @date 2025-01-07
 */
import React from 'react';
import {useAdLiveShowContext} from '../../context';
import TextScroll from './TextScroll';

/**
 * 展开模式胶囊组件 - 从图标展开到文字
 * @param {Object} props - 组件属性
 * @param {Function} props.onDoorOpen - 点击回调函数
 * @returns {JSX.Element} 展开胶囊组件
 */
const ExpandCapsule = ({onDoorOpen}) => {
    const {textItems = []} = useAdLiveShowContext();
    const textCompItems = textItems.map((item, index) => (
        <div key={index} className='text-item' style={{display: 'flex', alignItems: 'center', justifyContent: 'center'}}>
            <div className='icon' style={{width: '45px', height: '45px'}}></div>
            <span>{item}</span>
        </div>
    ));
    return (
        <div className='door-wrap' style={{width: '100%', maxWidth: '100%'}}>
            <div className='icon-1 carousel-mode button-type' onClick={onDoorOpen}>
                <div className='text-container'>
                    <TextScroll textItems={textCompItems} />
                </div>
            </div>
        </div>
    );
};

export default ExpandCapsule;
