/*
 * @file 默认模式胶囊组件
 * <AUTHOR> Assistant
 * @date 2025-01-07
 */
import React from 'react';

/**
 * 默认模式胶囊组件 - 只展示任意门图标
 * @param {Object} props - 组件属性
 * @param {Function} props.onDoorOpen - 点击回调函数
 * @returns {JSX.Element} 默认胶囊组件
 */
const DefaultCapsule = ({onDoorOpen}) => {
    return (
        <div className='icon-1' onClick={onDoorOpen}>
        </div>
    );
};

export default DefaultCapsule;
