/*
 * @file 无图标轮播模式胶囊组件 (inConvEnhance2)
 * <AUTHOR> Assistant
 * @date 2025-01-07
 */
import React from 'react';
import TextScroll from './TextScroll';

/**
 * 无图标轮播模式胶囊组件 - 只有文字轮播
 * @param {Object} props - 组件属性
 * @param {Function} props.onDoorOpen - 点击回调函数
 * @returns {JSX.Element} 无图标轮播胶囊组件
 */
const NoIconCarouselCapsule = ({onDoorOpen}) => {
    return (
        <div className='door-wrap'>
            <div className='icon-1 no-icon-mode' onClick={onDoorOpen}>
                <div className='text-container'>
                    <TextScroll />
                </div>
            </div>
        </div>
    );
};

export default NoIconCarouselCapsule;
