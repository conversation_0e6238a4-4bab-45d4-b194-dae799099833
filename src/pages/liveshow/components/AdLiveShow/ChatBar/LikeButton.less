.like-button-container {
    position: relative;
    width: 60px;
    height: 60px;
}

.like-button {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #fff;
    border: none;
    font-size: 32px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(26, 115, 232, .1);
    transition: transform .1s ease;

    &:active {
        transform: scale(.95);
    }
}

.bubble-container {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

@keyframes like-float {
    0% {
        opacity: 1;
        transform: translateY(0) scale(2);
    }

    50% {
        opacity: .95;
        transform: translateY(-100px) scale(1.5);
    }

    85% {
        opacity: .9;
        transform: translateY(-140px) scale(1.2);
    }

    100% {
        opacity: 0;
        transform: translateY(-160px) scale(1);
    }
}

.like-bubble {
    position: absolute;
    bottom: 40px;
    pointer-events: none;
    font-size: 28px;
    filter: drop-shadow(0 2px 8px rgba(26, 115, 232, .3));
    animation: like-float 1.2s ease-out forwards;
    transform-origin: center bottom;
    will-change: transform, opacity;
}
