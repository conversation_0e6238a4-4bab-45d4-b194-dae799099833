@import (reference) '../../../assets/style-util.less';

@height: 120pr;

.show-input-chat-bar-wrapper {
    padding: 7px 17px !important;
    width: 80%;

    input {
        outline: none;
        border: none;
        border-radius: 12px;
        padding: 9px;
        background-color: #F5F5F5;
    }
}

.in-conv-enhance-test5 {
    justify-content: flex-start !important;
}

.chat-input-field {
    background-color: #F5F5F5;
    position: relative;
    height: auto;
    font-size: 48pr;
    border: none;
    padding: 9px;
    flex: 1;
    box-sizing: border-box;
    border-radius: 12px;
    outline: none;
    resize: none; /* 禁止用户调整大小 */
    overflow-y: auto; /* 允许垂直滚动 */

    &::placeholder {
        color: #999;
    }
}

.chat-bar-wrapper {
    width: 100%;
    height: 100%;
    // padding: 0 30pr;
    box-sizing: border-box;
    display: flex;
    padding: 10px;
    align-items: center;
    justify-content: flex-end;
    color: #fff;
    gap: 30pr;

    .input-like {
        position: relative;
        box-sizing: border-box;
        height: @height;
        font-size: 48pr;
        line-height: @height;
        padding: 0 48pr;
        flex: 1;
        .bgMask(.23, 60pr);
    }

    .focus-input-box {
        position: relative;
        box-sizing: border-box;
        height: @height;
        font-size: 48pr;
        line-height: @height;
        padding: 0 48pr;
        flex: 1;
    }

    .send-btn {
        color: #F35;
        font-size: 16px;
    }

    .disabled-btn {
        opacity: .5;
    }

    .icon-1,
    .icon-2,
    .icon-3 {
        width: 40px;
        height: 40px;
        margin-left: 0;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center center;
        flex: none;
    }

    .icon-1 {
        background-image: url(./img/gift.svg);
        position: relative;
    }

    .door-wrap {
        position: relative;
        display: flex;
        align-items: center;
        width: auto;
        max-width: 300px;

        .pre-expanded-state {
            display: none;
        }

        .icon-1 {
            height: .4rem;
            background: #F35;
            border-radius: 20px;
            display: inline-flex;
            align-items: center;
            padding: 0 8px;
            box-shadow: 0 2px 8px rgba(255, 107, 53, .3);
            cursor: pointer;
            transition: all .3s ease;
            position: relative;
            min-width: 80px;
            max-width: 280px;
            width: auto;

            &.collapsed {
                width: 40px;
                min-width: 40px;
                padding: 0;
                justify-content: center;

                .icon {
                    margin-right: 0;
                    width: 45px;
                    height: 45px;
                }

                .text-container {
                    width: 0;
                    opacity: 0;
                    overflow: hidden;
                }
            }

            &.expanding {
                animation: expandCapsule .5s ease-out forwards;
                justify-content: center;

                .icon {
                    animation: fadeOutIcon .25s ease-out forwards;
                }

                .text-container {
                    opacity: 0 !important;
                    transform: scale(0) !important;
                    transition: none;
                    animation: showText .3s ease-out .5s forwards;
                }
            }

            &.expanded {
                justify-content: center;

                .icon {
                    width: 0;
                    margin-right: 0;
                    opacity: 0;
                    transform: scale(0);
                    transition: all .2s ease;
                }

                .text-container {
                    opacity: 1 !important;
                    text-align: center;
                    transform: scale(1) !important;

                    .text-scroll {
                        width: max-content;

                        .text-item {
                            text-align: center;
                            padding: 0 8px;
                        }
                    }
                }
            }

            &.carousel-mode {
                .icon {
                    margin-right: 2px;
                }

                .text-container {
                    flex: 1;
                    width: auto;
                    opacity: 1;

                    .text-scroll {
                        &.scroll-4-items {
                            animation: textScroll4Items 10s infinite linear;
                        }

                        &.scroll-3-items {
                            animation: textScroll3Items 8s infinite linear;
                        }

                        &.scroll-2-items {
                            animation: textScroll2Items 6s infinite linear;
                        }

                        &.no-scroll {
                            animation: none;
                        }
                    }
                }
            }

            &.button-type {
                width: 100% !important;
                max-width: 100% !important;

                .text-item span {
                    font-size: .16rem;
                }
            }

            &.no-icon-mode {
                .text-container {
                    flex: 1;
                    width: auto;
                    opacity: 1;
                    text-align: center;

                    .text-scroll {
                        &.scroll-4-items {
                            animation: textScroll4Items 10s infinite linear;
                        }

                        &.scroll-3-items {
                            animation: textScroll3Items 8s infinite linear;
                        }

                        &.scroll-2-items {
                            animation: textScroll2Items 6s infinite linear;
                        }

                        &.no-scroll {
                            animation: none;
                        }

                        .text-item {
                            text-align: center;
                            padding: 0 8px;
                        }
                    }
                }
            }

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(135deg, rgba(255, 255, 255, .2) 0%, rgba(255, 255, 255, .1) 100%);
                border-radius: 20px;
                pointer-events: none;
            }

            &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(255, 107, 53, .4);

                .text-scroll {
                    animation-play-state: paused;
                }
            }

            &:active {
                transform: translateY(0);
            }

            .icon {
                width: 34px;
                height: 34px;
                background-image: url(./img/gift-pure.svg);
                background-size: contain;
                background-repeat: no-repeat;
                background-position: center;
                margin-right: 6px;
                flex-shrink: 0;
                z-index: 1;
                position: relative;
                opacity: 1;
            }

            .text-container {
                height: 100%;
                overflow: hidden;
                position: relative;
                z-index: 1;
                opacity: 0;
                transition: all .3s ease;
                text-align: center;
                flex: 1;
                min-width: 0;

                .text-scroll {
                    position: relative;
                    width: max-content;
                    min-width: 100%;

                    .text-item {
                        height: 40px;
                        line-height: 40px;
                        font-size: .12rem;
                        color: #fff;
                        font-weight: 600;
                        white-space: nowrap;
                        text-align: center;
                        text-shadow: 0 1px 2px rgba(0, 0, 0, .1);
                    }
                }
            }
        }
    }

    // 2项滚动动画
    @keyframes textScroll2Items {
        0% {
            transform: translateY(0);
        }

        40% {
            transform: translateY(0);
        }

        50% {
            transform: translateY(-40px);
        }

        90% {
            transform: translateY(-40px);
        }

        100% {
            transform: translateY(-80px);
        }
    }

    // 3项滚动动画
    @keyframes textScroll3Items {
        0% {
            transform: translateY(0);
        }

        30% {
            transform: translateY(0);
        }

        33% {
            transform: translateY(-40px);
        }

        63% {
            transform: translateY(-40px);
        }

        66% {
            transform: translateY(-80px);
        }

        96% {
            transform: translateY(-80px);
        }

        100% {
            transform: translateY(-120px);
        }
    }

    // 4项滚动动画（原来的textScroll）
    @keyframes textScroll4Items {
        0% {
            transform: translateY(0);
        }

        20% {
            transform: translateY(0);
        }

        25% {
            transform: translateY(-40px);
        }

        45% {
            transform: translateY(-40px);
        }

        50% {
            transform: translateY(-80px);
        }

        70% {
            transform: translateY(-80px);
        }

        75% {
            transform: translateY(-120px);
        }

        95% {
            transform: translateY(-120px);
        }

        100% {
            transform: translateY(-160px);
        }
    }

    // 保留原来的动画名称以兼容
    @keyframes textScroll {
        0% {
            transform: translateY(0);
        }

        20% {
            transform: translateY(0);
        }

        25% {
            transform: translateY(-40px);
        }

        45% {
            transform: translateY(-40px);
        }

        50% {
            transform: translateY(-80px);
        }

        70% {
            transform: translateY(-80px);
        }

        75% {
            transform: translateY(-120px);
        }

        95% {
            transform: translateY(-120px);
        }

        100% {
            transform: translateY(-160px);
        }
    }

    @keyframes expandCapsule {
        0% {
            width: 40px;
            padding: 0;
            justify-content: center;
        }

        100% {
            width: 176px;
            padding: 0 8px;
            justify-content: center;
        }
    }

    @keyframes showText {
        0% {
            opacity: 0;
            transform: scale(0);
        }

        50% {
            opacity: .6;
            transform: scale(.8);
        }

        100% {
            opacity: 1;
            transform: scale(1);
        }
    }

    @keyframes fadeOutIcon {
        0% {
            width: 18px;
            margin-right: 6px;
            opacity: 1;
            transform: scale(1);
        }

        50% {
            width: 10px;
            margin-right: 3px;
            opacity: .5;
            transform: scale(.6);
        }

        100% {
            width: 0;
            margin-right: 0;
            opacity: 0;
            transform: scale(0);
        }
    }

    .icon-2 {
        background-image: url(./img/share.svg);

        &.isFromHonorBrowser {
            margin-left: 0;
        }
    }

    .icon-3 {
        background-image: url(./img/helper.svg);

        &.isFromHonorBrowser {
            margin-left: 0;
        }
    }
}

.share-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;

    .share-panel-mask {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, .6);
    }

    .share-panel-content {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        background: #fff;
        border-radius: 24pr 24pr 0 0;
        padding: 40pr 0;
        transform: translateY(0);

        .share-options {
            display: flex;
            justify-content: space-around;
            padding: 0 40pr;
            margin-bottom: 40pr;

            .share-option {
                display: flex;
                flex-direction: column;
                align-items: center;
                cursor: pointer;

                .share-icon {
                    width: 100pr;
                    height: 100pr;
                    margin-bottom: 16pr;
                    background-size: contain;
                    background-repeat: no-repeat;
                    background-position: center;

                    &.wechat-icon {
                        background-image: url('./img/wx.svg');
                    }

                    &.copy-icon {
                        background-image: url('./img/copylink.svg');
                    }
                }

                span {
                    font-size: 28pr;
                    color: #333;
                }
            }
        }

        .share-top {
            height: 100pr;
            color: #1E1F24;
            cursor: pointer;
            position: relative;
            font-weight: 500;
            font-size: 16px;
            line-height: 16px;
            text-align: center;

            .text {
                color: #1E1F24;
                font-weight: 500;
                font-size: 16px;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
            }

            .close {
                position: absolute;
                right: 10px;
                top: 0;
                color: #1E1F24;
                font-weight: 500;
                font-size: 16px;
                height: 100%;
                display: flex;
                align-items: center;

                .img {
                    height: 100%;
                }
            }
        }
    }
}
