.is-tomas {
    @factor: 1.26;

    .commonFont(@fontSize, @lineHeight) {
        font-size: @fontSize * @factor;
    }

    .commonFont(@fontSize, @lineHeight) when (isnumber(@lineHeight)) {
        line-height: (@lineHeight * @factor);
    }

    .commonWidth(@width, @height) {
        width: @width * @factor;
        height: @height * @factor;
    }

    .live-wrapper .page-screen {

        // 直播间头部信息
        .room-head-wrapper {
            .author-info-wrapper {
                .commonFont(.14rem, .2rem);

                .author-container {
                    max-width: 2.5rem;
                    border-radius: .27rem * @factor;
                    padding-top: .03rem * @factor;
                    padding-bottom: .03rem * @factor;
                    padding-left: .03rem * @factor;
                    padding-right: .08rem * @factor;

                    .author-content {
                        margin-right: .08rem;
                    }

                    .avatar {
                        .commonWidth(.32rem, .32rem);
                    }

                    .total-users {
                        .commonFont(.1rem, .12rem);

                        font-weight: normal;
                    }

                    .follow-btn-wrapper {
                        height: .22rem * @factor;
                        padding: 0 .08rem * @factor;
                        font-size: .13rem * @factor;
                        border-radius: .14rem * @factor;
                    }
                }
            }

            .more-live-btn {
                transform: scale(@factor);
                transform-origin: bottom right;
                top: unset;
                bottom: 0;
                // height: .24rem * @factor;
                // padding: 0 .18rem * @factor 0 .04rem * @factor;
                // border-radius: .12rem * @factor 0 0 .12rem * @factor;
                // .commonFont(.12rem, .24rem);

                // &::before {
                //     .commonWidth(.16rem, .16rem);
                // }

                // &::after {
                //     top: 0.12rem;
                //     right: 0.05rem;
                //     width: 0.04rem;
                //     height: 0.04rem;
                //     border-top: 0.01667rem solid;
                //     border-left: 0.01667rem solid;
                // }
            }

            .review-info {
                height: .24rem * @factor;
                border-radius: .12rem * @factor;
                .commonFont(.12rem, .24rem);

                .review-icon {
                    border-radius: .12rem * @factor;
                }
            }
        }

        // 播放器
        .live-video-wrapper {
            .hplayer {
                .hplayer-controller {
                    height: auto;
                }

                .hplayer-mobile-play .deplayer-pause-icon,
                .hplayer-mobile-play .deplayer-play-icon {
                    .commonWidth(.5rem, .5rem);
                }

                .hplayer-icons.hplayer-active-time,
                .hplayer-icons .hplayer-time {
                    .commonFont(.13rem, false);

                    min-width: .35rem * @factor;
                }

                .hplayer-bar-wrap .hplayer-bar .hplayer-played {
                    background-color: #fff !important;
                }

                .hplayer-bar-wrap .hplayer-bar .hplayer-played .hplayer-thumb {
                    width: .15rem;
                    height: .15rem;
                    top: 50%;
                    transform: translateY(-50%);
                    margin-top: 0;
                    margin-right: -.1rem * @factor;
                }

                .hplayer-bar-wrap {
                    padding: .06rem * @factor .1rem * @factor;
                }

                .hplayer-icons .hplayer-icon.hplayer-volume-icon,
                .hplayer-icons .hplayer-icon {
                    .commonWidth(.27rem, .27rem);
                }

                .hplayer-speed .hplayer-icon {
                    .commonWidth(.32rem, .32rem);
                }

                .hplayer-press .hplayer-press-speed,
                .hplayer-speed .hplayer-speed-mask .hplayer-speed-item {
                    .commonFont(.14rem, .3rem);
                }

                .hplayer-press {
                    height: .3rem * @factor;
                }

                .hplayer-press-text {
                    .commonFont(.12rem, .3rem);
                }

                .hplayer-swipe {
                    width: 1.24rem * @factor;
                }

                .hplayer-swipe .swipe-time-text .current-time-text,
                .hplayer-swipe .swipe-time-text .total-time-text {
                    .commonFont(.12rem, .1217rem);
                }
            }

            // 竖屏
            &.is-ver {
                #video-box .hplayer-controller {
                    bottom: .88rem;
                }

                #video-box .hplayer-notice {
                    top: 1rem * @factor;
                }
            }
        }

        // 底bar
        .bottom-bar-wrapper {
            .chat-bar-wrapper {
                height: .36rem * @factor;
            }

            .chat-bar-wrapper .input-like {
                .commonFont(.14rem, .36rem);

                height: .36rem * @factor;
                padding: 0 .16rem * @factor;
                border-radius: .2rem * @factor;
                border: .01rem solid rgba(255, 255, 255, .2);
                font-weight: bold;
                color: rgba(255, 255, 255, .8);
            }

            .chat-bar-wrapper .icon-1 {
                display: none;
            }

            .chat-bar-wrapper .icon-2 {
                .commonWidth(.36rem, .36rem);

                border-radius: 50%;
                background-color: rgba(0, 0, 0, .23);
                background-image: url(https://ala-gift.cdn.bcebos.com/gift/2022-8/1660704257163/share-tomas.png);
                background-size: 60% 60%;
            }

            .bottom-author-info-wrapper {
                border-radius: .2rem * @factor;
                padding-left: .03rem * @factor;
                .commonWidth(.7rem, .36rem);
            }

            .bottom-author-info-wrapper .avatar {
                .commonWidth(.29rem, .29rem);
            }

            .follow-btn-wrapper {
                padding: .09rem * @factor .13333rem * @factor .09rem * @factor .08333rem * @factor;
            }

            .follow-btn-wrapper.is-icon-style::after {
                .commonWidth(.13rem, .13rem);
            }
        }

        // im消息
        .im-wrapper {
            bottom: 1.3rem;

            .inner-wrapper .chat-item-container .chat-item .msg {
                opacity: 1;
                font-weight: normal;
                color: rgba(255, 255, 255, .9);
                text-shadow: 0 0 .02rem * @factor rgba(0, 0, 0, .3);
            }

            .chat-msg-wrapper .inner-wrapper .chat-item-container .chat-item {
                padding: .04rem * @factor .08rem * @factor;
                border-radius: .14rem * @factor;
                .commonFont(.14rem, .22rem);
            }

            .last-msg {
                background-color: #fff;
                border: none;
                color: #F35;

                .arrow {
                    display: none;
                }

                .msg-text {
                    margin-left: 0;
                }
            }
        }

        // 推荐列表
        .live-list-wrapper {
            .inner-content {
                margin-bottom: .4rem * @factor;
            }

            .item-wrapper {
                height: auto;

                .title {
                    @lineHeight: .22rem;

                    height: @lineHeight * 2 * @factor;
                    margin: .06rem * @factor .09rem;
                    .commonFont(.16rem, @lineHeight);
                }

                .user {
                    display: none;
                }
            }
        }

        // 顶部回流
        .reflow-btn-wrapper.top-btn {
            height: .7rem;

            .icon {
                height: .44rem;
            }

            .text {
                height: .38rem;
                border-radius: .206rem;
                background: #4E6EF2;
                font-size: .17rem;
                line-height: .17rem;
                padding: 0 .136rem;
            }
        }

        // 微信端右上角分享
        .share-tip-wrapper .tip {
            .commonFont(.14rem, .16rem);
        }
    }

    // 视频直播中
    .live-wrapper.is-living-video {
        .im-wrapper {
            bottom: .84rem * @factor;
        }

        .room-head-wrapper .more-live-btn {
            top: .6rem;
            transform-origin: top right;
        }
    }

    // 视频横屏
    .live-wrapper.is-hor-video {
        .im-wrapper {
            bottom: unset;
        }

        .video-screen {
            // padding-top: 1.35rem;
            padding-top: 16.8vh;
            padding-bottom: .86rem * @factor;
        }
    }

    // 回流弹窗
    .live-wrapper .s-dialog-wrap {
        .s-dialog-header {
            .commonFont(.2rem, false);
        }

        .dialog-body {
            .commonFont(.16rem, .23rem);

            text-indent: unset;
        }

        .dialog-btn {
            .commonFont(.18rem, false);

            height: .44rem * @factor;
            border-radius: .22rem * @factor;
        }
    }
}
