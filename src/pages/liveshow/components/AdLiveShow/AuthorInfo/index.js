/*
 * @file 主播信息
 * <AUTHOR>
 * @date 2025-01-10 17:01:23
 */

import React, {useCallback, useEffect} from 'react';
import './index.less';
import cn from 'classnames';
import useTotalUsers from '@/pages/liveshow/components/common/TotalUsers';
import {env} from '@/utils/env';
import {Avatar} from '@baidu/nano-react';
import {commonShowLog} from '@/pages/liveshow/log';
import {getUrlAllParam} from '@/pages/liveshow/utils/getUrlParam';
import {sendMonitor} from '../monitor';
import {baiduMonitorUa} from '../AnyDoor/utils';

function AuthorInfo(props) {
    const totalUsers = useTotalUsers(props.totalUsers);
    const userListNum = totalUsers.length >= 6 ? 1 : 2; // 总展示位数大于等于6位时，显示一个头像，如：999.9万人
    let {
        hostAvatar,
        hostName,
        isLive,
        online_user_list,
        feedbacks,
        status,
        nid,
        screen,
        componentPos
    } = props;
    let userList = online_user_list || [];
    userList = userList.slice(0, userListNum);
    useEffect(() => {
        commonShowLog(status, 'head', nid, screen, '15264', true, componentPos);
    }, []);
    const onClick = useCallback(() => {
        const urlParams = getUrlAllParam(location.href);
        const {
            bd_vid = '',
            ch = '',
            livesource = '',
            source = '',
            room_id
        } = urlParams || {};
        sendMonitor('click', {
            level: 'author_info', info: baiduMonitorUa, value: status,
            bd_vid, root: room_id, livesource, source, ch
        });
    }, [status]);

    return (
        <div className='ad-author-info-wrapper'>
            {/* 主播信息区 */}
            <div className='author-container' onClick={onClick}>
                <Avatar className='avatar' hairline={false} src={hostAvatar} />
                <div className="author-content">
                    <p className={cn('author-name', env.isTomasSource && 'tomas-name')}>{hostName}</p>
                    {env.isTomasSource && (
                        <p className='total-users'>
                            {feedbacks}点赞 {totalUsers}人
                        </p>
                    )}
                </div>
            </div>

            {/* 用户信息区 */}
            {
                isLive
                && !env.isTomasSource
                && (
                    <div className='users-container'>
                        <div className='users-profile-list'>
                            {userList.length >= userListNum
                                && (
                                    userList.map((item, idx) => {
                                        return <Avatar key={idx} className='avatar' src={item.avatar} />;
                                    })
                                )
                            }
                        </div>
                        <p className='total-users'>{totalUsers}人</p>
                    </div>
                )
            }
        </div>
    );
}

export default AuthorInfo;
