@import (reference) '../../../assets/style-util.less';

.absolute() {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.live-video-wrapper {
    width: 100%;
    height: 100%;
    line-height: 1;
    background-color: transparent;
    position: relative;

    #video-box {
        .absolute();

        background-size: cover;
        background-position: center center;

        .hplayer-video {
            object-fit: cover;
            object-position: center center;
        }
    }

    .play-btn-mask {
        position: absolute;
        width: 200pr;
        height: 200pr;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 99;
    }

    .fullscreen-btn-mask {
        position: absolute;
        width: 100pr;
        height: 100pr;
        right: 10pr;
        z-index: 10;

        &.hor-video {
            bottom: 10pr;
        }

        &.ver-video {
            bottom: 255pr;
        }
    }

    // 初始化不展示播放器提示
    &.is-init-status {
        #video-box .hplayer-notice {
            display: none;
        }
    }

    &.is-video-loading {
        #video-box .hplayer-bezel .diplayer-loading-icon {
            display: block !important;
        }
    }

    // 播放成功后不展示video背景图片
    &:not(.is-init-status) {
        #video-box .hplayer-video-wrap {
            background: none !important;
        }
    }

    // 直播中
    &.is-living {

        // 播放器设置
        #video-box {
            .hplayer-controller-mask,
            .hplayer-controller {
                display: none;
            }
        }
    }

    // 竖屏
    &.is-ver {

        // 播放器设置
        #video-box {
            .hplayer-controller-mask {
                display: none;
            }

            .hplayer-controller {
                bottom: 228pr;
            }

            .hplayer-notice {
                bottom: unset;
                top: 1rem;
            }

            .hplayer-press {
                top: 20%;
            }
        }

        // 竖屏初始化 兼容展示横版封面图
        &.is-init-status #video-box {
            .hplayer-video-wrap {
                background-size: contain;
            }

            .hplayer-video {
                display: none;
            }
        }

        // 竖屏初始化 兼容展示竖版封面图
        &.has-ver-cover #video-box {
            .hplayer-video-wrap {
                // background-size: cover;
            }
        }
    }

    // 横屏
    &.is-hor {
        position: relative;
        width: 100%;
        height: auto;
        padding-top: 56.25%;

        &.center {
            margin-top: 50vh;
            transform: translateY(-50%);
        }
    }

    // 初始化加载错误提示
    .error-wrapper {
        font-size: 48pr;
        line-height: 90pr;
        height: 90pr;
        color: #fff;
        position: absolute;
        z-index: 100;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    &.is-hor.is-video-error {
        background-color: #000;
    }
}

.video-mask-wrapper {
    .absolute();

    background-color: rgba(0, 0, 0, .5);
    z-index: 8;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 42pr;
    color: #fff;

    .live-video-wrapper.is-hor:not(.is-no-video) & {
        background-color: transparent;
    }

    .play-wrapper {
        width: 100%;
        height: 100%;
        background-position: center center;
        background-repeat: no-repeat;
        background-size: 240pr 240pr;
        background-image: url(./img/ver_play.png);

        .live-video-wrapper.is-hor & {
            width: 264pr;
            height: 264pr;
            background-image: url(./img/hor_play.png);
        }
    }

    .group-wrapper {
        .common-flex-center-column;

        color: #fff;

        .group-title {
            margin-bottom: 40pr;
            font-weight: bolder;
            font-size: 60pr;
        }

        .group-tip {
            opacity: .6;
            font-size: 42pr;
            margin-bottom: 55pr;
        }

        .group-btn {
            min-width: 504pr;
            height: 144pr;
            border-radius: 72pr;
            font-weight: bold;
            font-size: 48pr;
            line-height: 144pr;
            color: #fff;
            text-align: center;
            padding: 0 6pr;
            box-sizing: content-box;
            .common-btn-bgcolor;
        }
    }

    .paid-wrapper {
        .common-flex-center-column;

        .paid-title {
            margin-bottom: 45pr;
            font-size: 60pr;
            color: #fff;
        }

        .paid-price-container {
            margin-bottom: 81pr;
            font-size: 42pr;
            color: #f60;

            .discount {
                margin-left: 21pr;
                text-decoration: line-through;
                color: #b8b8b8;
            }
        }

        .paid-btn {
            min-width: 504pr;
            height: 144pr;
            font-size: 48pr;
            line-height: 144pr;
            text-align: center;
            border-radius: 72pr;
            color: #fff;
            .common-btn-bgcolor;
        }
    }

    .end-no-review-wrapper {
        .common-flex-center-column;

        .mb31 {
            margin-bottom: 31pr;
        }

        .tips {
            font-size: 42pr;
            white-space: nowrap;
            color: #999;
        }

        .end-text {
            font-size: 60pr;
            color: #fff;
        }

        .watched-num {
            margin-bottom: 51pr;
        }
    }
}
