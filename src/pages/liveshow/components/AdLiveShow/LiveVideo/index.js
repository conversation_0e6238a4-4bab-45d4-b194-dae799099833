/*
 * @file 播放器
 * <AUTHOR>
 * @date 2025-01-10 17:03:05
 */
import React, {useEffect, useRef, useState} from 'react';
import cls from 'classnames';
import HPlayer from '@baidu/hplayer';
import Hls from 'hls.js';
import Chat, {MessageType} from '@/pages/liveshow/utils/chat';
import {env} from '@/utils/env';
import {isBox, isAndroid} from '@baidu/boxx/env';
import {LiveStatus, supportMcpInvoke} from '@/pages/liveshow/config/const';
import {PageStore} from '@/pages/liveshow/service/register';
import {commonShowLog} from '@/pages/liveshow/log';
import {reflow} from '@/pages/liveshow/utils/reflow';
import './index.less';
import {getUrlAllParam} from '@/pages/liveshow/utils/getUrlParam';
import {useABTest, useABTestValue} from '@/abtest-sdk';
import {sendMonitor} from '../monitor';
import {baiduMonitorUa} from '../AnyDoor/utils';
import {isMultiAutoPlay} from '../monitor/abtest';
import {adCommonShowLog} from '../monitor/ad';
import useAutoPlayWorkMonitor from '../hooks/useAutoPlayWorkMonitor';
import useRemoveClassByName from '../hooks/useRemoveClassByName';

let isClickPlay = false; // 是否点击过播放
let innerInitStatus = true; // 视频第一帧播放成功（用于初始化im消息）
let hasShowModal = false; // 是否已展示过调起弹窗
const LiveVideo = props => {
    const {
        className = '',
        status,
        liveHlsUrl = '',
        updatePageData,
        coverImg,
        screen,
        setInitStatus = () => { },
        isInitStatus, // 首帧播放成功后值为false
        handleInvoke,
        schemeParams,
        isCNY,
        hasVerCover = false,
        setIsShowDoor,
        showDoorFromData,
        isMatrixApp,
        nid,
        setHasClickShowModel
    } = props;
    innerInitStatus = isInitStatus;
    const player = useRef();
    const chat = useRef(Chat.getInstance());
    const liveEndedTimerRef = useRef();
    const timeupdateTimerRef = useRef();
    const [videoLoadErr, setVideoLoadErr] = useState(false); // 是否加载异常
    const [showLoading, setShowLoading] = useState(false); // 是否显示加载中
    const [currentTime, setCurrentTime] = useState(0); // 重新加载时记录播放时间

    const isLive = status === LiveStatus.LIVEING;
    const initVideoType = streamURL => {
        // 默认类型为 normal
        let videoType = 'normal';

        // 仅PC端用iOS UA看H5页面才用HLS插件播放
        // 1. 移动端原生支持HLS
        // 2. 规避安卓H5播放器抢占手百音频焦点问题
        // 3. iOS Safari不支持MSE
        if (!env.isAndroid && !streamURL.includes('.mp4') && Hls.isSupported()) {
            videoType = 'hls';
        }

        return videoType;
    };

    // 初始化videoType
    const videoType = initVideoType(liveHlsUrl);

    window.console.log('videoType:', videoType);

    useEffect(() => {
        // 直播按钮展现
        !isMatrixApp && commonShowLog(status, 'h5_play_show', nid, screen, '15264');
    }, []);
    const [autoPlayWork, setAutoPlayWork] = useState();
    const icon = document.querySelector('.hplayer-mobile-play');
    useEffect(() => {
        if (icon) {
            // 自动播放失败
            icon.addEventListener('click', () => {
                setHasClickShowModel(true);
            });
        }
        // 自动播放成功（3s后出来）
        if (autoPlayWork) {
            setHasClickShowModel(true);
        }
    }, [autoPlayWork, icon, setHasClickShowModel]);
    const [isPlaying, setIsPlaying] = useState(false);
    const bqtAutoPlay = useABTestValue('bqtAutoPlay');
    const autoPlay = isMultiAutoPlay(bqtAutoPlay);
    useAutoPlayWorkMonitor('hplayer-mobile-play', (display) => {
        const autoPlayWork = display === 'none';
        setAutoPlayWork(autoPlayWork);
        // sendMonitor('click', {
        //     level: 'auto_play_video_work',
        //     info: baiduMonitorUa,
        //     value: status,
        //     bd_vid, root: room_id, livesource, source, ch,
        //     actiontype: display === 'none'
        // });
    });
    useEffect(() => {
        setVideoLoadErr(false);
        initMediaPlayer(autoPlay);

        return destroyPlayer;
    }, [status, autoPlay]);
    useRemoveClassByName(autoPlayWork);
    const {userstat} = useABTest();
    useEffect(() => {
        if (isPlaying && autoPlayWork !== undefined) {
            adCommonShowLog({
                page: status, type: 'enter', value: 'entry', nid, screen, serverId: '19338', isNewVersion: true,
                action: autoPlayWork, userstat
            });
        }
    }, [autoPlayWork, isPlaying, nid, screen, status, userstat]);
    const urlParams = getUrlAllParam(location.href);
    const {
        bd_vid = '',
        ch = '',
        livesource = '',
        source = '',
        room_id
    } = urlParams || {};
    const initMediaPlayer = (startPlay = false) => {
        setVideoLoadErr(false);
        player.current = new HPlayer({
            container: document.getElementById('video-box'),
            playsinline: true, // 是否内联播放
            theme: '#ff3355', // 主体色
            lang: 'zh-cn', // 本地语言
            loop: !isLive, // 循环
            live: isLive, // 是否直播中
            speed: !isLive && !isCNY, // 倍速播放
            screenshot: false, // 不启用截屏
            hotkey: false, // 不启用热键
            preload: 'auto', // video preload
            pressSpeed: isBox && isAndroid ? 2 : 3, // android手百仅支持到2倍速
            autoplay: autoPlay,
            video: {
                muted: autoPlay,
                url: liveHlsUrl,
                bgpic: coverImg ? coverImg : '', // 手百里容器层有探测图片弹NA View，手百里不添加兜底poster了
                pic: coverImg ? coverImg : '',
                type: videoType, // hls模式应用在尽可能的规避各浏览器强制接管h5 video
                customType: {
                    hls: function (video, player) {
                        if (player.plugins.hls) {
                            player.plugins.hls.destroy();
                        }
                        const hls = new Hls();
                        hls.on(Hls.Events.MEDIA_ATTACHED, console.info.bind(console));
                        hls.on(Hls.Events.MANIFEST_PARSED, console.info.bind(console));
                        hls.on(Hls.Events.FRAG_PARSING_USERDATA, console.info.bind(console));
                        hls.on(Hls.Events.FRAG_PARSING_METADATA, console.info.bind(console));
                        hls.on(Hls.Events.ERROR, console.info.bind(console));

                        hls.attachMedia(video);

                        hls.on(Hls.Events.MEDIA_ATTACHED, () => {
                            hls.loadSource(liveHlsUrl);

                            hls.on(Hls.Events.ERROR, (event, data) => {
                                if (data.fatal) {
                                    switch (data.type) {
                                        case Hls.ErrorTypes.NETWORK_ERROR:
                                            if (data.url === liveHlsUrl) {
                                                hls.detachMedia();
                                                onError();
                                            }
                                            break;
                                        case Hls.ErrorTypes.MEDIA_ERROR:
                                            hls.swapAudioCodec();
                                            hls.recoverMediaError();
                                            break;
                                        default:
                                            break;
                                    }
                                }
                            });
                        });

                        player.plugins.hls = hls;
                        player.events.on('destroy', () => {
                            hls.destroy();
                            delete player.plugins.hls;
                        });
                    }
                }
            }
        });

        videoSectionEvent();

        player.current.on('play', () => {
            isClickPlay = true;
            setShowLoading(true);

            sendMonitor('click', {
                level: 'play_video',
                info: baiduMonitorUa,
                value: status,
                bd_vid, root: room_id, livesource, source, ch
            });


            window.console.log('play');
            // 回放 或 直播中已经展示过调起弹窗 再次执行调起
            if (hasShowModal) {
                if (supportMcpInvoke && !env.isBaiduHi) {
                    // 主板
                    handleInvoke('part');
                    return;
                }
                else if (PageStore.queryParams.source === 'lite') {
                    // lite 支持再次点击调起
                    reflow({
                        roomId: PageStore.roomId,
                        outerSchemeParams: schemeParams
                    });
                    return;
                }
            }
        });

        player.current.on('playing', () => {
            window.console.log('playing');
            setIsPlaying(true);
            sendMonitor('click', {
                level: 'playing_video',
                info: baiduMonitorUa,
                value: status,
                bd_vid, root: room_id, livesource, source, ch
            });
            clearTimeout(liveEndedTimerRef.current);
            clearTimeout(timeupdateTimerRef.current);
            setInitStatus(false);
            setShowLoading(false);
            showDoorFromData && setIsShowDoor(true);
        });

        player.current.on('waiting', () => {
            setShowLoading(true);
        });

        player.current.on('pause', () => {
            clearTimeout(liveEndedTimerRef.current);
            clearTimeout(timeupdateTimerRef.current);
            setShowLoading(false);
        });

        player.current.on('ended', () => {
            clearTimeout(liveEndedTimerRef.current);
            clearTimeout(timeupdateTimerRef.current);
            !isLive && setCurrentTime(0);
        });

        player.current.on('error', onError);

        player.current.on('timeupdate', () => {
            setShowLoading(false);
            clearTimeout(liveEndedTimerRef.current);
            clearTimeout(timeupdateTimerRef.current);

            if (!innerInitStatus) {
                // 直播中状态 && 播放过程中8s加载不出资源，请求接口获取直播结束状态
                if (isLive) {
                    liveEndedTimerRef.current = setTimeout(() => {
                        updatePageData && updatePageData();
                    }, 8000);
                }

                // 15s加载不出资源，展示网络异常
                timeupdateTimerRef.current = setTimeout(() => {
                    setVideoLoadErr(true);
                }, 15000);
            }
        });

        player.current.on('loadstart', () => {
            if (player.current) {
                startPlay && player.current.play();
                if (!isLive && !isClickPlay) {
                    // 回放视频，重新加载时设置上一次播放位置
                    player.current.video.currentTime = currentTime;
                }
            }
        });

        player.current.on('ratechange', () => {
            window.console.log('ratechange');
            // 切换倍速
            if (supportMcpInvoke) {
                handleInvoke('part');
            }
        });

        player.current.on('fullscreen', () => {
            window.console.log('fullscreen');
            // 切换全屏
            if (supportMcpInvoke) {
                handleInvoke('part');
            }
        });

        if (isLive) {
            // 直播中 监听im消息切流状态 然后重新设置播放链接
            chat.current.on(MessageType.LIVE_CHANGE_FLOW, live_hls_url => {
                if (live_hls_url && player.current) {
                    player.current.switchVideo({url: live_hls_url});
                }
            });
        }
    };

    // 视频切片设置
    const videoSectionEvent = () => {
        // 是否需要视频切片，start有可能是零
        const {start: videoStartTime, end: videoEndTime} = PageStore.queryParams;
        const isNeedSetVideoStarTime = !isLive && +videoStartTime >= 0 && +videoEndTime >= 0;

        if (isNeedSetVideoStarTime) {
            // 切片是否播放完成
            let isEndedBySection = false;

            player.current.on('loadeddata', () => {
                // 初始化 设置视频切片开始时间
                if (+videoStartTime > 0) {
                    player.current.video.currentTime = +videoStartTime;
                }
            });
            player.current.on('timeupdate', () => {
                // 监听视频切片结束时间 自动暂停
                if (!isEndedBySection && player.current.video.currentTime >= +videoEndTime) {
                    player.current.pause();
                    isEndedBySection = true;
                }
            });
        }
    };

    const onError = () => {
        if (isClickPlay) {
            setVideoLoadErr(true);
            setShowLoading(false);
        }
    };

    // 销毁播放器及im监听
    const destroyPlayer = () => {
        clearTimeout(liveEndedTimerRef.current);
        clearTimeout(timeupdateTimerRef.current);

        isClickPlay = false;
        setInitStatus(true);
        setShowLoading(false);

        if (player.current) {
            !isLive && setCurrentTime(player.current.video.currentTime);
            player.current.destroy();
            player.current = null;
        }

        if (chat.current) {
            chat.current.off(MessageType.LIVE_CHANGE_FLOW);
        }
    };
    const getRootClass = cls({
        'live-video-wrapper': true,
        [className]: true,
        'is-hor': +screen === 1,
        'is-ver': +screen === 0,
        'is-living': isLive,
        'is-init-status': isInitStatus,
        'is-video-error': videoLoadErr,
        'is-video-loading': showLoading,
        'has-ver-cover': hasVerCover
    });
    return (
        <React.Fragment>
            <div className={getRootClass}>
                {/* 播放器 */}
                <div
                    id='video-box'
                ></div>
            </div>
        </React.Fragment>
    );
};

export default LiveVideo;
