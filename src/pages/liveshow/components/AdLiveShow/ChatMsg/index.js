import './index.less';
import cls from 'classnames';
import React, {useEffect, useRef, useState} from 'react';
import Chat, {MessageType} from '@/pages/liveshow/utils/chat';
import emoticon from '@/pages/liveshow/utils/emoticon';
import {tinyScrollTo} from '@/utils/animation';
import {useAdLiveShowContext} from '../context';
import {enhanceImAdd, useEnhanceImShow} from '../hooks/useEnhanceImMonitor';
import {useEverInViewport} from '../hooks/useEverInViewport';
import {isMultiMiniScreen, shouldAnydoorOptimize} from '../monitor/abtest';

// 初始化加载im消息 1初始化 2im区域正在滚动 0初始化动画完成
let isInitScroll = 1;
// 是否展示悬浮im消息提示
let isShowLastMsg = false;

const showName = (name, hideName) => {
    if (!name) {
        return name;
    }

    if (hideName) {
        return `${name.slice(0, 1)}***`;
    }
    return name;
};

function UserName({username, q2c}) {
    return !!username && <span className='user-name'>{username}：{q2c ? '@我' : null}</span>;
}
function ChatItem({item, nid, status}) {
    let {username, content, q2c} = item;

    let props = {
        dangerouslySetInnerHTML: {
            __html: emoticon(content)
        }
    };
    const isContentIm = q2c === 'contentIm';

    const [ref, inViewport] = useEverInViewport();

    const {chatImDataResult = {}} = useAdLiveShowContext();
    const {
        openFlag,
        failCode
    } = chatImDataResult;

    useEnhanceImShow({
        inViewport, failCode,
        status, nid, screen,
        openFlag
    });

    return (
        <div className={cls('chat-item-container', {
            'q2c-chat-item-container': q2c,
            'q2c-back-bg': isContentIm
        })}
        >
            {q2c ? <div className='chat-item'>
                {isContentIm ? <>
                    <span className='author' ref={ref}>
                        主播
                    </span>
                    <UserName username={username} q2c={q2c} />
                </> : <UserName username={username} />}
                <span className='msg' {...props} />
            </div> : <div className='chat-item'>
                <UserName username={username} />
                <span className='msg' {...props} />
            </div>}
        </div>
    );
}

function ChatMsg(props) {
    const {
        nid, status, msgHlsUrl, hideName, chat_mcast_id,
        isLogin, isCNY, likeAction, chatList, setChatList, host
    } = props;
    const authorName = host.name ?? '';
    const $msgBox = useRef();
    const $msgWrapper = useRef();
    const $chatList = useRef();
    const chat = useRef(Chat.getInstance());
    const [lastMsg, setLastMsg] = useState('');
    const {chatImDataResult = {}} = useAdLiveShowContext();

    const {
        openFlag, // false-代表关闭，true-代表打开 关闭则屏蔽样式不展示（query和contentim皆不展示）
        query, // 为空或length=0，则评论区不展示用户query，仅展示contentIm
        contentIm, // 为空或length=0，则屏蔽样式不展示
        failCode
    } = chatImDataResult;

    const openQ2C = openFlag && !!contentIm;
    useEffect(() => {
        isInitScroll = 1;
        isShowLastMsg = false;

        initChat(openQ2C, query, contentIm);
    }, [query, openQ2C, contentIm]);

    useEffect(() => {
        if (!chatList.length) {
            return;
        }

        // 当前展示位置非im底部 悬浮提示最新消息
        if (isShowLastMsg) {
            let newMsg = chatList[chatList.length - 1];
            if (newMsg && newMsg.content) {
                setLastMsg(newMsg.content);
            }
            return;
        }

        // 正在执行初始化滚动 禁止其他操作
        if (isInitScroll === 2) {
            return;
        }

        // 当前展示位置是im底部 直接滚动展示新消息

        const lastMessage = chatList[chatList.length - 1];
        const isQ2C = lastMessage && isQ2CMessage(lastMessage);

        scrollToBottom(isInitScroll ? 'shake' : '', isQ2C);

        // 仅第一次加载im消息时执行
        if (isInitScroll) {
            isInitScroll = 2;
            initAddEvent();
        }
    }, [chatList]);

    const isQ2CMessage = msg => !!msg.q2c;
    const initChat = (openQ2C, query, contentIm) => {
        chat.current.start({
            m3u8Link: msgHlsUrl,
            status: status,
            addDefaultMsg: true,
            chatID: chat_mcast_id,
            isLogin
        });

        chat.current.on(MessageType.MESSAGE, _list => {
            const q2cMessageItem = [
                {
                    msgId: `q2c-content-${Date.now()}`,
                    name: authorName,
                    word: contentIm,
                    avatarSrc: '',
                    publishTime: new Date().toISOString(),
                    responseUser: null,
                    q2c: 'contentIm'
                }];
            if (query) {
                q2cMessageItem.unshift({
                    msgId: `q2c-query-${Date.now()}`,
                    name: '我',
                    word: query,
                    avatarSrc: '',
                    publishTime: new Date().toISOString(),
                    responseUser: null,
                    q2c: 'query'
                });
            }
            const list = openQ2C ? [..._list, ...q2cMessageItem] : _list;
            enhanceImAdd({nid, screen, failCode, status});
            const newL = list.map(item => {
                return {
                    msgId: item.msgId,
                    avatarSrc: item.portrait,
                    username: showName(item.name, item?.q2c ? false : hideName),
                    publishTime: item.timestr,
                    content: item.word,
                    responseUser: item.responseUser,
                    q2c: item.q2c
                };
            });
            setChatList(newL);
        });
    };
    useEffect(() => {
        if (likeAction) {
            const newLikeMessage = {
                msgId: `like-${Date.now()}`,
                username: '',
                content: '你点赞了直播间',
                avatarSrc: '',
                publishTime: new Date().toISOString(),
                responseUser: null
            };
            setChatList(prev => [...prev, newLikeMessage]);
        }
    }, [likeAction]);

    const scrollToBottom = (type, isQ2C = false) => {
        const element = $msgBox.current;
        isShowLastMsg = false;
        setLastMsg('');

        if (element) {
            const {scrollHeight, clientHeight} = element;
            let scrollDistance;

            if (isQ2C) {
                // Q2C场景：让第一条Q2C消息出现在距离容器顶部20px的位置
                const firstQ2CIndex = chatList.findIndex(msg => msg.q2c);

                if (firstQ2CIndex !== -1) {
                    // 目标位置：让第一条Q2C消息距离容器顶部20px
                    const targetPosition = 20;

                    // 尝试通过DOM获取更精确的位置
                    const chatItems = element.querySelectorAll('.chat-item-container');

                    if (chatItems[firstQ2CIndex] && chatItems[firstQ2CIndex].offsetTop) {
                        // 使用实际DOM元素的位置
                        const firstQ2CElement = chatItems[firstQ2CIndex];
                        const elementTop = firstQ2CElement.offsetTop;

                        // 计算需要滚动的距离
                        scrollDistance = elementTop - targetPosition;
                    }
                    else {
                        // 如果DOM元素还没有渲染或位置不准确，使用估算方法
                        // 估算每条消息的高度（包括margin-bottom）
                        // 根据CSS，每条消息大约是 line-height(66pr) + padding(24pr) + margin-bottom(12pr)
                        // 转换为px大约是 66*0.5 + 24*0.5 + 12*0.5 = 51px 左右
                        const estimatedMessageHeight = 51;

                        // chat-list的margin-top是150px
                        const chatListMarginTop = 150;

                        // 计算第一条Q2C消息的大概位置
                        const firstQ2CPosition = chatListMarginTop + (firstQ2CIndex * estimatedMessageHeight);

                        // 计算需要滚动的距离
                        scrollDistance = firstQ2CPosition - targetPosition;
                    }

                    // 确保滚动距离不会超出范围，且不小于0
                    scrollDistance = Math.max(0, Math.min(scrollDistance, scrollHeight - clientHeight));
                }
                else {
                    // 如果没有找到Q2C消息，使用默认滚动到底部
                    scrollDistance = scrollHeight - clientHeight;
                }
            }
            else {
                // 非Q2C场景：正常滚动到底部
                scrollDistance = scrollHeight - clientHeight;
            }

            if (type === 'shake') {
                const duration = Math.min(2000, 100 * Math.max(4, chatList.length));
                tinyScrollTo(element, 'scrollTop', scrollDistance, duration);

                setTimeout(() => {
                    if ($chatList.current) {
                        $chatList.current.style.marginTop = 0;
                    }
                    isInitScroll = 0;
                }, duration + 50);
                return;
            }

            // 非初始化滚动效果
            tinyScrollTo(element, 'scrollTop', scrollDistance, 200);
        }
    };

    const initAddEvent = () => {
        const element = $msgWrapper.current;
        if (element) {
            let startY = -1;
            let endY = -1;

            element.addEventListener('touchstart', e => {
                e.stopPropagation();

                if (e.touches[0]) {
                    startY = e.touches[0].screenY;
                }
            }, {passive: false});

            element.addEventListener('touchmove', e => {
                e.stopPropagation();

                if (e.touches[0]) {
                    endY = e.touches[0].screenY;
                }

                const isPull = startY < endY;
                const {scrollHeight, clientHeight, scrollTop} = $msgBox.current;
                const scrollDistance = scrollHeight - clientHeight;

                if (isPull && scrollTop === 0
                    || !isPull && scrollTop === scrollDistance) {
                    e.preventDefault();
                }
            }, {passive: false});

            element.addEventListener('touchend', e => {
                e.stopPropagation();

                const {scrollHeight, clientHeight, scrollTop} = $msgBox.current;
                if (scrollTop + 10 >= scrollHeight - clientHeight) {
                    isShowLastMsg = false;
                    setLastMsg('');
                }
                else {
                    isShowLastMsg = true;
                }
            }, {passive: false});
        }
    };

    return (
        <div
            className={cls('ad-chat-msg-wrapper', {
                'ad-chat-msg-wrapper-cny': isCNY
            })}
            ref={$msgWrapper}
        >
            <div
                className='inner-wrapper'
                ref={$msgBox}
            >
                <div
                    className="chat-list"
                    ref={$chatList}
                >
                    {
                        chatList.map((item, idx) => (
                            <ChatItem key={item.msgId || idx} item={item} nid={nid} status={status} />
                        ))
                    }
                </div>
            </div>

            {/* 新消息提示 点击滚动到im底部 */}
            {lastMsg && (
                <div
                    className='last-msg'
                    onClick={() => scrollToBottom('', false)}
                >
                    <div className='arrow' />
                    <div className='msg-text'>{lastMsg}</div>
                </div>
            )}
        </div>
    );
}

export default ChatMsg;
