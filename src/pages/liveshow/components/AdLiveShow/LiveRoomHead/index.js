import './index.less';
import React, {useEffect} from 'react';
import cls from 'classnames';
import {huaweiFeed} from '@/pages/liveshow/utils/ignoreReflow';
import {LiveStatus, supportMcpInvoke} from '@/pages/liveshow/config/const';
import {commonShowLog, commonClickLog} from '@/pages/liveshow/log';
import AuthorInfo from '../AuthorInfo';

function LiveRoomInfo(props) {
    let {
        className = '',
        updatePageData,
        status,
        hasReflowBtn,
        showRecMoreBtn,
        componentPos,
        showAuthorInfo = true,
        handleInvoke,
        isMatrixApp,
        nid
    } = props;

    const isLive = status === LiveStatus.LIVEING;
    const getRootClass = cls('room-head-wrapper', {
        'has-author-info': !huaweiFeed,
        'has-reflow-btn': hasReflowBtn,
        'has-review-info': !isLive,
        'has-more-live-btn': showRecMoreBtn,
        [className]: true
    });
    useEffect(() => {
        // 回放标签展现打点
        commonShowLog(status, 'playback_btn_show', nid, screen, '15264');
    }, []);

    const handlePlayback = e => {
        e.preventDefault();
        e.stopPropagation();
        supportMcpInvoke && commonClickLog(status, 'playback_btn_click', nid, screen, '15264');
        supportMcpInvoke && handleInvoke('part');
    };

    return (
        <div className={getRootClass}>
            <div className="top-wrapper">
                {!huaweiFeed && showAuthorInfo && (
                    <AuthorInfo
                        {...props}
                        isLive={isLive}
                        updatePageData={updatePageData}
                        componentPos={componentPos}
                        handleInvoke={handleInvoke}
                        isMatrixApp={isMatrixApp}
                    />
                )}
            </div>

            {!isLive && (
                <div className='review-info' onClick={handlePlayback}>
                    <p className="review-icon">回放</p>
                    <p className="review-tip">直播已结束，正在播放回放</p>
                </div>
            )}
        </div>
    );
}

export default LiveRoomInfo;
