@import (reference) '../../../assets/style-util.less';

.room-head-wrapper {
    width: 100%;
    position: relative;
    box-sizing: border-box;
    color: #fff;
    padding: 36pr 30pr 0;

    .top-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24pr;
        position: relative;

        // 直播信息
        .author-info-wrapper {
            flex: 1;
        }

        // 打开app按钮
        .reflow-btn-wrapper.auther-reflow-btn {
            flex: none;
            margin-left: 18pr;
        }
    }

    // 回放信息
    .review-info {
        height: 72pr;
        float: left;
        display: inline-flex;
        align-items: center;
        font-size: 36pr;
        line-height: 72pr;
        .bgMask(.23, 36pr);

        position: relative;

        .review-icon {
            height: 100%;
            background: #768CAE;
            border-radius: 36pr;
            padding: 0 18pr 0 15pr;
            display: flex;
            align-items: center;

            &::before {
                content: '';
                display: block;
                width: 48pr;
                height: 42pr;
                background-image: url(./imgs/review.png);
                background-size: 100% 100%;
                margin-right: 6pr;
            }
        }

        .review-tip {
            padding: 0 24pr;
        }
    }

    // 更多直播按钮
    .more-live-btn {
        height: 72pr;
        padding: 0 54pr 0 12pr;
        display: flex;
        align-items: center;
        position: absolute;
        top: 180pr;
        right: 0;
        font-size: 36pr;
        line-height: 72pr;
        .bgMask(.23, 36pr 0 0 36pr);

        &::before {
            content: '';
            display: block;
            width: 48pr;
            height: 48pr;
            background-image: url(./imgs/more-live-icon.png);
            background-size: 100% 100%;
            margin-right: 12pr;
        }

        &::after {
            content: '';
            display: block;
            position: absolute;
            top: 36pr;
            right: 15pr;
            .arrow(12pr);
        }
    }
}
