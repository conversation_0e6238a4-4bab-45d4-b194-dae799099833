/*
 * @file iframe相关逻辑hook
 * <AUTHOR>
 * @date 2025-03-15 10:00:00
 */
import {useState} from 'react';

export const useIframe = () => {
    // AB测试相关
    const isIframe = false;

    // iframe状态管理
    const [showIframe, setShowIframe] = useState(false);
    const [isClosing, setIsClosing] = useState(false);
    const [iframeUrl, setIframeUrl] = useState('');

    // 关闭iframe的函数，带动画效果
    const closeIframe = () => {
        setIsClosing(true);
        // 等待动画完成后再隐藏iframe
        setTimeout(() => {
            setShowIframe(false);
            setIsClosing(false);
        }, 250); // 与动画时长保持一致
    };

    // 打开iframe的函数
    const openIframe = (url) => {
        setIframeUrl(url);
        setShowIframe(true);
    };

    return {
        // 状态
        isIframe,
        showIframe,
        isClosing,
        iframeUrl,

        // 方法
        setShowIframe,
        setIframeUrl,
        closeIframe,
        openIframe
    };
};
