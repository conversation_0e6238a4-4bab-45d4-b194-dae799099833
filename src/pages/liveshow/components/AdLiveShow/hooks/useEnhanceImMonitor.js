/*
 * @file 增强IM打点上报
 * <AUTHOR>
 * @date 2025-07-21 20:44:43
 */

import {useEffect} from 'react';
import {adCommonShowLog} from '../monitor/ad';


const imStatusMap = {
    // request_over 请求qc结束
    request_over: 'request_over',
    // add 加到im消息队列
    add: 'add',
    // show 增强im曝光
    show: 'show'
};

function createEnhanceImLogger(type) {
    return ({nid, status, failCode, screen}) => adCommonShowLog({
        page: status, type,
        value: 'entry',
        nid, screen, serverId: '19636', isNewVersion: true,
        failCode
    });
}
export const enhanceImRequestOver = createEnhanceImLogger(imStatusMap.request_over);
export const enhanceImAdd = createEnhanceImLogger(imStatusMap.add);
export const enhanceImShow = createEnhanceImLogger(imStatusMap.show);

export function useEnhanceImRequestOver({
    loading, failCode,
    status, nid, screen,
    openFlag
}) {
    useEffect(() => {
        if (!loading) {
            enhanceImRequestOver({nid, screen, failCode, status});
        }
    }, [failCode, openFlag, status, loading]);
}

export function useEnhanceImShow({
    failCode,
    status, nid, screen,
    openFlag, inViewport
}) {
    useEffect(() => {
        if (inViewport) {
            enhanceImShow({nid, screen, failCode, status});
        }
    }, [failCode, openFlag, status, inViewport]);
}