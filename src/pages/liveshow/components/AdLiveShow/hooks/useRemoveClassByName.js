import {useEffect} from 'react';

/**
 * 移除页面上所有具有指定 className 的元素的该 class
 * @param className - 不带点号的 class 名称，比如 'active'
 */
function useRemoveClassByName(needRemove) {
    const c = 'diplayer-loading-icon';
    useEffect(() => {
        try {
            setTimeout(() => {
                const elements = document.querySelector(`.${c}`);
                if (needRemove === true && elements) {
                    elements.remove();
                }
            }, 5000);
        }
        catch (error) {
        }
    }, [needRemove]);
}

export default useRemoveClassByName;
