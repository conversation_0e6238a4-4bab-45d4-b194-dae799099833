/*
 * @file 自动播放工作监控
 * <AUTHOR>
 * @date 2025-02-11 16:00:00
 */
import {useEffect} from 'react';

function useAutoPlayWorkMonitor(className, callback) {
    useEffect(() => {
        let timeout;

        const updateDisplay = () => {
            const element = document.querySelector(`.${className}`);
            if (element) {
                const newDisplay = getComputedStyle(element).display;
                callback(newDisplay);
            }
        };

        const onLoad = () => {
            timeout = setTimeout(updateDisplay, 3000);
        };

        if (document.readyState === 'complete') {
            onLoad();
        }
        else {
            window.addEventListener('load', onLoad);
        }

        return () => {
            window.removeEventListener('load', onLoad);
            clearTimeout(timeout);
        };
    }, [className]);

}

export default useAutoPlayWorkMonitor;
