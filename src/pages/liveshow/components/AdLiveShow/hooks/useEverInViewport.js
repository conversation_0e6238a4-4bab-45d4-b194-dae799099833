import {useRef, useState, useEffect} from 'react';
import {useInViewport} from 'ahooks';

export function useEverInViewport() {
    const ref = useRef(null);
    const [inViewport] = useInViewport(ref);
    const [everInViewport, setEverInViewport] = useState(false);

    useEffect(() => {
        if (inViewport && !everInViewport) {
            setEverInViewport(true);
        }
    }, [inViewport, everInViewport]);

    return [ref, everInViewport];
}
