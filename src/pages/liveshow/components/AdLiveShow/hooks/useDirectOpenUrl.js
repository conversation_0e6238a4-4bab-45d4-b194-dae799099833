/*
 * @file 直接打开URL逻辑hook
 * <AUTHOR>
 * @date 2025-03-15 10:00:00
 */
import {useEffect} from 'react';
import {isWeChatUrl} from '@/utils/wxLaunch';
import {shouldDirectOpenUrl} from '../monitor/abtest';

/**
 * 直接打开URL逻辑hook
 * 当满足条件时直接跳转到指定URL
 * @param {boolean} isShowDoor - 是否显示门
 * @param {string} anyMountCardUrlType - 任意门卡片URL类型
 * @param {string} anyMountCardUrlH5 - 任意门卡片H5 URL
 */
export const useDirectOpenUrl = (isShowDoor, anyMountCardUrlType, anyMountCardUrlH5) => {
    useEffect(() => {
        if (isShowDoor && !isWeChatUrl(anyMountCardUrlType) && shouldDirectOpenUrl && anyMountCardUrlH5) {
            window.location.href = anyMountCardUrlH5;
        }
    }, [anyMountCardUrlH5, anyMountCardUrlType, isShowDoor]);
};
