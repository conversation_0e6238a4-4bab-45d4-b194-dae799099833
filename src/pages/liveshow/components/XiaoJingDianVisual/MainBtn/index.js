/**
 * @file  MainBtn 主按钮
 * <AUTHOR>
 */

import React, {useCallback, useEffect, useState} from 'react';
import cls from 'classnames';
import {Button} from '@baidu/nano-react';
import {TemplateEnum, logTxtMap, jdlogTxtMap} from '@/pages/liveshow/config/const';
import {getAppSchemeHead} from '@/pages/liveshow/utils/getUrlParam';
import {createMcpWxTag} from '@baidu/mcp-sdk';
import {matrixAppSchemeHeaderList} from '@/pages/liveshow/utils/reflow';
import {subscribe} from '@/pages/liveshow/service/api';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import {env} from '@/utils/env';
import {xiaoJingDianClickLog} from '@/pages/liveshow/log';
import style from './index.module.less';

const cx = cls.bind(style);

// 按钮文案
const btnTxtMap = {
    '0': '立即观看',
    '2': '直播结束',
    '3': '精彩回顾'
};

const previewImg = 'https://mpics.bdstatic.com/qm/202404/pic_EKTzeL_1712654474.gif';
const previewedImg = 'https://mpics.bdstatic.com/qm/202404/pic_ki9lBm_1712633487.png';

// 端内协议头
const appSchemeHead = getAppSchemeHead();

// 回流矩阵场景
const isMatrixApp = matrixAppSchemeHeaderList.includes(appSchemeHead);

// const source = PageStore.queryParams.source;

const MainBtn = ({data, refresh, handleInvoke, jumpToNA, entranceAPP, invokeInfo}) => {
    const {
        template,
        status,
        user_info, // 用户信息
        appointment_status = 0
    } = data;

    const isPreview = template === TemplateEnum.Preview; // 是否为预约直播

    const isLogin = user_info.is_login; // 是否登录

    const [btnTxt, setBtnTxt] = useState(''); // 按钮文案
    const [img, setImg] = useState(+appointment_status === 1 ? previewedImg : previewImg); // 背景图片

    useEffect(() => {
        let btnTxt = '';
        if (isPreview) {
            btnTxt = +appointment_status === 1 ? '已预约' : '立即预约';
            +appointment_status === 1 ? setImg(previewedImg) : setImg(previewImg);
        }
        else {
            btnTxt = btnTxtMap[status] || '去看回放';
        }
        setBtnTxt(btnTxt);
    }, [appointment_status, template, status]);

    useEffect(() => {
        window.console.log('main-btn', invokeInfo);
        if (env.isAndroid && env.isWechat && !isMatrixApp && invokeInfo && invokeInfo.action_rule) {
            const {action_rule, log_id} = invokeInfo;
            // 创建微信开放标签
            const btnDom = document.querySelector('.main-btn-jd');
            btnDom && createMcpWxTag({targetDom: btnDom}, 'pos_part', action_rule.pos_part[0], log_id);
        }
    }, [invokeInfo]);

    const handleSubEnd = (success, requestStatus) => {
        const sucToast = requestStatus ? '预约成功' : '取消预约成功';
        const failToast = requestStatus ? '预约失败' : '取消预约失败';
        boxjsUtils.showToast(success ? sucToast : failToast);
        // 更新按钮文案
        success && setBtnTxt(requestStatus ? '已预约' : '立即预约');
        success && setImg(requestStatus ? previewedImg : previewImg);

        // 通知开启系统通知的弹窗
        if (requestStatus === 1 && !env.isHaokan) {
            setTimeout(() => {
                boxjsUtils.systemNotify('live_sub');
            }, 500);
        }

        if (success) {
            refresh && refresh();
        }
    };

    const handleClk = useCallback(e => {
        if (e) {
            e.preventDefault();
            e.stopPropagation();
        }

        xiaoJingDianClickLog('h5_subscribe', 'subscribe_btn', '15263', logTxtMap[status], jdlogTxtMap[status]);
        if (entranceAPP) {
            if (isPreview) {
                if (isLogin) {
                    const requestStatus = +appointment_status === 0 ? 1 : 0;
                    subscribe(requestStatus)
                        .then((success) => handleSubEnd(success, requestStatus))
                        .catch(() => handleSubEnd(false, requestStatus));
                }
                else {
                    // 没有登录去登录,登录成功后刷新数据
                    boxjsUtils.login().then(() => {
                        // 一定延时后调用(解决安卓11.20这个逻辑运行不稳定的问题)
                        window.setTimeout(() => {
                            refresh();
                        }, 500);
                    });
                }
            }
            else {
                jumpToNA();
            }
        }
        else {
            // 其他场景调起
            handleInvoke();
        }
    }, [appointment_status, isLogin, isPreview]);
    return (
        <div
            className={cx('main-btn-jd', env.isTomas && 'tomas')}
            style={{backgroundImage: `url(${img})`}}
        >
            <div
                className="main-txt"
                onClick={handleClk}
            >
                {btnTxt}
            </div>
        </div>
    );
};

export default MainBtn;
