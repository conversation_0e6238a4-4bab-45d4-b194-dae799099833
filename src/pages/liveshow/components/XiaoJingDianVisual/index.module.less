.xiao-jingdian-visual {
    position: relative;
    width: 100%;
    min-height: 100%;
    background: url('https://mpics.bdstatic.com/qm/202404/pic_MZIcV3_1712635321.jpg') no-repeat;
    background-size: cover;

    .cover {
        position: absolute;
        height: 2.0133rem;
        top: 5.78rem;

        img {
            height: 100%;
            width: 100%;
        }
    }

    .title-jd {
        position: relative;
        height: 1.92rem;
        width: 100%;
        margin: auto;

        img {
            height: 100%;
            width: auto;
        }
    }

    .button-jd {
        display: flex;
        position: relative;
        justify-content: center;
        align-items: center;
        flex-wrap: nowrap;
        margin-top: .1466rem;
        overflow: hidden;
        padding-bottom: .09rem;
        z-index: 2;

        &.no-countdown {
            margin-top: 4.67rem;
        }
    }

    .program {
        position: relative;
        height: 4.5866rem;
        padding-bottom: .03rem;
        z-index: 2;

        img {
            height: 100%;
            width: 100%;
        }
    }
}
