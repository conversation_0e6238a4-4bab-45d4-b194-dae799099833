@import '~@baidu/nano-theme/index.less';

@font-face {
    font-family: 'baidunumber-Medium';
    src: url(../../../assets/font/baidunumber-Medium.ttf) format('truetype');
}
.theme-root() !important;

.count-down-new-jd {
    position: relative;
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 4.4rem;

    .count-content-jd {
        display: flex;
        flex-wrap: nowrap;
        margin-top: .08rem;

        .count-title-jd {
            font-family: PingFang SC;
            font-size: .143rem;
            font-weight: 500;
            line-height: .143rem;
            text-align: center;
            color: #FFF;
            margin-top: .13rem;
            margin-right: .066rem;
        }

        .countDownTimeNew {
            width: .238rem;
            height: .3713rem;
            border-radius: .0464rem;
            background: #C72400;
            color: #FFF;
            font-family: "baidunumber-Medium";
            font-size: .26rem;
            font-weight: 500;
            line-height: .3713rem;
            margin-left: .0305rem;
            text-align: center;

            &.is-android {
                line-height: normal;
                padding-top: .05rem;
            }

            &.tomasTime {
                line-height: .3rem !important;
            }
        }

        .countDownUnitNew {
            color: #FFF;
            text-align: center;
            font-family: "PingFang SC";
            font-size: .14rem;
            font-style: normal;
            font-weight: 500;
            line-height: .1933rem; /* 171.429% */
            margin-top: .1rem;
            margin-left: .0533rem;
            margin-right: .0533rem;
        }
    }
}
