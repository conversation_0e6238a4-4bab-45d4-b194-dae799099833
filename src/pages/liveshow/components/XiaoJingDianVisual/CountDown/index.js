/**
 * @file 倒计时组件
 * <AUTHOR> on 2024-02-01 20:59:32.
 */

import React, {useCallback, useEffect, useRef, useState} from 'react';
import {getEndStatus} from '@/pages/liveshow/service/api';
import {env} from '@/utils/env';
import cls from 'classnames';
import style from './index.module.less';

const minuteSecond = 60;
const hourSecond = minuteSecond * 60;
const daySecond = hourSecond * 24;
const cx = cls.bind(style);
const titleImg = 'https://mpics.bdstatic.com/qm/202404/pic_UTWi1y_1712645832.png';

function CountDownTimeStr({timestamp = 0}) {
    if (timestamp === null) {
        return '---';
    }
    if (timestamp < 0) {
        timestamp = 0;
    }
    const timeArray = [
        {
            unit: '天',
            secondCount: daySecond
        },
        {
            unit: '时',
            secondCount: hourSecond
        },
        {
            unit: '分',
            secondCount: minuteSecond
        },
        {
            unit: '秒',
            secondCount: 1
        }
    ];
    let time = Math.ceil(timestamp);
    let resultArray = timeArray.map((timeItem) => {
        const count = Math.floor(time / timeItem.secondCount);
        time -= count * timeItem.secondCount;
        return {
            count: count.toString().length < 2 ? `0${count}` : count,
            ...timeItem
        };
    });
    return resultArray.reduce((pre, result, index) => {
        if (pre === '') {
            if (result.count > 0 || result.unit === '秒') {
                return [
                    <span
                        className={cx(style.countDownTimeNew, env.isAndroid && 'is-android',
                        env.isTomas && 'tomasTime')} key={`${index}-1`}
                     >
                        {result.count.toString()[0]}
                    </span>,
                    <span
                        className={cx(style.countDownTimeNew, env.isAndroid && 'is-android',
                        env.isTomas && 'tomasTime')} key={`${index}-3`}
                    >
                        {result.count.toString()[1]}
                    </span>,
                    <span className={style.countDownUnitNew} key={`${index}-2`}>
                        {result.unit}
                    </span>
                ];
            }
            return pre;
        }
        return pre.concat([
            [
                <span
                    className={cx(style.countDownTimeNew, env.isAndroid && 'is-android',
                    env.isTomas && 'tomasTime')} key={`${index}-1`}
                >
                    {result.count.toString()[0]}
                </span>,
                <span
                    className={cx(style.countDownTimeNew, env.isAndroid && 'is-android',
                    env.isTomas && 'tomasTime')} key={`${index}-3`}
                >
                    {result.count.toString()[1]}
                </span>,
                <span className={style.countDownUnitNew} key={`${index}-2`}>
                    {result.unit}
                </span>
            ]
        ]);
    }, '');
}

function CountDown(props) {
    const [countdown, setCountdown] = useState(() => {
        return +props.countdown < 0 ? 0 : +props.countdown;
    });
    const [noStart, setNoStart] = useState(false);
    const timeOut = useRef(null);
    const update = useCallback(() => {
        return getEndStatus().then((data) => {
            // -1 表示直播还未开始,继续倒计时
            const status = +data.status;
            const count = +data.countdown < 0 ? 0 : +data.countdown;
            const show = (status === -1 && count > 0);
            props.setShowLiveBtn && props.setShowLiveBtn(!show);
            setNoStart(show);
            if (status === -1) {
                setCountdown(count);
            }
            else {
                // 主播开始,刷新数据前往直播间
                props.refresh && props.refresh();
            }
        });
    }, [props]);

    useEffect(() => {
        update();
    }, []);

    useEffect(() => {
        if (countdown <= 0) {
            // 倒计时结束逻辑
            update();
        }
        else {
            timeOut.current = setTimeout(() => {
                setCountdown(countdown - 1);
            }, 1000);
        }
        return () => {
            clearTimeout(timeOut.current);
        };
    }, [countdown]);

    const hasCount = countdown > 0;

    return (
        <React.Fragment>
            {
                noStart && countdown < 86400 ?  <div className={cx('count-down-new-jd')}>
                    <div className={cx('count-content-jd')}>
                        <span className={cx('count-title-jd')}>距离开唱还有</span>
                        {hasCount && <CountDownTimeStr timestamp={countdown} />}
                    </div>
                 </div> : null
            }
        </React.Fragment>
    );
}
export default CountDown;
