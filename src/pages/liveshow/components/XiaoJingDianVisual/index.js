/* eslint-disable max-len */
/**
 * @file 新版分享页视觉visual_id 为 4
 * <AUTHOR>
 */

import React, {useEffect, useCallback, useState, useRef} from 'react';
import {TemplateEnum, logTxtMap, jdlogTxtMap} from '@/pages/liveshow/config/const';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import Dialog from '@/pages/liveshow/components/common/Dialog';
import {env} from '@/utils/env';
import {getMcpData, invokeApp} from '@/pages/liveshow/utils/mcpInvoke';
import {xiaoJingDianShowLog, xiaoJingDianStartLog, xiaoJingDianSlideLog} from '@/pages/liveshow/log';
import {initShare, generateShareUrl} from '@/utils/share';
import {registerPageHidden} from '@/utils';
import cls from 'classnames';
import LiveVideo from './LiveVideo';
import CountDown from './CountDown';
import MainBtn from './MainBtn';
import ShareBtn from './ShareBtn';
import style from './index.module.less';

const cx = cls.bind(style);

// 调起位
const posKey = 'pos_part';

// 页面投放的矩阵
const releaseAPP = env.isMainBox
                || env.isLiteBox
                || env.isHaokan
                || env.isTomas;

// 视频 或 直播 播放5s 后暂停播放并展示调起弹窗 非端内 且 （回流主板 或 矩阵）
const showInvokeModal = !releaseAPP;

const previewImg = 'https://mpics.bdstatic.com/qm/202404/pic_ITfuAq_1713162591.png';
const previewedImg = 'https://mpics.bdstatic.com/qm/202404/pic_Sdi806_1713162534.png';

// 自动跳转NA直播间端 主板、安卓Lite、畅听、好看
const entranceAPP = env.isMainBox
                || (env.isAndroid && env.isLiteBox)
                || env.isHaokan
                || env.isTomas;

// 展示分享按钮
const showShareBtn = entranceAPP || (env.isLiteBox && env.isIOS);

// 展示主按钮（预约、观看、回顾）
const showMainBtn = !(env.isLiteBox && env.isIOS);

let invokeInfo = {}; // mcp调起信息

const XiaoJingDianVisual = ({data, refresh}) => {
    const {
        template,
        status,
        schema_info, // 回流NA直播间信息
        countdown,
        tiebaLiveCmd,
        screen,
        nid,
        back_flow = {},
        new_visual = {},
        share
    } = data;
    const {enter_cmd = schema_info.enter_cmd, program_list} = new_visual;

    // 是否展示回流弹窗
    const [showModel, setShowModel] = useState({
        show: false,
        type: 'reflow',
    });
    // 初始化 视频未播放之前
    const [isInitStatus, setInitStatus] = useState(true);
    // const [invokeAppInfo, setInvokeAppInfo] = useState({});
    const agendaRef = useRef()
    const isPreview = template === TemplateEnum.Preview; // 是否为预约直播
    const isAutoPlay = ((env.isMainBox || env.isLiteBox || env.isTomas || env.isHaokan) && isPreview) || (env.isIOS && env.isLiteBox);
    let startTime = 0;

    useEffect(() => {
        const agendaDom = agendaRef.current;

        const agendaObserver = new IntersectionObserver(entries => {
            entries.forEach(entry => {
                if (entry.isIntersecting && entry.intersectionRatio) {
                    xiaoJingDianSlideLog('h5_subscribe','song_pendant', '15263', screen, jdlogTxtMap[status])
                }
            });
        }, {threshold: 0.2});

        agendaObserver.observe(agendaDom);

        return () => {
            agendaObserver.unobserve(agendaDom);
        };
    }, []);

    useEffect(() => {
        const getInvokeInfo = async () => {
            window.console.log(`enter_cmd1111: ${enter_cmd}`);
            // 非入口矩阵内 执行mcp调起逻辑
            if (!entranceAPP && enter_cmd) {
                try {
                    window.console.log(`enter_cmd: ${enter_cmd}`);
                    invokeInfo = await getMcpData(enter_cmd, 'main-btn-jd'); // 调起按钮的className main-btn-jd
                    window.console.log('invokeInfo222', invokeInfo);
                }
                catch (e) {
                    window.console.log(e);
                }
            }
        };

        getInvokeInfo();
    }, [enter_cmd]);

    useEffect(() => {
        if (share) {
            // 初始化框架分享
            initShare({
                linkUrl: generateShareUrl(share),
                title: share.main_title,
                content: share.sub_title,
                source: 'xcw_live2',
                iconUrl: share.cover,
                imageUrl: share.cover,
                mediaType: 'all',
            });
        }

        // 仅符合条件的矩阵内直接调起NA直播间
        if (entranceAPP) {
            jumpToNA();
        }
    }, [share]);

    useEffect(() => {
        // 页面时长打点开始
        startTime = new Date();
        xiaoJingDianStartLog('h5_subscribe', screen, 'start', '15266', jdlogTxtMap[status], 'page_tingliu', 0);
        registerPageHidden(
            () => {
                const endTime = new Date() - startTime;
                xiaoJingDianStartLog('h5_subscribe', screen, 'end', '15266', jdlogTxtMap[status], 'video', endTime);
                xiaoJingDianStartLog('h5_subscribe', screen, 'end', '15266', jdlogTxtMap[status], 'page_tingliu', endTime);
            },
            () => {
                startTime = new Date();
                xiaoJingDianStartLog('h5_subscribe', screen, 'start', '15266', jdlogTxtMap[status], 'video', 0);
                xiaoJingDianStartLog('h5_subscribe', screen, 'start', '15266', jdlogTxtMap[status], 'page_tingliu', 0);
            }
        );
    }, []);

    useEffect(() => {
        xiaoJingDianShowLog('h5_subscribe','subscribe', '15263', logTxtMap[status], jdlogTxtMap[status]);
    }, [status]);

    const jumpToNA = useCallback(() => {
        // 直播中、回放 符合条件自动跳转直播间
        if ([0, 3].indexOf(status) !== -1 && schema_info) {
            boxjsUtils.invokeSchema({schema: enter_cmd});
            return;
        }
    }, [status, schema_info]);

    const onClickShowModel = res => {
        setShowModel({
            show: res.show,
            type: res.type,
        });
    };

    const handleInvoke = () => {
        invokeApp(new_visual, invokeInfo, posKey);
    };

    return (
        <div className={`white-screen-patrol ${cx('xiao-jingdian-visual')}`}>
            <LiveVideo
                {...data}
                className='center'
                updatePageData={refresh}
                isBaijiahaoTag={false}
                onClickShowModel={onClickShowModel}
                setInitStatus={setInitStatus}
                isInitStatus={isInitStatus}
                isAutoPlay={isAutoPlay}
                handleInvoke={handleInvoke}
                jumpToNA={jumpToNA}
                isPreview={isPreview}
                showModel={showModel}
                entranceAPP={entranceAPP}
                showInvokeModal={showInvokeModal}
             />

            <div className={cx('title-jd')}>
                <img  src={isPreview ? previewImg : previewedImg} alt='logo'/>
            </div>
           <div className={cx('cover')}>
                <img  src='https://mpics.bdstatic.com/qm/202404/pic_nyqBJ2_1713265914.png' alt='图片'/>
           </div>
            <CountDown status={status} countdown={countdown} refresh={refresh} />
            <div className={cls({'button-jd':true,'no-countdown': countdown > 86400 || !(isPreview && countdown > 0) })}>
                {showShareBtn && <ShareBtn data={data} refresh={refresh} />}
                {showMainBtn && <MainBtn
                    data={data}
                    handleInvoke={handleInvoke}
                    jumpToNA={jumpToNA}
                    invokeInfo={invokeInfo}
                    refresh={refresh}
                    entranceAPP={entranceAPP}
                />}
            </div>
            <div className={cx('program')}>
               <img ref={agendaRef} src={program_list.length > 0 && program_list[0].img} alt='节目单'/>
            </div>
            <Dialog
                showModel={showModel}
                tiebaLiveCmd={tiebaLiveCmd}
                onClickShowModel={onClickShowModel}
                status={status}
                nid={nid}
                screen={screen}
                outerSchemeParams={data.schemeParams}
                backFlow={back_flow}
                handleInvoke={handleInvoke}
                invokeInfo={invokeInfo}
            />
        </div>
    );
};

export default XiaoJingDianVisual;
