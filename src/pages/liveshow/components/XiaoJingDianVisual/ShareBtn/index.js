/**
 * @file  ShareBtn 分享按钮
 * <AUTHOR>
 */

import React, {useCallback} from 'react';
import cls from 'classnames';
import {unifiedShare} from '@/utils/share';
import {xiaoJingDianClickLog} from '@/pages/liveshow/log';
import {jdlogTxtMap} from '@/pages/liveshow/config/const';
import {env} from '@/utils/env';
import style from './index.module.less';

const cx = cls.bind(style);

const ShareBtn = ({data}) => {
    const {
        status,
        share, // 链接分享信息
        new_visual = {} // 新视觉依赖物料
    } = data;

    const handleShare = useCallback(() => {
        xiaoJingDianClickLog('h5_subscribe', 'subscribe_btn', '15263', 'share', jdlogTxtMap[status]);
        unifiedShare(new_visual, share, 'xcw_live2');
    }, []);
    return (
        <div className={cx('share-btn-jd', env.isTomas && 'tomasShare')}  onClick={handleShare}>
            分享
        </div>
    );
};

export default ShareBtn;
