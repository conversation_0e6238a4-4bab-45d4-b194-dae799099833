import React, {useEffect, useCallback, useState, useRef, useMemo} from 'react';
import cls from 'classnames/bind';
import {lock, unlock} from 'tua-body-scroll-lock';
import InfiniteScroll from 'react-infinite-scroll-component';
import RecordItem from './RecordItem';
import RecordError from './RecordError';
import styles from './index.module.less';
import {getWinnerList, getInviteList} from '../../../service/api';
import MockJson from './mock.json';
import awardMockJson from './awardMock.json';

const cx = cls.bind(styles);

const titleMap = {
    assist: '助力记录',
    award: '中奖记录'
};

// type 为'assist'（助力） | 'award'（中奖）
const MaskList = ({type = 'assist', draw, handleMaskListStatus}) => {
    const contentScrollDom = useRef();

    const pageSize = 10;
    const [listData, setListData] = useState([]);
    const [pageShow, setPageShow] = useState(false);
    const [listState, setListState] = useState({
        isLoading: false,
        isError: false,
        hasMore: true,
        pageNo: 1,
        recordId: 0
    });

    const fetchData = useCallback(async () => {
        if (listState.isLoading || !listState.hasMore) {
            return;
        }
        setListState({
            ...listState,
            isLoading: true
        });

        try {
            let res = null;
            // 助力列表
            if (type === 'assist') {
                res = await getInviteList({
                    page_no: listState.pageNo,
                    page_size: pageSize,
                    record_id: listState.recordId
                });
                // res = MockJson;
            }
            else {
                // 中奖列表
                res = await getWinnerList({
                    act_id: draw.act_id,
                    biz_id: draw.biz_id,
                    page_no: listState.pageNo,
                    page_size: pageSize,
                    record_id: listState.recordId
                });
                // res = awardMockJson;
            }
            if (!pageShow) {
                setPageShow(true);
            }

            if (+res.errno === 0) {
                updateData(res.data);
            }
            else {
                setError();
            }
        }
        catch (error) {
            setError();
        }
    }, [listState, updateData, setError, pageShow]);

    const updateData = useCallback((data) => {
        if (data.list && data.list.length > 0) {
            setListData([...listData, ...data.list]);
        }
        let {pageNo} = listState;
        setListState({
            ...listState,
            isLoading: false,
            isError: false,
            hasMore: !!data.has_more,
            pageNo: pageNo + 1,
            recordId: data.record_id
        });
    }, [listState, listData]);

    const setError = useCallback(() => {
        setListState({
            ...listState,
            isLoading: false,
            isError: true
        });
    }, [listState]);

    const isEmpty = useMemo(() => {
        return listData.length === 0;
    }, [listData]);

    useEffect(() => {
        (async () => {
            try {
                fetchData();
            }
            catch (error) {
                console.log(error);
                setError();
            }
        })();
    }, []);

    const LoaderMessage = ({text, loading = false}) => {
        return (
            <div className={cx('loader-message')}>
                {
                    loading && (
                        <span className={cx('loading')}></span>
                    )
                }
                <span className={cx('text')}>{text}</span>
            </div>
        );
    };

    const refreshList = useCallback(() => {
        setListData([]);
        setPageShow(false);
        fetchData();
    }, [fetchData]);

    // useEffect(() => {
    //     window.console.log('targetElement');
    //     window.console.log(contentScrollDom);
        
    //     lock(contentScrollDom.current); // contentScrollRef.current
    //     return () => {
    //         unlock(contentScrollDom.current);
    //     };
    // }, []);

    return (
        <div className={cx('mask-list-wrapper')}>
            <div
                className={cx('close-block')}
                onClick={() => handleMaskListStatus(false, type)}
                onTouchMove={() => handleMaskListStatus(false, type)}
            >
                <div className={cx('close')}></div>
            </div>
            <div className={cx('mask-list')}>
                <div className={cx('title')}>{titleMap[type]}</div>
                {
                    pageShow ? (
                        <div
                            className={cx('content', {
                                'center': listState.isError || isEmpty
                            })}
                            id="infinitiScroll"
                            // ref={contentScrollDom}
                        >
                            {
                                listState.isError && (
                                    <RecordError refreshList={refreshList} />
                                )
                            }
                            {
                                !listState.isError && isEmpty && (
                                    <div className={cx('mask-list-record-empty')}>
                                        <div>{type === 'assist' ? '暂无好友助力，快去邀请吧~' : '名单加载中，请稍后查阅~'}</div>
                                    </div>
                                )
                            }
                            {
                                !listState.isError && !isEmpty && (
                                    <InfiniteScroll
                                        initialLoad={false}
                                        dataLength={listData.length}
                                        next={fetchData}
                                        hasMore={listState.hasMore}
                                        scrollThreshold="40px"
                                        scrollableTarget="infinitiScroll"
                                        endMessage={<LoaderMessage text='已展示全部记录' />}
                                        loader={<LoaderMessage text='加载更多' loading />}
                                        className='mask-list-record-scroll'
                                    >
                                        {
                                            listData.map((item, index) => {
                                                return (
                                                    <RecordItem item={item} key={index} />
                                                );
                                            })
                                        }
                                    </InfiniteScroll>
                                )
                            }
                        </div>
                    ) : (
                        <div className={cx('load-wrap')}>
                            <div className={cx('disk-wrap')}>
                                <span className={cx('left-note')}></span>
                                <span className={cx('right-note')}></span>
                                <div className={cx('disk')}></div>
                            </div>
                        </div>
                    )
                }
            </div>
        </div>
    );
}

export default MaskList;
