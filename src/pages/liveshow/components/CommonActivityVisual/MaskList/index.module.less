.mask-list-wrapper {
    width: 100%;
    height: 100vh;
    background-color: rgba(0, 0, 0, .8);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;
    animation: appear .3s ease forwards;

    .close-block {
        width: 100%;
        height: calc(100vh - 4.9rem);
        position: absolute;
        top: 0;
        left: 0;
    }

    .close {
        position: absolute;
        right: .366rem;
        bottom: .2467rem;
        width: .293rem;
        height: .293rem;
        background: url('./assets/close.png') no-repeat center;
        background-size: 100% 100%;
    }

    .mask-list {
        width: 100%;
        height: 4.89rem;
        position: absolute;
        bottom: 0;
        background: #F7F0DF url('./assets/bg.png') no-repeat center;
        background-size: 100% 100%;
        border-radius: .27rem .27rem 0 0;
    }

    .title {
        width: 100%;
        text-align: center;
        margin: .2rem 0 .16rem;
        color: #565656;
        font-family: "PingFang SC";
        font-size: .24rem;
        font-style: normal;
        font-weight: 600;
    }

    .content {
        height: calc(4.89rem - .4rem);
        width: 100%;
        padding: 0 .27rem;
        overflow-y: auto;
        padding-bottom: .3rem;

        &.center {
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .mask-list-record-empty {
        color: rgba(0, 0, 0, .5);
        font-family: PingFang SC;
        font-size: .167rem;
        margin-top: -.7rem;
    }

    .load-wrap {
        height: calc(4.89rem - .4rem);
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;

        .disk-wrap {
            width: 1rem;
            height: 1rem;
            position: relative;
        }

        .disk {
            width: 1rem;
            height: 1rem;
            background: url('./assets/disk.png') no-repeat center;
            background-size: 100% 100%;
            animation: rotate 1.5s linear infinite;
        }

        .left-note {
            position: absolute;
            left: .5rem;
            top: .2rem;
            width: .225rem;
            height: .31rem;
            background: url('./assets/left.png') no-repeat center;
            background-size: 100% 100%;
            animation: noteOffset 1.5s linear infinite;
        }

        .right-note {
            position: absolute;
            left: .6rem;
            top: .25rem;
            width: .152rem;
            height: .183rem;
            background: url('./assets/right.png') no-repeat center;
            background-size: 100% 100%;
            animation: noteOffsetSmall 1.5s linear infinite .3s;
        }
    }

    .loader-message {
        height: .49rem;
        color: #8D8D8F;
        font-size: .127rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .loading {
            width: .18148rem;
            height: .18148rem;
            background: url('./assets/loading.png') no-repeat center;
            background-size: 100%;
            animation: rotate 2s linear infinite;
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        @keyframes appear {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        @keyframes noteOffset {
            0% {
                opacity: 1;
                transform: translate(0, 0);
            }

            80% {
                opacity: 1;
            }

            100% {
                opacity: 0;
                transform: translate(.5rem, -.4rem);
            }
        }

        @keyframes noteOffsetSmall {
            0% {
                opacity: 1;
                transform: translate(0, 0);
            }

            80% {
                opacity: 1;
            }

            100% {
                opacity: 0;
                transform: translate(.8rem, -.3rem);
            }
        }
    }
}
