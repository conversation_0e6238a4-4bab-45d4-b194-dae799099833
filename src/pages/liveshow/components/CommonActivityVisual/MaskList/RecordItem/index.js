/**
 * @File: 记录列表项
*/
import React from 'react';
import classNames from 'classnames/bind';
import {dateFormat} from '@/utils/utils';
import styles from './index.module.less';

const cx = classNames.bind(styles);

const RecordItem = ({item}) => {

    return (
        <div className={cx('masklist-recode-wrapper')}>
            <img className={cx('avatar')} src={item.avatar} alt='' />
            <div className={cx('info')}>
                <div className={cx('desc')}>
                    <span className={cx('nick')}>{item.user_name}{item?.is_self ? '(自己)' : ''}</span>
                    <span className={cx('detail')}> {item.desc || '为您助力'}</span>
                </div>
                <div className={cx('time')}>
                    {dateFormat(new Date(item.time * 1000), 'yyyy.MM.dd hh:mm')}
                </div>
            </div>
        </div>
    );
};
export default RecordItem;
