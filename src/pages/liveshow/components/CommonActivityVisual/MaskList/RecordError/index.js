/**
 * @File: 记录空状态
*/
import React from 'react';
import classNames from 'classnames/bind';
import styles from './index.module.less';

const cx = classNames.bind(styles);

const RecordError = ({refreshList}) => {
    return (
        <div className={cx('mask-list-record-error')} onClick={refreshList}>
            <div className={cx('img')} />
            <p className={cx('text')}>网络不太好</p>
            <p className={cx('text')}>请点击重试</p>
        </div>
    );
};
export default RecordError;
