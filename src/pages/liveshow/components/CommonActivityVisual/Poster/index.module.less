.poster-content {
    font-family: "PingFang SC";
    font-size: .14rem;
    font-style: normal;
    font-weight: 600;

    .date {
        position: absolute;
        bottom: 1.23rem;
        left: .25rem;
        color: rgba(56, 56, 56, .8);
    }

    .seat {
        position: absolute;
        bottom: 1.23rem;
        left: 1.63rem;
        color: rgba(56, 56, 56, .8);
    }

    .user-info {
        position: absolute;
        bottom: .32rem;
        left: .15rem;
        display: flex;

        .avator {
            width: .42rem;
            height: .42rem;
            border-radius: 50%;
            margin-right: .13rem;
        }

        .nick {
            font-family: "PingFang SC";
            font-size: .14rem;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            text-align: left;
            margin-bottom: .01rem;
            color: #000;
        }

        .detail {
            position: relative;
        }

        .label {
            width: .547rem;
            height: .2rem;
            position: absolute;
            left: 0;
            top: .23rem;
        }
    }

    .qrcode {
        position: absolute;
        width: .56rem;
        height: .56rem;
        bottom: .303rem;
        right: .15rem;
    }

    .poster-tip {
        position: absolute;
        bottom: .17rem;
        right: .15rem;
        width: .63rem;
        height: .1rem;
        text-align: center;
        color: #898989;
        font-family: "PingFang SC";
        font-size: .073rem;
        font-style: normal;
        font-weight: 600;
    }
}
