/**
 * @file 海报弹窗
 * <AUTHOR>
 */

import React, {useState, useCallback, useEffect} from 'react';
import cls from 'classnames/bind';
import {mixImg} from 'mix-img';
import QRCode from 'antd/lib/qrcode';
import {env} from '@/utils/env';
import {dateFormat} from '@/utils/utils';
import {savePhotoToAlbum} from '@baidu/ug-matrix';
import RedPacketWrapper from '../RedPacketWrapper';
import {upload} from '../../../service/api';
import {activityShowLog, activityClickLog} from '@/pages/liveshow/log';
import styles from './index.module.less';
import {getUrlParam} from '@/pages/liveshow/utils/getUrlParam';
import {PageStore} from '@/pages/liveshow/service/register';

const cx = cls.bind(styles);

let isDownLoadingImg = false;
const qrCodeUrl = location.origin + `/m/media/multipage/liveshow/index.html?room_id=${PageStore.roomId}`;

const Poster = ({showPosterModel, onClose, drawLottery, materialConfig, handleToast}) => {
    const {
        show,
        data
    } = showPosterModel;
    if (!show) {
        return '';
    }

    // const data = {
    //     "style": "ticket", // 新增字段: 展现形式为门票
    //     "title": "预约成功",
    //     "sub_title": "获得一次抽奖机会",
    //     "button": "点击抽奖",
    //     "ext": {
    //         // toast style
    //         "toast": "预约成功并关注主播~", // 字段变更: 移到ext中
    //         // ticket style
    //         "poster_bg": "https://baidu-rmb-mvideofe-h5-static.cdn.bcebos.com/images/yuyue/poster_yuyue.png",
    //         "seat_no": "百度APP F区9排999座", // 新增字段
    //         "avatar_url": "https://avatar.bdstatic.com/it/u=3959177649,3625441908\u0026fm=3012\u0026app=3012\u0026autime=1735720507\u0026size=b200,200",
    //         "variate": "在紫霄岩采集矿石的胭脂花",
    //         "date": 1730725293,
    //         "date_str": "2025年1月末",
    //     }
    // };

    const avatarType = getUrlParam('type');
    const avatarMap = {
        '1': 'https://himg.bdimg.com/sys/portrait/item/wise.1.153668d0.tVPT4lD_S1AzWPx0pitsIg.jpg?time=9226&tieba_portrait_time=9226',
        '2': 'https://gips0.baidu.com/it/u=1725213584,2143508159&fm=3012&app=3012&autime=1705033089&size=b360,360',
        '3': 'https://avatar.bdstatic.com/it/u=3959177649,3625441908\u0026fm=3012\u0026app=3012\u0026autime=1735720507\u0026size=b200,200',
        '4': 'https://efe-h2.cdn.bcebos.com/ceug/resource/res/2020-07/1594717976441/idyexeq1u92w.png',
        '': data.ext?.avatar_url || ''
    }
    const avatar = avatarMap[avatarType] || '';

    // 头像base64地址
    const [avatarBase64Url, setAvatarBase64Url] = useState(avatar);
    // 动态更新信息
    const [replaceInfo, setReplaceInfo] = useState({
        date: data.ext.date_str || dateFormat(new Date(data.ext.date * 1000), 'yyyy.MM.dd hh:mm'),
        seatNo: data.ext?.seat_no || '',
        label: 'https://baidu-rmb-mvideofe-h5-static.cdn.bcebos.com/images/yuyue/yuyue_label.png',
        variate: data.ext?.variate || '',
        avatarUrl: `${avatar}${avatar.includes('?') ? '&' : '?'}yuyue=1`,
        qrCodeUrl: env.isLiteBox ? qrCodeUrl + '&source=lite' : qrCodeUrl
    });

    // 某些头像跨域,先转为base64
    const convertImageToBase64 = (imgUrl, callback) => {
        return new Promise((resolve, reject) => {
            const image = new Image();
            image.crossOrigin = 'anonymous';
            image.onload = () => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.height = image.naturalHeight;
                canvas.width = image.naturalWidth;
                ctx.drawImage(image, 0, 0);
                const dataUrl = canvas.toDataURL();
                resolve(dataUrl);
            };
            image.onerror = () => {
                reject(`加载失败：${imgUrl}`);
            };
            image.src = imgUrl;
        });
    };

    // 生成头像base64地址
    useEffect(async () => {
        if (avatar) {
            const avatarBase = await convertImageToBase64(avatar);
            setAvatarBase64Url(avatarBase);
        }
    }, []);

    // 更新动态信息
    useEffect(() => {
        if (avatarBase64Url) {
            setReplaceInfo({
                ...replaceInfo,
                avatarUrl: avatarBase64Url
            });
        }
    }, [avatarBase64Url]);

    // 保存图片
    const savePhoto = useCallback(async () => {
        if (isDownLoadingImg) {
            return;
        }

        // 保存中提示
        env.isAndroid && handleToast('saving-img');

        // 保存图片打点
        activityClickLog('save_photo', 15263);

        isDownLoadingImg = true;
        const ratio = 0.8;
        const configData = {
            'base': {
                'backgroundImg': data.ext.poster_bg || 'https://baidu-rmb-mvideofe-h5-static.cdn.bcebos.com/images/yuyue/poster_yuyue.png',
                'width': 960 * ratio,
                'height': 1588 * ratio,
                'quality': 0.75,
                'fileType': 'png',
                'isCrossOrigin': true
            },
            'qrCode': {
                'noCode': 0,
                'width': 168 * ratio,
                'height': 168 * ratio,
                'text': '{qrCodeUrl}',
                'x': 747 * ratio,
                'y': 1329 * ratio,
                'correctLevel': 1
            },
            'dynamic': [
                {
                'type': 2,
                'position': {
                    'x': 70 * ratio,
                    'y': 1200 * ratio
                },
                'style': {
                    'fontSize': 42 * ratio,
                    'color': 'rgba(56, 56, 56, 0.8)',
                    'textAlign': 'left',
                    'fontWeight': 'bold'
                },
                'text': '{date}'
                },
                {
                'type': 2,
                'position': {
                    'x': 490 * ratio,
                    'y': 1200 * ratio
                },
                'isCrossOrigin': false,
                'style': {
                    'fontSize': 42 * ratio,
                    'fontWeight': 'bold',
                    'color': 'rgba(56, 56, 56, 0.8)',
                    'textAlign': 'left'
                },
                'text': '{seatNo}'
                },
                {
                'type': 1,
                'position': {
                    'x': 48 * ratio,
                    'y': 1366 * ratio
                },
                'size': {
                    'dWidth': 126 * ratio,
                    'dHeight': 126 * ratio
                },
                'style': {},
                'isRound': true,
                'isCrossOrigin': true,
                'imgUrl': '{avatarUrl}'
                },
                {
                'type': 2,
                'position': {
                    'x': 210 * ratio,
                    'y': 1410 * ratio
                },
                'style': {
                    'fontSize': 42 * ratio,
                    'color': '#000000',
                    'fontWeight': 'bold',
                    'textAlign': 'left'
                },
                'isCrossOrigin': false,
                'text': '{variate}'
                },
                {
                'type': 1,
                'position': {
                    'x': 200 * ratio,
                    'y': 1433 * ratio
                },
                'isCrossOrigin': true,
                'size': {
                    'dWidth': 164 * ratio,
                    'dHeight': 60 * ratio
                },
                'imgUrl': '{label}',
                'isRound': false
                },
                {
                'type': 2,
                'position': {
                    'x': 754 * ratio,
                    'y': 1526 * ratio
                },
                'style': {
                    'fontSize': 22 * ratio,
                    'color': '#898989',
                    'fontWeight': 'bold'
                },
                'isCrossOrigin': false,
                'text': '扫码一起领门票'
                }
            ]
        };
        configData.replaceText = {
            ...replaceInfo
        };
        let success = 1; // success 为0 说明成功   1失败
        try {
            const res = await mixImg(configData);
            window.console.log('imgUrl res', JSON.stringify(res));
            let imgUrl = res?.data?.base64 || '';

            // 动态合成失败
            if (+res.errno != 0) {
                isDownLoadingImg = false;
                handleToast('save-img-fail');
                return;
            }

            if (env.isAndroid) {
                // 仅安卓下需先生成 url地址后再保存
                const uploadRes = await upload({
                    content_type: 'image/jpeg',
                    upload_type: 'cover',
                    data: imgUrl.replace(/^data:image\/\w+;base64,/, ''),
                    pre_email: 'live_yuyue',
                });
                imgUrl = uploadRes?.data?.url || '';

                // 安卓生成图片url失败
                if (+uploadRes.errno != 0) {
                    isDownLoadingImg = false;
                    handleToast('save-img-fail');
                    return;
                }
            }
            isDownLoadingImg = false;

            const resData = await savePhotoToAlbum({
                imageUrl: imgUrl
            });
            window.console.log('savePhotoToAlbum:', resData);

            if (+resData.status === 0) {
                // 保存成功
                success = 0;
                env.isLiteBox && handleToast('save-img-success');
            }
            else {
                success = 1;

                if (+resData.status === 1001) {
                    console.log('相机访问权限');
                    // toast('请在手机设置中\n打开相册访问权限');
                }

                handleToast('save-img-fail');
            }

        }
        catch (failData) {
            success = 1;
            window.console.log(failData);
            isDownLoadingImg = false;

            handleToast('save-img-fail');
        }
    }, [replaceInfo]);

    // 立即抽奖
    const lottery = useCallback(() => {
        // 立即抽奖打点
        activityClickLog('subscribe_choujiang', 15263);

        onClose('poster_lottery');
        drawLottery();
    }, []);

    useEffect(() => {
        // 页面展现
        activityShowLog('subscribe_success', 15263);
    }, []);

    return (
        <>
            <RedPacketWrapper
                materialConfig={materialConfig}
                outerText={'预约成功'}
                subOuterText='获得一次抽奖机会'
                btnAreaShow={false}
                bgType={3}
                doubleBtnAreaShow
                onClose={onClose}
                onClick={lottery}
                savePhoto={savePhoto}
                logoHidden
                outTextBgShow
                outTextShow
                outTextBgAnimationShow
            >
                <div className={cx('poster-content')}>
                    <div className={cx('date')}>{replaceInfo.date}</div>
                    <div className={cx('seat')}>{replaceInfo.seatNo}</div>
                    <div className={cx('user-info')}>
                        <img className={cx('avator')} src={avatarBase64Url} alt="" />
                        <div className={cx('detail')}>
                            <div className={cx('nick')}>{replaceInfo.variate}</div>
                            <img className={cx('label')} src={replaceInfo.label} />
                        </div>
                    </div>
                    <QRCode
                        value={replaceInfo.qrCodeUrl}
                        size={56}
                        color="#000000" // 二维码的颜色
                        bordered={false}
                        className={cx('qrcode')}
                    />
                    <div className={cx('poster-tip')}>扫码一起领门票</div>
                </div>
            </RedPacketWrapper>
        </>
    );
}

export default Poster;
