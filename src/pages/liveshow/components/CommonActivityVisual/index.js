/**
 * @file 通用活动预约页
 * <AUTHOR>
 */

import React, {useEffect, useCallback, useState, useRef} from 'react';
import {PageStore} from '@/pages/liveshow/service/register';
import {dateFormat} from '@/utils/utils';
import {matrixAppSchemeHeaderList, refolwForConfig} from '@/pages/liveshow/utils/reflow';
import {getAppSchemeHead, getUrlParam} from '@/pages/liveshow/utils/getUrlParam';
import {TemplateEnum} from '@/pages/liveshow/config/const';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import {execInvoke, getInvokeInfo, setCommonParams, createMcpWxTag} from '@baidu/mcp-sdk';
import {env} from '@/utils/env';
import {
    CommonSubscribeAndFollow,
    getSupportResult,
    getLotteryResultV2,
    getInviteResult,
} from '@/pages/liveshow/service/api';
import vSpeed from '@baidu/mvideo-vspeed';
import {activityShowLog, activityClickLog} from '@/pages/liveshow/log';
import HelpPage from './HelpPage';
import FirstScreen from './FirstScreen';
import FollowingPart from './FollowingPart';
import Dialog from '@/pages/liveshow/components/NewLiveShow/Dialog';
import {initShare} from '@/utils/share';
import cls from 'classnames/bind';
import LiveVideo from './LiveVideo';
import Toast from './Toast';
import MaskList from './MaskList';
import Poster from './Poster';
import Result from './Result';
import Back from './Back';
import './assets/style.module.less';
import styles from './index.module.less';

const cx = cls.bind(styles);
const roomId = getUrlParam('room_id');
const shareCuk = getUrlParam('share_cuk') || '';
const TIMER_DELAY = 30 * 1000; // 30s轮询

// 兼容折叠屏/ipad等设备
// (() => {
//     document.addEventListener('DOMContentLoaded', () => {
//         const screenWidth = window.innerWidth;
//         if (screenWidth > 500) {
//             setTimeout(() => {
//                 // 重设rem基准值
//                 document.documentElement.style.fontSize = '24.1546vw';
//             }, 10);
//         }
//     });
// })();

// mcp 场景信息
const APP_NAME_SCENE = {
    'baiduboxapp': 'sharepage',
    'lite': 'sharepagelite', // 新增scene
    '': 'sharepage'
};
// 预约页外部资源
const sids = ['tiebak', 'wangpan', 'ditu'];
// 调起位
const posKey = 'pos_part';
const source = PageStore.queryParams.source;
const appSchemeHead = getAppSchemeHead();
// 回流矩阵场景
const isMatrixApp = matrixAppSchemeHeaderList.includes(appSchemeHead);
const entranceAPP = (env.isMainBox && !isMatrixApp) || (env.isLiteBox); // 手百、极速版
// 仅在live.baidu.com 域名下安卓回手百新增微信开放标签
const isLiveDomain = location.host === 'live.baidu.com';
let invokeInfo = {}; // mcp调起信息

const CommonActivityVisual = ({data, refresh}) => {
    const {
        template,
        status,
        schema_info, // 回流NA直播间信息
        countdown,
        tiebaLiveCmd,
        screen,
        nid,
        back_flow = {},
        new_visual = {},
        share,
        user_info,
        appointment_status,
        appointment_number
    } = data
    const {enter_cmd = schema_info.enter_cmd, draw, invite_page = {}, material = {}, hide_inviter = 0} = new_visual;
    const isLogin = user_info.is_login; // 登录状态 0:未登录，1：已登陆

    const [showToast, setShowToast] = useState(false);
    const [toastType, setToastType] = useState('');
    const [toastMessage, setToastMessage] = useState('');
    const isAnti = useRef(false); // 是否为黑产
    const [subStatus, setSubStatus] = useState(appointment_status);// 0 未预约 1 已预约
    const [inviteTask, setInviteTask] = useState(new_visual?.invite_task || {}); // 助力任务
    // 是否展示回流弹窗
    const [showModel, setShowModel] = useState({
        show: false,
        type: 'reflow',
    });
    // 是否展示礼物弹窗 预约 抽奖
    const [showGiftModel, setShowGiftModel] = useState({show: false});
    // 是否展示挽留弹窗
    const [showBackModel, setShowBackModel] = useState(false);
    // 是否展示引导小手
    const [showGuideHand, setShowGuideHand] = useState(false);
    // 是否展示气泡
    const [hasBubble, setHasBubble] = useState(appointment_status === 0);
    // 初始化 视频未播放之前
    const [isInitStatus, setInitStatus] = useState(true);
    const [invokeAppInfo, setInvokeAppInfo] = useState({});
    const isPreview = template === TemplateEnum.Preview; // 是否为预约直播
    const [lotteryState, setLotteryState] = useState(0); // 0:复位 1:抽奖中 2:停止
    const [lotteryResult, setLotteryResult] = useState(0); // 抽奖结果 礼物id
    const [showFullList, setShowFullList] = useState(false); // 展示助力/中奖列表页面
    const [listType, setListType] = useState('assist'); // 列表类型 assist（助力)/award（中奖）
    const [showPosterModel, setShowPosterModel] = useState({show: false}); // 是否展示海报弹窗
    // 是否展示额外的封面
    const [showExtraCover, setShowExtraCover] = useState(true);
    // 垫片静音状态
    const [videoMuted, setVideoMuted] = useState(true);
    // 抽奖点击锁
    const lotteryLock = useRef(false);

    useEffect(() => {
        // 仅主板 或 极速版 执行返回挽留
        (env.isMainBox || env.isLiteBox) && hideBackButton();
    }, [])

    useEffect(() => {
        if (!isLogin) {
            return;
        }
        // 区分是否为“被邀请好友”，被邀请好友不进行自动预约, 请求助力接口
        if (shareCuk && entranceAPP) {
            handleSupport();
            return;
        }
        // 未预约用户进行自动预约
        if (entranceAPP && appointment_status !== 1) {
            handleSubscribeAndFollow('auto');
            return;
        }
        // 已预约用户刷新页面状态
        setSubStatus(appointment_status);
        setInviteTask(new_visual?.invite_task || {});
    }, [isLogin, appointment_status, new_visual]);

    useEffect(() => {
        polling();
    }, []);

    useEffect(() => {
        if (toastType) {
            setShowToast(true);
            setTimeout(() => {
                setShowToast(false);
                setToastType('');
                setToastMessage('');
            }, 3000);
        }
    }, [toastType]);

    useEffect(() => {
        // 初始化分享
        initShare({
            linkUrl: share.share_url,
            title: share.main_title,
            content: share.sub_title,
            source: 'huangshan_live',
            iconUrl: share.cover,
            imageUrl: share.cover,
            mediaType: 'all',
        });
    }, []);

    // 武侠歌会 预约页展示打点
    useEffect(() => {
        const value = entranceAPP ? 'subscribe' : 'port_btn';
        activityShowLog(value, 15263, {
            share_cuk: shareCuk
        });
    }, []);

    const jumpToNA = useCallback(() => {
        // 直播中、回放 符合条件自动跳转直播间
        if ([0, 3].indexOf(status) !== -1 && schema_info) {
            boxjsUtils.invokeSchema({schema: enter_cmd});
            return;
        }
    }, [status, schema_info]);

    const onClickShowModel = res => {
        setShowModel({
            show: res.show,
            type: res.type,
        });
    };

    const handleInvoke = () => {
        invokeApp(posKey);
    };

    useEffect(() => {
        // 非矩阵内 （回流主板 或 极速版）执行mcp调起逻辑
        if (!entranceAPP) {
            getMcpData(enter_cmd);
        }
    }, []);

    // 隐藏端返按钮
    const hideBackButton = () => {
        const params = {backBtnStatus: 0};
        boxjsUtils.invokeSchema({
            schema: `baiduboxapp://updateImmerseStyle/updateBackBtnStatus?params=${encodeURIComponent(JSON.stringify(params))}`
        });
    };

    const getMcpData = useCallback(async schema => {
        if (!schema) {
            return;
        }

        // 设置通用调起信息
        setCommonParams({
            app: 'wise',
            scene: APP_NAME_SCENE[source] || 'sharepage',
            ext_sid: sids.indexOf(source) !== -1 ? source : '' // 贴吧、网盘、地图
        });

        // 获取调起位详细信息
        const info = await getInvokeInfo({
            'invoke_info': {
                [posKey]: [{
                    'share_scheme': schema
                }]
            }
        });
        invokeInfo = info;
        setInvokeAppInfo({...info});

        // 存在调起信息 && 安卓 && 微信 && 回流主板 新增微信开放标签校验 isLiveDomain &&
        if (isLiveDomain && !isMatrixApp && env.isAndroid && env.isWechat && info.action_rule) {
            createWxTags(info);
        }
    }, []);

    const handleGuideHand = () => {
        setShowGuideHand(true);
        setTimeout(() => {
            setShowGuideHand(false);
            setTimeout(() => {
                setHasBubble(false);
            }, 1000);
        }, 3000);
    };

    // 创建微信开放标签
    const createWxTags = invokeInfo => {
        const {action_rule: actionRule, log_id: logId} = invokeInfo;
        const liveShareInfo = actionRule[posKey][0];
        const shareContainer1 = document.querySelector('.help-page-wrapper');
        const shareContainer2 = document.querySelector('.lottery-bg-outApp');
        const partDoms = [shareContainer1, shareContainer2];
        partDoms.forEach(dom => {
            dom && liveShareInfo && createMcpWxTag({targetDom: dom}, posKey, liveShareInfo, logId);
        });
    };

     // mcp 调起
     const invokeApp = useCallback(async (pos) => {
        const invokeConfig = {
            appName: appSchemeHead,
            toStore: true,
            scheme: new_visual?.enter_cmd,
            token: new_visual?.share_token
        };
        const invokeInfoForPos = invokeInfo && invokeInfo.action_rule && invokeInfo.action_rule[pos];
        if (!invokeInfoForPos || !Array.isArray(invokeInfoForPos) || !invokeInfoForPos[0]) {
            // 执行默认调起 兼容接口返回异常情况
            refolwForConfig(invokeConfig);
            return;
        }

        try {
            const res = await execInvoke(
                pos,
                invokeInfoForPos[0],
                invokeInfoForPos[0].log_id
            );
        }
        catch (e) {
            // 执行默认调起行为
            refolwForConfig(invokeConfig);
        }
    }, [invokeInfo]);

    // 轮询
    const polling = useCallback(() => {
        getTask();
        inviteTaskTimer && clearTimeout(inviteTaskTimer);
        const inviteTaskTimer = setTimeout(() => {
            polling();
        }, TIMER_DELAY);
    }, []);

    // 邀请用户接口
    const getTask = useCallback(async () => {
        const taskData = await getInviteResult();
        if (taskData.errno === 0 && subStatus === 1) {
            setInviteTask(taskData.data || {});
        }
    }, []);

    // 助力
    const handleSupport = useCallback(async () => {
        const fetchStart = Date.now();
        const res = await getSupportResult({
            act_id: draw?.act_id || '',
            biz_id: +draw?.biz_id || 0,
        });
        vSpeed.send({
            'api-support': Date.now() - fetchStart
        });
        if (res.errno === 0) {
            // 如果是黑产，本地记录
            if (+res?.data?.anti === 1) {
                isAnti.current = true;
            }
            setToastType(res?.data?.toast);
            return;
        }
        // 助力失败
        setToastType('support-fail');
        setTimeout(() => {
            handleGuideHand();
        }, 3000);
    }, []);

    // 预约并关注
    const handleSubscribeAndFollow = useCallback(async (type) => {
        const fetchStart = Date.now();

        activityShowLog('subscribe_and_follow', 15263, {
            api_name: 'subscribe_and_follow',
            room_id: roomId,
        });

        const res = await CommonSubscribeAndFollow({
            roomId: roomId,
            followAnchor: 1,
            actId: draw?.act_id || '',
            bizId: +draw?.biz_id || 0,
        });

        vSpeed.send({
            'api-subscribeAndFollow': Date.now() - fetchStart
        });

        activityShowLog('subscribe_and_follow_back', 15263, {
            api_name: 'subscribe_and_follow_back',
            room_id: roomId,
            errno: res?.errno,
            time: Date.now() - fetchStart
        });

        // 以预约结果为准
        if (+res?.errno === 0 && +res?.data?.subscription === 1) {
            setSubStatus(1);
            // 首次预约成功，提供一次抽奖机会
            res?.data?.first_subscription === 1 && setInviteTask({
                activity_status: 0,
                draw_number: 1,
                draw_number_used: 0,
            })
            res?.data?.first_subscription === 1 && setShowPosterModel({
                show: true,
                data: res.data?.tips
            });
        }
        else {
            const toastType = type === 'auto' ? 'auto-subscribe-fail' : 'subscribe-fail';
            // 手动预约失败
            setToastType(toastType);
            if (type === 'auto') {
                handleGuideHand();
            }
        }
    }, []);

    // 开始抽奖动效
    const handleLotteryStart = useCallback(() => {
        setLotteryState(1);
    }, []);

    // 结束抽奖动效
    const handleLotteryEnd = useCallback(() => {
        setLotteryState(2);
    }, []);

    // 复位抽奖动效
    const handleLotteryReset = useCallback(() => {
        setLotteryState(0);
    }, []);

    // 抽奖
    const drawLottery = useCallback(async () => {
        if (lotteryLock.current) {
            return;
        }
        lotteryLock.current = true;
        activityClickLog('choujiang_btn', 15263, {
            click_btn: 'wait_choujiang'
        });
        handleLotteryStart();
        try {
            const fetchStart = Date.now();
            const res = await getLotteryResultV2({
                act_id: draw?.act_id || '',
                biz_id: +draw?.biz_id || 0,
            });

            vSpeed.send({
                'api-lottery': Date.now() - fetchStart
            });

            // 以预约结果为准
            if (+res?.errno === 0 && res?.data) {
                setInviteTask(res.data?.invite_task || {});
                setTimeout(() => {
                    setLotteryResult(+res.data?.prize?.id);
                    setLotteryState(2);
                    setTimeout(() => {
                        setShowGiftModel({
                            show: true,
                            data: res.data.prize
                        });
                    }, 1500);
                }, 1000);
            }
            else {
                setTimeout(() => {
                    console.log('抽奖失败');
                    setToastType('lottery-fail');
                    setLotteryState(0);
                }, 2000);
            }
        } catch (error) {
            // 网络请求错误处理
            console.error('抽奖请求失败:', error);
            setToastType('lottery-fail');
            setLotteryState(0);
        } finally {
            // 无论成功失败，最后都要重置抽奖锁
            lotteryLock.current = false;
        }
    }, [])

    const handleBack = () => {
        const date = dateFormat(new Date(), 'yyyy-MM-dd');
        const storageName = 'preview_page_show_back_modal';
        const back_date = localStorage.getItem(storageName);
        // 如果已经弹过框，或者已经预约，则直接关闭
        if (back_date || subStatus === 1) {
            boxjsUtils.invokeSchema({schema:'baiduboxapp://v11/browser/closeWindow'});
            return;
        }
        localStorage.setItem(storageName, date);
        setShowBackModel(true);
    }

    // 关闭挽留弹窗
    const handleBackPopClose = () => {
        setShowBackModel(false);
    };

    // 挽留弹窗--确认离开
    const handleBackPopCancel = () => {
        setShowBackModel(false);
        boxjsUtils.invokeSchema({schema: 'baiduboxapp://v11/browser/closeWindow'});
    };

    // 挽留弹窗--继续参与活动
    const handleBackPopOk = () => {
        setShowBackModel(false);
        handleSubscribeAndFollow();
    };

    const handleToast = (type) => {
        setToastType(type);
    }

    const handleGiftPopClose = () => {
        setShowGiftModel({show: false});
    }

    const setOverflow = value => {
        const body = document.getElementById('root');
        if (body) {
            body.style.overflowY = value ? 'hidden' : 'auto';
        }
    };

    // 控制列表页状态
    const handleMaskListStatus = useCallback((status, type = 'award') => {
        setShowFullList(status);
        setListType(type);

        setOverflow(status);
    }, []);

    // 关闭海报弹窗
    const handlePosterPopClose = (type) => {
        setShowPosterModel(false);
        if(type === 'poster_lottery') {
            return;
        }
        handleGuideHand();
    };

    // 控制额外封面展示
    const closeExtraCover = useCallback(() => {
        setShowExtraCover(false);
    }, []);

    // 控制视频静音状态
    const handleVideoMutedStatus = (status) => {
        setVideoMuted(prevVideoMuted => !prevVideoMuted);
    };

    if (!entranceAPP && shareCuk) {
        return (
            <HelpPage
                invitePage={invite_page}
                handleInvoke={handleInvoke}
                materialConfig={material}
                shareCuk={shareCuk}
                hideInviter={hide_inviter}
            />
        );
    }

    return (
        <div className={cx('background')}>
            <div className={`white-screen-patrol ${cx('common-music-visual')}`}>
                <FirstScreen
                    data={data}
                    shareCuk={shareCuk}
                    subStatus={subStatus}
                    refresh={refresh}
                    entranceAPP={entranceAPP}
                    newVisual={new_visual}
                    isLogin={isLogin}
                    isAnti={isAnti.current}
                    inviteTask={inviteTask}
                    handleInvoke={handleInvoke}
                    handleToast={handleToast}
                    handleSubscribeAndFollow={handleSubscribeAndFollow}
                    drawLottery={drawLottery}
                    lotteryState={lotteryState}
                    lotteryResult={lotteryResult}
                    handleLotteryStart={handleLotteryStart}
                    handleLotteryEnd={handleLotteryEnd}
                    handleLotteryReset={handleLotteryReset}
                    handleMaskListStatus={handleMaskListStatus}
                    showGuideHand={showGuideHand}
                    hasBubble={hasBubble}
                    showExtraCover={showExtraCover}
                    materialConfig={material}
                    videoMuted={videoMuted}
                    handleVideoMutedStatus={handleVideoMutedStatus}
                    appointmentNumber={appointment_number}
                />
                <LiveVideo
                    {...data}
                    className='center'
                    updatePageData={refresh}
                    isBaijiahaoTag={false}
                    onClickShowModel={onClickShowModel}
                    setInitStatus={setInitStatus}
                    isInitStatus={isInitStatus}
                    isAutoPlay={false}
                    handleInvoke={handleInvoke}
                    jumpToNA={jumpToNA}
                    isPreview={isPreview}
                    showModel={showModel}
                    isLogin={isLogin}
                    entranceAPP={entranceAPP}
                    closeExtraCover={closeExtraCover}
                    materialConfig={material}
                    videoMuted={videoMuted}
                    handleVideoMutedStatus={handleVideoMutedStatus}
                />
                {
                    entranceAPP && <FollowingPart
                        isLogin={isLogin}
                        feedbackUrl={new_visual.feedback_url || ''}
                        modules={new_visual.modules || []}
                        acknowledge={new_visual.acknowledge || ''}
                        entranceAPP={entranceAPP}
                        handleToast={handleToast}
                        materialConfig={material}
                    />
                }

                {/* 返回按钮 */}
                {
                    entranceAPP && <div
                        className={cx('back-btn')}
                        onClick={handleBack}
                    ></div>
                }
                {
                    showToast && <Toast
                        toastType={toastType}
                        toastMessage={toastMessage}
                    />
                }
                {/* 回流弹窗 */}
                <Dialog
                    showModel={showModel}
                    tiebaLiveCmd={tiebaLiveCmd}
                    onClickShowModel={onClickShowModel}
                    status={status}
                    nid={nid}
                    screen={screen}
                    outerSchemeParams={data.schemeParams}
                    backFlow={back_flow}
                    handleInvoke={handleInvoke}
                    invokeInfo={invokeAppInfo}
                />
                {
                    showBackModel && (
                        <Back
                            onClose={handleBackPopClose}
                            onCancel={handleBackPopCancel}
                            onOk={handleBackPopOk}
                            materialConfig={material}
                        />
                    )
                }
                {
                    showFullList && (
                        <MaskList
                            type={listType}
                            handleMaskListStatus={handleMaskListStatus}
                            draw={draw}
                        />
                    )
                }
                {
                    // 抽奖结果弹窗
                    <Result
                        showGiftModel={showGiftModel}
                        newVisual={new_visual}
                        onClose={handleGiftPopClose}
                    />
                }
                {
                    // 海报弹窗
                    <Poster
                        onClose={handlePosterPopClose}
                        showPosterModel={showPosterModel}
                        drawLottery={drawLottery}
                        materialConfig={material}
                        handleToast={handleToast}
                    />
                }
            </div>
        </div>
    );
}

export default CommonActivityVisual;
