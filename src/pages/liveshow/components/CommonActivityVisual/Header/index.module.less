.header {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    z-index: 3;
    position: relative;
    pointer-events: auto;

    .title {
        width: 100%;
        pointer-events: none;
        background-position: center;
        background-size: contain;
        background-repeat: no-repeat;
    }

    .teel-title {
        width: 100%;
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
    }

    .head-share-btn,
    .winning-list-btn,
    .head-muted-btn,
    .rule-btn {
        position: absolute;
        width: .32rem;
        height: .32rem;
        border-radius: .32rem;
        font-family: "PingFang SC";
        font-weight: 500;
        font-size: .1rem;
        align-content: center;
        text-align: center;
        text-wrap: wrap;
        line-height: .32rem;
    }

    .rule-btn {
        top: .53rem;
        left: .12rem;
    }

    .winning-list-btn {
        display: flex;
        top: .97rem;
        left: .12rem;
        line-height: .1rem;
        justify-content: center;
        align-items: center;
    }

    .head-share-btn {
        top: .53rem;
        right: .12rem;
    }

    .head-muted-btn {
        top: .97rem;
        right: .12rem;
    }

    .head-muted-img {
        width: .32rem;
        height: .32rem;
        border-radius: .32rem;
    }
}
