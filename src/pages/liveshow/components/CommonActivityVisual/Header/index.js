/**
 * @file 活动预约页Header内容
 * @abstract 1. 标题；2.副标题 3.规则、分享、名单按钮
 * <AUTHOR>
 */

import React, {useEffect, useCallback, useState, useRef} from 'react';
import cls from 'classnames/bind';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import {activityShowLog, activityClickLog} from '@/pages/liveshow/log';
import styles from './index.module.less';

const cx = cls.bind(styles);

const Head = props => {
    const {
        isLogin,
        ruleUrl,
        entranceAPP,
        handleMaskListStatus,
        handleShare,
        materialConfig,
        videoMuted,
        handleVideoMutedStatus,
    } = props;

    const btnStyles = {
        backgroundColor: materialConfig?.head_btn_color,
        color: materialConfig?.head_btn_font_color
    };

    // 打开规则页
    const openRulePage = () => {
        activityClickLog('rule', 15263);
        if (entranceAPP) {
            // 跳转规则页
            boxjsUtils.openImmerseBrowser(ruleUrl);
        }
    }

    // 打开中奖名单 需要登录
    const openWinningList = () => {
        if (!isLogin && entranceAPP) {
            boxjsUtils.login().then(() => {
                window.setTimeout(() => {
                    refresh();
                }, 500);
            });
            return;
        }
        activityClickLog('b_award_list', 15263);
        handleMaskListStatus(true, 'award');
    }

    // 分享直播间
    const shareRoom = () => {
        activityClickLog('share', 15263);
        handleShare(false);
    }

    return (
        <div className={cx('header')}>
            <div
                className={cx('title')}
                style={{
                    backgroundImage: `url('${materialConfig?.title}')`,
                    height: `${materialConfig?.title_height}rem`,
                    marginTop: `${materialConfig?.title_margin}rem`
                }}
            ></div>
            <div className={cx('teel-title')}></div>
            {
                entranceAPP && (
                    <>
                        <div
                            className={cx('rule-btn')}
                            style={btnStyles}
                            onClick={openRulePage}
                        >规则</div>

                        <div
                            className={cx('winning-list-btn')}
                            style={btnStyles}
                            onClick={openWinningList}
                        >
                            <div>
                                中奖<br></br>名单
                            </div>
                        </div>

                        <div
                            className={cx('head-share-btn')}
                            style={btnStyles}
                            onClick={shareRoom}
                        >分享</div>
                        <div
                            className={cx('head-muted-btn')}
                            style={btnStyles}
                            onClick={handleVideoMutedStatus}
                        >
                            <img
                                className={cx('head-muted-img')}
                                src={videoMuted ? materialConfig?.head_muted : materialConfig?.head_not_muted}
                            />
                        </div>
                    </>
                )
            }
        </div>
    );
}

export default Head;
