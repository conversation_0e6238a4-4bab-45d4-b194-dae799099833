/**
 * @file 直播落地页弹窗组件（新版）
 */

import React, {useEffect, useState} from 'react';
import {Dialog} from '@baidu/nano-react';
import cls from 'classnames/bind';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import {activityShowLog, activityClickLog} from '@/pages/liveshow/log';
import style from './index.module.less';

import giftImg from '.././assets/img/temp_gift.png'
const cx = cls.bind(style);

let textMap = {
    subscribe: {
        align: 'left',
        title: '预约成功并关注百度直播',
        sub_title: '送您直播礼物已存入直播间「礼物背包」',
        prize_name: '预约礼物',
        dialog_img: giftImg
    },
    prize: {
        align: 'left',
        title: '',
        sub_title: '已存入直播间「礼物背包」',
        prize_name: '直播礼物',
        price_str: '',
        dialog_img: giftImg
    },
    money: {
        align: 'left',
        title: '',
        sub_title: '去个人中心【钱包】提现哦',
        prize_name: '',
        dialog_img: giftImg
    },
    real: {
        align: 'left',
        title: '',
        sub_title: '需72h内登记信息完成领取',
        prize_name: '真人签名照',
        dialog_img: giftImg
    },
};

function GiftDialog(props) {
    const { show, type, data } = props.showGiftModel;
    if (!show) {
        return '';
    }
    const [seconds, setSeconds] = useState(5); // 5s倒计时
    const [isClose, setIsClose] = useState(false);

    const onClose = (flag) => {
        // 预约弹窗5s关闭打点
        if (type === 'subscribe') {
            const extText = flag === 'click' ? 'accept' : '5s_close';
            activityClickLog('subscribe_success', 15263, {
                click_btn: extText
            });
        }
        props.onClose?.();
    };

    const info = textMap[type];

    // 倒计时
    useEffect(() => {
        setTimeout(() => {
            if (seconds > 1) {
                setSeconds(seconds - 1);
            }
            else {
                !isClose && onClose();
            }
        }, 1000);
    }, [seconds, isClose]);

    // 如果云控有下发分享信息，优先取云控下发数据
    if (data) {
        // info.title = data?.title || info.title;
        info.sub_title = data?.sub_title || info.sub_title;
        info.prize_id = data?.prize?.prize_id || '';
        info.price_str = data?.prize?.price_str || '';
        info.prize = data?.prize?.price || '';
        info.prize_name = data?.prize?.name || data?.prize_name || info.prize_name;
        info.dialog_img = data?.prize?.image || data?.prize_image || info.dialog_img;
    }

    // 打点
    useEffect(() => {
        // 预约弹窗展现打点
        if (type === 'subscribe') {
            activityShowLog('subscribe_success', 15263);
            return;
        }
        const extParams = type === 'money'
        ? {
            money: info.prize
        }
        : {
            award :{
                content_txt: info.prize_name,
                content_id: info.prize_id
            }
        }
        activityShowLog('pop_prize_yes', 15263, extParams);
    }, [])

    const handleClose = () => {
        onClose('click');
        setIsClose(true);
    }

    // 跳转到资产钱包页
    const jumpToWallet = () => {
        handleClose();
        boxjsUtils.openEasyBrowse('https://ucenter.baidu.com/ucenterv4/page/act/e3NqA/assetIndex?serVer=v4&jumpHome=1&immerseBrowser=1', 0);
    };

    const prize = Number.isInteger(+info.prize / 100) ? (+info.prize / 100) : (+info.prize / 100).toFixed(1);

    return (
        <Dialog
            className={cx('dialog-wrapper dialog-wrapper-wu')}
            closeType='outside'
            showCancel
            showClose
            onClose={handleClose}
        >
            <div className={cx('light', {
                'light-money': type === 'money',
            })}></div>
            {
                <div className={cx('dialog-bg', {
                    'money-bg': type === 'money',
                })}></div>
            }

            {
                type !== 'money' && (
                    <div className={cx('music-icon')}></div>
                )
            }
            {
                type === 'subscribe' && (
                    <>
                        <div className={cx('cloud')}></div>
                        <div className={cx('dialog-title')}>{info.title}</div>
                        <div className={cx('sub-title')}>
                            {info.sub_title}
                        </div>
                        <div>
                            {
                                info.dialog_img && (
                                    <div
                                        className={cx('gift-img')}
                                        style={{backgroundImage: `url(${info.dialog_img})`}}
                                        >
                                    </div>
                                )
                            }
                        </div>
                        <div className={cx('gift-name')}>{info.prize_name}</div>
                    </>
                )
            }
            {
                (type === 'prize'  || type === 'real') && (
                    <>
                        <div className={cx('prize-title', {
                            'real-sign-title': type === 'real' && info.prize_id !== 8,
                            "real-ticket-title": type === 'real' && info.prize_id === 8
                        })}></div>
                        <div>
                            {
                                info.dialog_img && (
                                    <div
                                        className={cx('gift-img', ' prize-img')}
                                        style={{backgroundImage: `url(${info.dialog_img})`}}
                                        >
                                    </div>
                                )
                            }
                        </div>
                        <div className={cx('gift-name', 'real-name', {
                            'prize-name': type === 'prize' && info?.price_str,
                        })}>{info.prize_name}</div>
                        {
                            (type === 'prize' && info?.price_str) && (
                                <div className={cx('price_str')}>{info.price_str}</div>
                            )
                        }
                        <div className={cx('sub-title', 'prize-sub-title')}>
                            {info.sub_title}
                        </div>
                    </>
                )
            }
            {
                type === 'money' && (
                    <>
                        <div className={cx('money-title')}></div>
                        <div className={cx('amount')}>
                            <div className={cx('amount-num')}>{prize}</div>
                            <div className={cx('amount-unit')}>元</div>
                        </div>
                        <div className={cx('sub-title', 'prize-sub-title')}>
                            {info.sub_title}
                        </div>
                    </>
                )
            }

            <div
                className={cx('dialog-btn', {
                    'dialog-btn-prize': type === 'money',
                })}
                onClick={type === 'real' ? jumpToWallet : handleClose}
            >
                <span className={cx('dialog-btn-txt')}>
                    {
                        type === 'real'
                        ? '立即领取'
                        : '开心收下'
                    }
                    ({seconds}s)
                </span>
            </div>
        </Dialog>
    );
}

export default GiftDialog;
