.bag-outer-text {
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: FZZhengHeiS-EB-GB;
    font-style: normal;
    font-weight: 400;

    .text-pre {
        font-size: .333rem;
        // line-height: 0.344rem;
        background: linear-gradient(179deg, #FFDFA1 -4.54%, #FFFCE0 76.46%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .text-main {
        font-size: .333rem;
        // line-height: 0.344rem;
        background: linear-gradient(180deg, #FFCA99 11.39%, #FF9939 79.21%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
}

.bag-content {
    font-family: "PingFang SC";

    .money-box {
        margin-top: .4rem;

        .money-tip {
            color: rgba(0, 0, 0, .5);
            font-size: .15rem;
            font-style: normal;
            font-weight: 400;
            margin-top: .02rem;
        }
    }
}

.result-content {
    font-family: "PingFang SC";

    .prize {
        width: 2.393rem;
        height: 2.393rem;
        margin: -.7rem auto 0;
    }

    .info {
        color: #FF4E4F;
        text-align: center;
        font-family: "PingFang SC";
        font-size: .273rem;
        font-style: normal;
        font-weight: 600;
        display: flex;
        flex-direction: column;
        position: absolute;
        width: 100%;
        top: 1.82rem;
        left: 50%;
        transform: translateX(-50%);
    }

    .value {
        font-size: .2rem;
        font-weight: 400;
        position: absolute;
        top: .35rem;
        left: 50%;
        transform: translateX(-50%);
    }

    .prize-tip {
        color: rgba(0, 0, 0, .5);
        font-size: .15rem;
        font-weight: 400;
        margin-top: .4rem;
    }

    .thx-tip {
        color: #873A01;
        text-align: center;
        font-family: "PingFang SC";
        font-size: .247rem;
        font-weight: 400;
        margin: .43rem auto 0;
    }

    .thx-img {
        width: 2.82rem;
        height: 1.081rem;
        background: url('./assets/thx_img.png') no-repeat center;
        background-size: 100% 100%;
        margin: .27rem auto 0;
    }
}
