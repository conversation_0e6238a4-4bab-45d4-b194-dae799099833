/**
 * @file 抽奖结果弹窗
 * <AUTHOR>
 */

import React, {useCallback, useEffect} from 'react';
import cls from 'classnames/bind';
import RedPacketWrapper from '../RedPacketWrapper';
import MoneyBox from '../MoneyBox';
import styles from './index.module.less';
import {activityShowLog} from '@/pages/liveshow/log';

const cx = cls.bind(styles);

const Result = (props) => {
    const {materialConfig, newVisual} = props;
    const {
        show,
        data
    } = props.showGiftModel;
    if (!show) {
        return '';
    }

    // const data = {
    //     'id': 123,
    //     'notice': '恭喜抽中',
    //     'image': 'https://ala-gift.cdn.bcebos.com/gift/2024-12/1735210605295/iphone.png', // 奖品展示图片
    //     'desc': '茅台酒一瓶', // 奖品描述文案
    //     'sub_desc': '', // （价值998）
    //     'btn_tip': '可在【我的奖品】查看', // 按钮提示
    //     'scheme': 'xxx', // 钱包页scheme
    //     'type': 2, // 1:红包 2:实物奖品 3:打赏礼物 4:音频(祝福语) 5:奖券 6:福袋 100:谢谢惠顾(thx)
    //     'ext': {
    //         // 红包
    //         'amount': 100, // 红包:100分钱
    //         // 语音祝福
    //         'audio_url': 'https://abc.mp3', // 音频文件链接
    //         'duration': 10,
    //         // 打赏礼物
    //         'backpack_prize': {
    //             'backpackType': '', // 背包礼物类型
    //             'id': '123',
    //             'icon': '',
    //             'name': '比心',
    //             'num': 1,
    //         },
    //         'has_used': 0, // 打赏虚拟礼物:打赏礼物是否已经使用
    //         // 谢谢惠顾(thx)
    //         'anti_status': 1 // 1:风险用户
    //     }
    // }

    const onClose = useCallback((flag) => {
        // 预约弹窗5s关闭打点
        // if (type === 'subscribe') {
        //     const extText = flag === 'click' ? 'accept' : '5s_close';
        //     activityClickLog('subscribe_success', 15263, {
        //         click_btn: extText
        //     });
        // }
        props.onClose?.();
    }, []);

    const handleClose = useCallback(() => {
        onClose('click');
    }, []);

    useEffect(() => {
        // 页面展现
        activityShowLog('pop_prize_yes', 15263);
    }, []);

    // 跳转到资产钱包页
    const jumpToWallet = useCallback(() => {
        handleClose();
        newVisual?.wallet_url && boxjsUtils.invokeSchema({schema: newVisual?.wallet_url});
    }, []);

    return (
        <>
            {
                +data.type === 1 && (
                    <RedPacketWrapper
                        materialConfig={materialConfig}
                        outerTextCustom={
                            <div className={cx('bag-outer-text')}>
                                <div className={cx('text-pre')}>恭喜获得</div>
                                <div className={cx('text-main')}>星动红包</div>
                            </div>
                        }
                        // btnText={'开心收下'}
                        btnBg={'https://ala-gift.cdn.bcebos.com/gift/2024-12/1735112154736/accept_btn.png'}
                        btnAreaShow
                        bgType={2}
                        onClick={handleClose}
                        onClose={handleClose}
                        logoHidden
                        outTextBgShow
                        outTextShow
                        outTextBgAnimationShow
                    >
                        <div className={cx('bag-content')}>
                            <div className={cx('money-box')}>
                                <MoneyBox amount={data?.ext?.amount || 0} showTip />
                                <div className={cx('money-tip')}>{data.btn_tip}</div>
                            </div>
                        </div>
                    </RedPacketWrapper>
                )
            }
            {
                // 谢谢回顾
                (+data.type === 100) && (
                    <RedPacketWrapper
                        outerText={'很遗憾未抽中'}
                        // btnText={'再接再厉'}
                        btnBg={'https://ala-gift.cdn.bcebos.com/gift/2024-12/1735212258145/thx_btn.png'}
                        btnAreaShow
                        bgType={4}
                        onClick={handleClose}
                        onClose={handleClose}
                        logoHidden
                        outTextBgShow
                        outTextShow
                        outTextBgAnimationShow
                    >
                        <div className={cx('result-content')}>
                            <div className={cx('thx-tip')}>
                                <p>别灰心</p>
                                <p>再接再厉</p>
                            </div>
                            <div className={cx('thx-img')}></div>
                        </div>
                    </RedPacketWrapper>
                )
            }
            {
                (+data.type !== 1 && +data.type !== 100) && (
                    <RedPacketWrapper
                        materialConfig={materialConfig}
                        outerText={data.notice}
                        // btnText={+data.type === 2 ? '立即领取' : '开心收下'}
                        btnBg={'https://ala-gift.cdn.bcebos.com/gift/2024-12/1735112154736/accept_btn.png'}
                        btnAreaShow
                        bgType={1}
                        onClick={+data.type === 2 ? jumpToWallet : handleClose}
                        onClose={handleClose}
                        logoHidden
                        outTextBgShow
                        outTextShow
                        outTextBgAnimationShow
                    >
                        <div className={cx('result-content')}>
                            {data.image && <img className={cx('prize')} src={data.image} alt='' />}
                            <div className={cx('info')}>
                                <span className={cx('name')}>{data.desc}</span>
                                {data.sub_desc && <span className={cx('value')}>{data.sub_desc}</span>}
                                <span className={cx('prize-tip')}>{data.btn_tip}</span>
                            </div>
                        </div>
                    </RedPacketWrapper>
                )
            }
        </>
    );
}

export default Result;
