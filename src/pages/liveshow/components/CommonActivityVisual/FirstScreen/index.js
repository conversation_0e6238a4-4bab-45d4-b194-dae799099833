/**
 * @file 活动预约页首屏内容
 * @abstract 1. head：活动标题、按钮；2. countdown：倒计时；3. Lottery抽奖区域
 * <AUTHOR>
 */

import React, {useEffect, useCallback, useState, useRef} from 'react';
import {openShareBox} from '@/utils/share';
import {env} from '@/utils/env';
import {activityShowLog, activityClickLog} from '@/pages/liveshow/log';
import cls from 'classnames/bind';
import Header from '../Header';
import CountDown from '../CountDown';
import Button from '../common/Button';
import Lottery from '../Lottery';
import MainBtn from '../common/MainBtn';
import styles from './index.module.less';

const cx = cls.bind(styles);

const FirstScreen = props => {
    const {
        data,
        shareCuk,
        subStatus,
        refresh,
        entranceAPP,
        newVisual = {},
        handleInvoke,
        isLogin,
        handleToast,
        handleSubscribeAndFollow,
        isAnti,
        inviteTask,
        drawLottery,
        lotteryState,
        lotteryResult,
        handleLotteryStart,
        handleLotteryEnd,
        handleLotteryReset,
        handleMaskListStatus,
        showGuideHand,
        hasBubble,
        showExtraCover,
        materialConfig,
        videoMuted,
        handleVideoMutedStatus,
        appointmentNumber
    } = props;
    const {status, countdown, share, chat_mcast_id, reliable_mcast_id} = data;
    const {is_share_token = 0, share_token = ''} = newVisual;

    const btnText = useRef('预约直播\n并抽奖');

    useEffect(() => {
        switch (inviteTask?.activity_status) {
            // 活动进行中
            case 0:
                if (subStatus === 0 || !isLogin) {
                    btnText.current = '预约直播\n并抽奖';
                }
                else {
                    btnText.current = '点击抽奖';
                }
                break;
            // 抽奖次数用完
            case 1:
                btnText.current = '抽奖机会\n已用完';
                break;
            // 所有活动已结束
            case 2:
                btnText.current = '抽奖活动\n已结束';
                break;
        }
    }, [subStatus, inviteTask, isLogin]);

    const invokeApp = useCallback(() => {
        activityClickLog('port_btn', 15236);
        handleInvoke && handleInvoke();
    }, []);

    const removeShareCukParam = (url) => {
        const index = url.indexOf('&share_cuk=');
        if (index === -1) {
            return url;
        }

        const endIndex = url.indexOf('&', index + 1);
        if (endIndex === -1) {
            return url.substring(0, index);
        }
        return url.substring(0, index) + url.substring(endIndex);

    };

    /**
     * 分享
     * @param {boolean} isHelp 是否是助力分享
     */
    const handleShare = useCallback((isHelp) => {
        const shareUrl = isHelp
            ? share.share_url
            : removeShareCukParam(share.share_url);
        if (!!is_share_token && share_token) {
            // 口令分享
            const schema = `baiduboxapp://share/doShare?params=${encodeURIComponent(JSON.stringify({'shareData': {
                'mediaType': 'all',
                'title': share.main_title,
                'content': share.sub_title,
                'linkUrl': shareUrl,
                'imageUrl': share.cover,
                'iconUrl': share.cover,
                'type': env.isIOS ? 'text' : 9,
                'textContent': share_token
            }}))}`;

            boxjsUtils.invokeSchema({schema});
        }
        else {
            openShareBox({
                linkUrl: env.isLiteBox ? shareUrl + '&source=lite' : shareUrl,
                title: share.main_title,
                content: share.sub_title,
                source: 'huangshan_live',
                iconUrl: share.cover
            });
        }
    }, []);

    // 按钮群
    const btnGroup = useCallback(() => {
        return (
            <MainBtn
                newVisual={newVisual}
                data={data}
                shareCuk={shareCuk}
                isLogin={isLogin}
                inviteTask={inviteTask}
                handleToast={handleToast}
                btnText={btnText.current}
                subStatus={subStatus}
                handleSubscribeAndFollow={handleSubscribeAndFollow}
                drawLottery={drawLottery}
                refresh={refresh}
                isAnti={isAnti}
                handleLotteryStart={handleLotteryStart}
                handleLotteryEnd={handleLotteryEnd}
                handleLotteryReset={handleLotteryReset}
                handleShare={handleShare}
                handleMaskListStatus={handleMaskListStatus}
                showGuideHand={showGuideHand}
                hasBubble={hasBubble}
                materialConfig={materialConfig}
            />
        );
    }, [subStatus, entranceAPP, inviteTask, showGuideHand, hasBubble, isLogin]);

    return (
        <div
            className={cx('common-music-cover1')}
            style={{
                backgroundImage: `url('${materialConfig?.first_screen_bg}')`
            }}
        >
            <Header
                isLogin={isLogin}
                ruleUrl={env.isLiteBox ? newVisual?.rule_url_lite : newVisual?.rule_url}
                entranceAPP={entranceAPP}
                handleMaskListStatus={handleMaskListStatus}
                handleShare={handleShare}
                materialConfig={materialConfig}
                videoMuted={videoMuted}
                handleVideoMutedStatus={handleVideoMutedStatus}
            />
            {
                showExtraCover && <div
                    className={cx('common-music-extra-cover')}
                    style={{
                        backgroundImage: `url('${materialConfig?.video_extra_cover}')`
                    }}
                ></div>
            }
            <CountDown
                status={status}
                countdown={countdown}
                refresh={refresh}
                subStatus={subStatus}
                entranceAPP={entranceAPP}
                showStartTime={newVisual?.show_start_time}
                materialConfig={materialConfig}
                chatMcastId={chat_mcast_id}
                reliableMcastId={reliable_mcast_id}
            />
            {
                entranceAPP ? (
                    <>
                        <Lottery
                            entranceAPP={entranceAPP}
                            inviteTask={inviteTask}
                            newVisual={newVisual}
                            lotteryState={lotteryState}
                            lotteryResult={lotteryResult}
                            subStatus={subStatus}
                            materialConfig={materialConfig}
                            appointmentNumber={appointmentNumber}
                        />
                        {btnGroup()}
                    </>
                )
                    : <>
                        <div
                            className='lottery-bg-outApp'
                            style={{
                                backgroundImage: `url('${materialConfig?.lottery_bg_outApp}')`,
                                height: `${materialConfig?.lottery_bg_outApp_height}rem`
                            }}
                            onClick={invokeApp}
                        >
                        </div>
                        <div className='main-btn-outApp'>
                            <Button
                                btnText={'马上预约'}
                                width='2.34rem'// 按钮宽度
                                height='0.749rem' // 按钮高度
                                btnTextColor={materialConfig?.lotter_main_btn_font_color}
                                btnBg={materialConfig?.main_btn_outApp}
                                btnTextSize='0.321' // 按钮字体大小
                                hasBubble
                                bubbleWidth='1.584rem' // 气泡宽度
                                bubbleBg={materialConfig?.lotter_main_btn_bubble_bg_outApp} // 气泡背景
                                hasZoomIn={false}
                            />
                        </div>
                        <div className='company-outApp'>百度在线网络技术（北京）有限公司 版本号13.77.0</div>
                        <a className='policy-outApp' href='https://s.bdstatic.com/common/agreement/privacy.html'>隐私政策和权限</a>
                    </>
            }

        </div>
    );
};

export default FirstScreen;
