/**
 * @file  挽留弹窗
 * <AUTHOR>
 */

import React, {useEffect} from 'react';
import {activityShowLog, activityClickLog} from '@/pages/liveshow/log';
import cls from 'classnames';
import style from './index.module.less';

const cx = cls.bind(style);

const BackDialog = (props = {}) => {

    useEffect(() => {
        activityShowLog('pop_stay', 15263);
    }, []);

    const handleClose = () => {
        props.onClose?.();
    }

    const handleCancel = () => {
        activityClickLog('stay_exit', 15263);
        props.onCancel?.();
    }

    const handleOk = () => {
        activityClickLog('stay_yuyue_and_choujiang', 15263);
        props.onOk?.();
    }

    return (
        <div className={cx('back-wrap')}>
            <div className={cx('back-title')}>
                <div className={cx('back-close')}>
                    <div className={cx('back-close-icon')} onClick={handleClose}></div>
                </div>
                <div className={cx('back-dialog-title')}>你要放弃最高千元奖品吗</div>
            </div>

            <div className={cx('back-bg')}>
                <div className={cx('back-subtitle')}>预约直播100%抽好礼</div>
                <div className={cx('back-gift-img')}></div>
            </div>

            <div className={cx('back-btns')}>
                <button className={cx('back-cancel')}  onClick={handleCancel}></button>
                <button className={cx('back-confirm')} onClick={handleOk}></button>
            </div>
        </div>
    );
};

export default BackDialog;
