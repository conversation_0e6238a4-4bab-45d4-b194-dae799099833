.back-wrap {
    width: 100%;
    height: 100vh;
    position: fixed;
    display: flex;
    flex-direction: column;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, .8);
    overflow: hidden;
    z-index: 5;

    .back-title {
        width: 100%;
        margin: 1.08rem auto 0;

        .back-close {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        .back-close-icon {
            margin-right: .367rem;
            width: .24rem;
            height: .24rem;
            background: url('./assets/img/close-icon.png') center/contain no-repeat;
        }

        .back-dialog-title {
            margin-top: .43rem;
            font: 600 .3333rem/.344rem "PingFang SC";
            text-align: center;
            background: linear-gradient(180deg, #FFFEF1 0%, #FDF0B9 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }

    .back-bg {
        width: 3rem;
        height: 3.36rem;
        margin: .4267rem auto 0;
        display: flex;
        flex-direction: column;
        background: url('./assets/img/back-bg.png') center/contain no-repeat;

        .back-subtitle {
            margin-top: .4367rem;
            color: #FF4E4F;
            font: 500 .1933rem/.1933rem "PingFang SC";
            text-align: center;
        }

        .back-gift-img {
            margin: .54rem auto 0;
            width: 1.52rem;
            height: 1.6067rem;
            background: url('../assets/img/temp_gift.png') center/contain no-repeat;
        }
    }

    .back-btns {
        width: 3.053rem;
        height: .59rem;
        display: flex;
        margin: .43rem auto 0;
        justify-content: space-between;
        align-items: center;

        .back-cancel {
            width: 1.2rem;
            height: .5rem;
            background: url('./assets/img/cancel-btn.png') center/contain no-repeat;
        }

        .back-confirm {
            width: 1.7667rem;
            height: .533rem;
            background: url('./assets/img/confirm-btn.png') center/contain no-repeat;
        }
    }
}
