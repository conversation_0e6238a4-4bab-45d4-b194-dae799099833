/**
 * @file 活动预约页非首屏内容
 * @abstract 1. 最新资讯；2. 往期精彩回顾；3. 节目单（可选）4.联合出品方
 * <AUTHOR>
 */

import React, {useMemo} from 'react';
import RelatedVideo from '../common/RelatedVideo';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import Guests from '../common/Guests';
import ModuleFrame from '../common/ModelFrame';
import cls from 'classnames/bind';
import styles from './index.module.less';

const cx = cls.bind(styles);

const FollowingPart = props => {
    const {
        feedbackUrl,
        modules,
        acknowledge,
        entranceAPP,
        isLogin,
        handleToast,
        materialConfig
    } = props;

    const moduleList = modules;

    // 使用 useMemo 来缓存模块列表的 JSX 元素
    const renderedModules = useMemo(() => {
        return moduleList.map((item, index) => {
            return (
                <ModuleFrame
                    titleBg={materialConfig?.[`module_frame_title_bg_${index}`]}
                    titleHeight={'0.467rem'}
                    backgroundbg={materialConfig?.module_frame_body_bg}
                    bottomBg={materialConfig?.module_frame_bottom_bg}
                    bottomHeight={'0.333rem'}
                    key={index}
                    item={item}
                >
                    {
                        // 用户列表
                        +item?.type === 1 && +item?.enable === 1 && (
                            <Guests
                                isLogin={isLogin}
                                list={item?.list}
                                circle={materialConfig?.guests_avatar_circle}
                                handleToast={handleToast}
                            />
                        )
                    }
                    {
                        // 视频列表
                        +item?.type === 2 && +item?.enable === 1 && (
                            <RelatedVideo
                                canSlide={item?.layout === 'horizontal'}
                                list={item?.list}
                                jumpScheme={item?.jump_scheme}
                            />

                        )
                    }
                    {
                        // 节目单
                        +item?.type === 3 && +item?.enable === 1 && (
                            <RelatedVideo
                                // list={relatedVideoList}
                            />
                        )
                    }

                </ModuleFrame>
            );
        });
    }, []); // 依赖项列表

    return (
        <div
            className={cx('common-music-cover2', !entranceAPP && 'out-app')}
            style={{
                backgroundImage: `url('${materialConfig?.following_part_bg}')`,
                marginTop: `${materialConfig?.following_part_bg_margin_top}rem`
            }}
        >
            {renderedModules}
            <div className={cx('bottom-logo',!entranceAPP && 'no-show-program')}>
                <img src={acknowledge} alt='标题' decoding='sync' />
            </div>
        </div>
    );
};

export default FollowingPart;
