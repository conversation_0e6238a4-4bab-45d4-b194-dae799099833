/**
 * @file  toast 提示组件
 * <AUTHOR>
 */

import React, {useEffect, useRef, useState} from 'react';
import cls from 'classnames';
import {activityShowLog} from '@/pages/liveshow/log';
import style from './index.module.less';

const cx = cls.bind(style);

const contentMap = {
    'share-success': {
        tip: '分享成功'
    },
    'auto-subscribe-follow-success': {
        tip: '预约成功\n并关注百度直播\n快去抽奖吧',
        type: '1',
    },
    'auto-subscribe-success': {
        tip: '预约成功\n快去抽奖吧',
        type: '2',
    },
    'subscribe-success': {
        tip: '预约成功\n并关注百度直播',
        type: '1',
    },
    'auto-subscribe-fail': {
        tip: '预约直播\n可参与抽奖',
        type: '8',
    },
    'subscribe-fail': {
        tip: '预约失败\n请稍后再试',
        type: '8',
    },
    'support-fail': {
        tip: '活动火爆\n请稍后再试',
        type: '6',
    },
    'handle-success': {
        tip: '操作成功'
    },
    'lottery-fail': {
        tip: '活动火爆\n稍后再试'
    },
    'is-anti': {
        tip: '账号异常\n请稍后再试',
        type: '3',
    },
    'support-time-full': {
        tip: '你的助力次数\n已达上限',
        type: '4',
    },
    'support-same-person': {
        tip: '你只能为TA\n助力一次哦',
        type: '5',
    },
    'support-self': {
        tip: '不能为自己\n助力哦',
        type: '7',
    },
    'support-success': {
        tip: '助力成功',
    },
    'follow-success': {
        tip: '关注成功',
        type: '9',
    },
    'cancle-follow-success': {
        tip: '取消关注成功',
    },
    'save-img-fail': {
        tip: '保存图片失败\n请稍后再试'
    },
    'saving-img': {
        tip: '图片保存中'
    },
    'save-img-success': {
        tip: '已保存到相册'
    }
}

const Toast = ({toastType, toastMessage}) => {
    const toastInfo = toastMessage || contentMap[toastType || 'handle-success']?.tip;
    const logType = contentMap[toastType]?.type || 0;
    const toastRef = useRef(null);
    const [toastPosition, setToastPosition] = useState(0);

    useEffect(() => {
        const updatePosition = () => {
            if (toastRef.current) {
                const viewportHeight = window.innerHeight;
                const componentHeight = toastRef.current.offsetHeight;
                setToastPosition(
                    -toastRef.current.getBoundingClientRect().top +
                        (viewportHeight / 2) -
                        (componentHeight / 2)
                );
            }
        };
        updatePosition();
    }, [toastRef]);

    useEffect(() => {
        activityShowLog('subscribe_toast', 15263, {
            content_txt: toastInfo,
            content_type: logType,
        });
    }, []);

    return (
        <div
            className={cx('common-toast-wrap')}
            ref={toastRef}
            style={{
                top: `${toastPosition}px`,
            }}
        >
            <div className={cx('tip')}>
                {toastInfo}
            </div>
        </div>
    );
};

export default Toast;
