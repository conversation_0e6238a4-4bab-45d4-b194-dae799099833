/**
 * @file 活动预约页-端外助力页
 * <AUTHOR>
 */

import React, {useEffect, useCallback, useState, useRef} from 'react';
import {activityShowLog, activityClickLog} from '@/pages/liveshow/log';
import Button from '../common/Button';
import cls from 'classnames/bind';
import styles from './index.module.less';

const cx = cls.bind(styles);

const HelpPage = props => {
    const {handleInvoke, invitePage, materialConfig, shareCuk, hideInviter} = props;

    const invokeApp = useCallback(() => {
        activityClickLog('port_help_btn', 15263, {
            share_cuk: shareCuk
        });
        handleInvoke && handleInvoke();
    }, []);

    useEffect(() => {
        activityShowLog('port_help_btn', 15263, {
            share_cuk: shareCuk
        });
    }, []);

    return (
        <>
            <div
                className={cx('help-page-wrapper')}
                style={{
                    backgroundImage: `url('${materialConfig?.help_page_bg}')`,
                }}
                onClick={invokeApp}
            >
                {
                    // 隐藏邀请者信息
                    +hideInviter !== 1 && (
                        <>
                            <div
                                className={cx('help-page-avatar')}
                                style={{
                                    backgroundImage: `url('${invitePage?.avatar || {}}')`,
                                }}
                            >
                                <div
                                    className={cx('help-page-avatar-circle')}
                                    style={{
                                        backgroundImage: `url('${materialConfig?.guests_avatar_circle}')`,
                                    }}
                                ></div>
                            </div>
                            <div className={cx('help-page-name')}>{invitePage?.user_name}</div>
                        </>
                    )
                }
                <div
                    className={cx('help-page-gift')}
                    style={{
                        backgroundImage: `url('${materialConfig?.help_page_gift}')`,
                    }}
                ></div>
                <div className={cx('help-page-text')}></div>
                <div className='help-page-btn'>
                    <Button
                        btnText={'立即助力'}
                        width='2.34rem'// 按钮宽度
                        height='0.749rem' // 按钮高度
                        btnTextColor={materialConfig?.lotter_main_btn_font_color}
                        btnBg={materialConfig?.main_btn_outApp}
                        btnTextSize='0.321' // 按钮字体大小
                        hasBubble
                        bubbleWidth='1.584rem' // 气泡宽度
                        bubbleBg={materialConfig?.lotter_main_btn_bubble_bg_outApp} // 气泡背景
                        hasZoomIn={false}
                    />
                </div>
                <div className='company-outApp2'>百度在线网络技术（北京）有限公司 版本号13.77.0</div>
                <a className='policy-outApp2' href='https://s.bdstatic.com/common/agreement/privacy.html'>隐私政策和权限</a>
            </div>
        </>

    );
}

export default HelpPage;
