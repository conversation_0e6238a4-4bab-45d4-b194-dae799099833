.help-page-wrapper {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 8.95rem;
    background: #A9322F;
    background-position: center;
    background-size: contain;
    background-repeat: no-repeat;
}

.help-page-avatar {
    position: absolute;
    margin-top: 2.363rem;
    margin-left: 50%;
    transform: translateX(-50%);
    width: .605rem;
    height: .605rem;
    background-position: center;
    background-size: contain;
    background-repeat: no-repeat;
    border-radius: 50%;

    .help-page-avatar-circle {
        position: absolute;
        width: 100%;
        height: 100%;
        background-position: center;
        background-size: contain;
        background-repeat: no-repeat;
    }
}

.help-page-name {
    position: absolute;
    top: 3.06rem;
    left: 50%;
    transform: translateX(-50%);
    color: #000;
    text-align: center;
    font-family: "PingFang SC";
    font-size: .168rem;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.help-page-gift {
    width: 3.294rem;
    height: 3.433rem;
    margin: 2.623rem auto 0;
    background-position: center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.help-page-btn {
    margin: .747rem auto 0;
}

.company-outApp2 {
    position: relative;
    margin-top: .5rem;
    width: 100%;
    color: #D4645C;
    font-family: "PingFang SC";
    font-size: .12rem;
    font-style: normal;
    font-weight: 500;
    line-height: .15rem;
    text-align: center;
    z-index: 2;
}

.policy-outApp2 {
    position: relative;
    width: 100%;
    margin-top: .064rem;
    color: #D4645C;
    font-family: "PingFang SC";
    font-size: .12rem;
    font-style: normal;
    font-weight: 500;
    line-height: .15rem;
    text-align: center;
    text-decoration: underline;
    z-index: 2;
}
