/**
 * @file 弹窗组件封装
 * <AUTHOR>
 */

import React, {useCallback, useEffect, useState, useRef} from 'react';
import cls from 'classnames/bind';
import AfxConstructor from '@baidu/afx-video';
import Button from '../common/Button';
import {lock, unlock} from 'tua-body-scroll-lock';
// import {activityInfo1Mock} from '../../mockData';
// import chunwan from './assets/chunwan_yuyue.1219.mp4';
import styles from './index.module.less';

const cx = cls.bind(styles);

// window.orientation: 90 -90 横屏，但是已废弃
// screen.orientation.type 新属性，但是安卓浏览器不支持
const isLandscape = (window.screen.orientation?.type === 'landscape-primary')
    || (window.screen.orientation?.type === 'landscape-secondary')
    || (window.orientation === 90)
    || (window.orientation === -90);

// 兼容折叠屏/ipad等设备
// (() => {
//     document.addEventListener('DOMContentLoaded', () => {
//         const screenWidth = window.innerWidth;
//         if (screenWidth > 500) {
//             // 高1366的屏幕上,fontSize设置190最合适,由此得出缩放比例
//             const scale = 1366 / 190;
//             const screenHeight = window.innerHeight;
//             const computerFontSize = screenHeight / scale;
//             setTimeout(() => {
//                 // 重设rem基准值
//                 document.documentElement.style.fontSize = computerFontSize + 'px';
//                 if (isLandscape) {
//                     const contentScale = 320 / screenHeight;
//                     document.documentElement.style.fontSize = (computerFontSize / contentScale) + 'px';
//                 }
//             }, 0);
//         }
//     });
// })();

const afxInstance = new AfxConstructor({
    // 设置xhr下载mp4超时时间
    timeout: 2000
});

// https://baidu-rmb-mvideofe-h5-static.cdn.bcebos.com/packet_gaokao24/packet_afx_0507.mp4

const videoMap = {
    main: 'https://ala-gift.cdn.bcebos.com/gift/2024-12/1735205040043/yuyue_1226.mp4'
};

const RedPacketWrapper = ({
    outerText = '',
    subOuterText = '',
    outerTextCustom,
    children,
    titleBgClassName = '',
    activeWrapClassName = '',
    bgClassName = '',
    popBgImg = '',
    bgType = 1,
    btnAreaShow = false,
    doubleBtnAreaShow = false,
    btnText = '',
    btnBg = '',
    BtnAreaCustom,
    onClick,
    savePhoto,
    onClose = () => {},
    closeHidden = false,
    outTextBgAnimationShow = true,
    bgAnimationShow = true,
    NextAutoOpenComponent,
    onAnimationEnd = () => {},
    resultVisible,
    materialConfig,
    closeOnMaskClick = false
}) => {
    const closeWebviewFn = useRef();
    const closeWebview = useCallback(async () => {
        await onClose();
        // TODO: 关闭弹窗
    }, [onClose]);

    closeWebviewFn.current = closeWebview;

    const stopPropagation = useCallback((e) => {
        e.stopPropagation();
    }, []);

    const [animationStatus, setAnimationStatus] = useState(false);
    const [showMain, setShowMain] = useState(false);

    const handleAfx = useCallback(() => {
        window.console.log('handleAfx', videoMap.main);
        afxInstance
            .load({
                videoMap,
                progress(progress) {
                    window.console.log('下载进度', progress);
                }
            })
            .then(() => {
                // 小春晚直播间背景动画
                afxInstance.play({
                    container: '#videoAnimation', // 需要dom中存在此节点
                    path: videoMap.main,
                    loop: true, // 是否循环播放
                    videoPlayProcess(videoTime) {
                        // console.log('视频播放进度', videoTime);
                        if (+videoTime > .1 && !animationStatus) {
                            setAnimationStatus(true);
                            setShowMain(true);
                        }
                    },
                    end() {
                        // console.log('视频播放结束');
                        setAnimationStatus(true);
                        setShowMain(true);
                    },
                    unExpectErrorCb() {
                        setAnimationStatus(true);
                        setShowMain(true);
                    }
                }).then(() => {
                    console.log('视频播放成功');
                }).catch(e => {
                    console.log('视频播放失败', e);
                    setAnimationStatus(true);
                    setShowMain(true);
                });
            })
            .catch(err => {
                console.log('load err', err);
                setShowMain(true);
                setAnimationStatus(true);
            });
    }, [animationStatus]);

    useEffect(() => {
        handleAfx();
    }, [handleAfx]);

    const redPacketBgDom = useRef();

    const redPacketContentDom = useRef();
    useEffect(() => {
        if (redPacketContentDom.current) {
            if (isLandscape) {
                redPacketContentDom.current.style.marginTop = '0px';
            }
        }
    }, [bgType]);

    // 滚动穿透fix
    useEffect(() => {
        lock();
        return () => {
            unlock();
        };
    }, []);

    return (
        <div
            className={cx('active-wrap', {
                [activeWrapClassName]: !!activeWrapClassName
            })}
            onClick={stopPropagation}
            onAnimationEnd={onAnimationEnd}
        >
            {
                closeOnMaskClick && !closeHidden && (
                    <div
                        className={cx('close-mask')}
                        onTouchStart={closeWebview}
                    ></div>
                )
            }
            {
                showMain && (
                    <div
                        className={cx('red-packet-content')}
                        ref={redPacketContentDom}
                    >
                        {!closeHidden && <div
                            className={cx('close', {
                                'result-visible': resultVisible // 抽奖结果显示后，再显示关闭按钮
                            })}
                            onClick={closeWebview}
                        ></div>}
                        {
                            (outerText || outerTextCustom) && <div className={cx({
                                'text-bg': true,
                                ['red-packet-outer-title-zoom-in']: true,
                                [titleBgClassName]: !!titleBgClassName
                            })}
                            >
                                {
                                    outerTextCustom ? outerTextCustom : (
                                        <>
                                            <p className={cx('text', {
                                            ['red-packet-outer-title-zoom-in']: outTextBgAnimationShow
                                            })}
                                            >{outerText}</p>
                                            {subOuterText && <p className={cx('subText')}>{subOuterText}</p>}
                                        </>
                                    )
                                }
                            </div>
                        }
                        <div
                            className={cx({
                                'base-red-packet-bg': true,
                                'init-money-red-packet-bg': bgType === 1, // 抽奖结果弹窗
                                'money-red-packet-bg': bgType === 2, // 红包弹窗
                                'poster-popup-packet-bg': bgType === 3, // 海报弹窗
                                'back-popup-packet-bg': bgType === 4, // 挽留弹窗
                                'red-packet-active-animation': true, // 弹窗入场动画
                                [bgClassName]: !!bgClassName
                            })}
                            // style={{backgroundImage: `url("${popBgImg}")`}}
                            ref={redPacketBgDom}
                        >
                            {children}
                        </div>
                        {
                            btnAreaShow && (
                                <Button
                                    btnText={btnText}
                                    width={'2.39rem'}
                                    height={'0.76rem'}
                                    btnBg={btnBg}
                                    onClick={onClick}
                                    style={{marginTop: '0.263rem'}}
                                />
                            )
                        }
                        {
                            doubleBtnAreaShow && (
                                <div
                                    className={cx('double-btn-area', {
                                        // 'delay-show': !!tplId && resultVisible
                                    })}
                                >
                                    <Button
                                        width={'1.59rem'}
                                        height={'0.51rem'}
                                        btnBg={'https://ala-gift.cdn.bcebos.com/gift/2024-12/1734603670068/save_photo.png'}
                                        onClick={savePhoto}
                                    />
                                    <Button
                                        btnText={'点击抽奖'}
                                        width={'1.59rem'}
                                        height={'0.51rem'}
                                        btnTextColor={materialConfig?.lotter_main_btn_font_color}
                                        btnBg={materialConfig?.lotter_main_btn_bg}
                                        onClick={onClick}
                                        hasBubble
                                        bubbleWidth={'0.896rem'} // 气泡宽度
                                        bubbleBg={'https://ala-gift.cdn.bcebos.com/gift/2024-12/1734600746354/poster_bubble.png'} // 气泡背景
                                    />
                                </div>
                            )
                        }
                        {BtnAreaCustom}
                        {NextAutoOpenComponent}
                    </div>
                )
            }
            {
                bgAnimationShow && animationStatus && (

                    <div id="videoAnimation" className={cx('enter-afx-player')} />
                )
            }
        </div>
    );
};

export default RedPacketWrapper;
