.active-wrap {
    width: 100%;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    background-color: rgba(0, 0, 0, .8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 99;

    .close-mask {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: 5;
    }

    .close {
        position: absolute;
        width: .27298rem;
        height: .27298rem;
        top: -1rem;
        right: 0;
        background: url(./assets/close.png) no-repeat top left / 100%;
        z-index: 10;
    }

    .red-packet-content {
        position: relative;
        flex-shrink: 0;
        width: 3.16rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        font-family: PingFang SC;
        z-index: 10;

        .text-bg {
            width: 3.113rem;
            height: .73rem;
            background: url(./assets/text-bg.png) no-repeat top left / 100%;
            position: absolute;
            top: -.8rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .red-packet-outer-title-zoom-in {
            animation: zoomIn .3s cubic-bezier(.33, 0, .18, 1) both;
        }

        .text {
            text-align: center;
            font-family: FZZhengHeiS-EB-GB;
            font-size: .333rem;
            font-style: normal;
            font-weight: 400;
            line-height: .344rem; /* 128.571% */
            background: linear-gradient(180deg, #FFDFA1 0%, #FFFCE0 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .subText {
            text-align: center;
            font-family: PingFang SC;
            font-size: .167rem;
            font-style: normal;
            font-weight: 400;
            line-height: .3rem; /* 128.571% */
            background: linear-gradient(180deg, #FFFFFC 0%, #FFF4B7 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .base-red-packet-bg {
            width: 2.89rem;
            height: 3.98rem;
            background: url('./assets/init-bg.png') no-repeat top left;
            background-size: 100% 100%;
            text-align: center;
            position: relative;

            &.money-red-packet-bg {
                width: 2.726rem;
                height: 3.4rem;
                background-image: url('./assets/redpacket.png');
            }

            &.poster-popup-packet-bg {
                width: 3.2rem;
                height: 5.296rem;
                background-image: url('./assets/yuyue_poster.png');
            }

            &.init-money-red-packet-bg {
                width: 3.003rem;
                height: 3.16rem;
                background-image: url('./assets/init-bg.png');
                margin-top: .45rem;
            }

            &.back-popup-packet-bg {
                width: 2.953rem;
                height: 3.121rem;
                background-image: url('./assets/init-bg.png');
                margin-top: .2rem;
            }

            &.red-packet-active-animation {
                animation: zoomInAndRotate .88s linear both;
            }
        }

        .btn-area {
            width: 2.4rem;
            height: .7rem;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;

            .btn-primary {
                width: 100%;
                height: 100%;
                background: url('https://mpics.bdstatic.com/qm/202411/pic_pGTucL_1731929167.png') no-repeat center / contain;
                font-size: .2rem;
                font-weight: 600;
                display: flex;
                align-items: center;
                justify-content: center;

                .btn-primary-text {
                    font-family: FZZhengHeiS-EB-GB;
                    background: linear-gradient(180deg, #FF6421 0%, #FF0F00 100%);
                    background-clip: text;
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                }
            }
        }

        .double-btn-area {
            position: absolute;
            bottom: -.8rem;
            width: 3.4rem;
            display: flex;
            align-items: center;
            justify-content: space-between;

            &.delay-show {
                animation: fadeIn 0s ease-in .8s both;
            }

            .save-photo {
                width: 1.593rem;
                height: .51rem;
                background: url('./assets/save_photo.png') no-repeat center;
                background-size: 100% 100%;
            }
        }
    }

    .enter-afx-player {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 2;
    }

    @keyframes zoomIn {
        from {
            transform: scale(0);
        }

        to {
            transform: scale(1);
        }
    }

    @keyframes zoomInAndRotate {
        0% {
            animation-timing-function: linear;
            transform: scale(0);
        }

        15.53% {
            animation-timing-function: linear;
            transform: scale(.31) rotate3d(0, 1, 0, -90deg);
        }

        34.226% {
            animation-timing-function: linear;
            transform: scale(.69) rotate3d(0, 1, 0, -180deg);
        }

        54.5% {
            animation-timing-function: linear; // cubic-bezier(.17, 0, .83, 1)
            transform: scale(1.1) rotate3d(0, 1, 0, -360deg);
        }

        70.25% {
            animation-timing-function: linear;
            transform: scale(1) rotate3d(0, 1, 0, -360deg);
        }

        85.4% {
            animation-timing-function: linear;
            transform: scale(1.05) rotate3d(0, 1, 0, -360deg);
        }

        100% {
            animation-timing-function: linear;
            transform: scale(1) rotate3d(0, 1, 0, -360deg);
        }
    }
}
