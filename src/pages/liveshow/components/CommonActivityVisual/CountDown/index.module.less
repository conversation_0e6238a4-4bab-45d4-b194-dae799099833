@import '~@baidu/nano-theme/index.less';
@import '../assets/style.module.less';

.theme-root() !important;

.count-down-inApp {
    position: absolute;
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: nowrap;
    // justify-content: space-between;
    z-index: 3;

    &.count-down-outApp {
        justify-content: center;
    }

    .count-text-common {
        position: relative;
        width: .361rem;
        height: .386rem;
        margin-right: .101rem;
        background-position: center;
        background-size: 100% 100%;
        background-repeat: no-repeat;

        &.count-text-common-outApp {
            width: .435rem;
            height: .461rem;
            margin-right: .1193rem;
        }
    }

    .count-content-common {
        display: flex;
        flex-wrap: nowrap;
        flex-direction: row;

        .countDownTimeNew {
            width: .1825rem;
            height: .336rem;
            border-radius: .0464rem;
            background: linear-gradient(0deg, #FFF1D6 -7.02%, #FFE298 109.65%);
            font-family: "baidunumber-Medium";
            font-size: .32rem;
            font-weight: 500;
            line-height: .336rem;
            margin-left: .0168rem;

            &.is-android {
                line-height: normal;
            }
        }

        .countDownUnitNew {
            text-align: center;
            font-family: "PingFang SC";
            font-size: .14rem;
            font-style: normal;
            font-weight: 600;
            line-height: .1933rem; /* 171.429% */
            margin-top: .1rem;
            margin-left: .044rem;
            margin-right: .0272rem;
        }
    }

    .count-content-common-outApp {
        display: flex;
        flex-wrap: nowrap;
        flex-direction: row;

        .countDownTimeNew {
            width: .219rem;
            height: .401rem;
            border-radius: .05rem;
            background: linear-gradient(0deg, #FFF1D6 -7.02%, #FFE298 109.65%);
            font-family: "baidunumber-Medium";
            font-size: .403rem;
            font-weight: 500;
            line-height: .403rem;
            margin-left: .0168rem;

            &.is-android {
                line-height: normal;
            }
        }

        .countDownUnitNew {
            text-align: center;
            font-family: "PingFang SC";
            font-size: .151rem;
            font-style: normal;
            font-weight: 600;
            line-height: .1933rem; /* 171.429% */
            margin-top: .1rem;
            margin-left: .044rem;
            margin-right: .0272rem;
        }
    }

    .common-no-count-down-waiting-text-inApp {
        width: 100%;
        font-family: "FZZhengHeiS-EB-GB";
        white-space: pre-wrap;
        text-align: center;
        font-weight: 400;
        letter-spacing: .01rem;
    }

    .common-no-count-down-waiting-text-inApp-noWrap {
        line-height: .4033rem;
    }

    .common-no-count-down-waiting-text-outApp {
        line-height: normal;
    }

    .common-no-count-down-waiting-text-outApp-noWrap {
        white-space: nowrap;
    }
}
