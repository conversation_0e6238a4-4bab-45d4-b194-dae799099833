/**
 * @file 倒计时组件
 */

import React, {useCallback, useEffect, useRef, useState} from 'react';
import {useUpdateEffect} from 'ahooks';
import {getEndStatus} from '@/pages/liveshow/service/api';
import {env} from '@/utils/env';
import {registerPageHidden} from '@/utils/index';
import MultiChat, {MessageType} from '@/pages/liveshow/utils/multiChat';
import cls from 'classnames';
import styles from './index.module.less';

const cx = cls.bind(styles);

const minuteSecond = 60;
const hourSecond = minuteSecond * 60;

function CountDownTimeStr({timestamp = 0, entranceAPP, materialConfig}) {
    if (timestamp === null) {
        return '---';
    }
    if (timestamp < 0) {
        timestamp = 0;
    }
    const timeArray = [
        {
            unit: '时',
            secondCount: hourSecond
        },
        {
            unit: '分',
            secondCount: minuteSecond
        },
        {
            unit: '秒',
            secondCount: 1
        }
    ];
    let time = Math.ceil(timestamp);
    let resultArray = timeArray.map((timeItem) => {
        const count = Math.floor(time / timeItem.secondCount);
        time -= count * timeItem.secondCount;
        return {
            count: count.toString().length < 2 ? `0${count}` : count,
            ...timeItem
        };
    });
    return resultArray.reduce((pre, result, index) => {
        if (pre === '') {
            return [
                <span
                    className={cx('countDownTimeNew', env.isAndroid && 'is-android')}
                    key={`${index}-1`}
                    style={{
                        color: materialConfig?.lottery_countdown_font_color
                    }}
                >
                    {result.count.toString()[0]}
                </span>,
                <span
                    className={cx('countDownTimeNew', env.isAndroid && 'is-android')}
                    key={`${index}-3`}
                    style={{
                        color: materialConfig?.lottery_countdown_font_color
                    }}
                >
                    {result.count.toString()[1]}
                </span>,
                <span
                    className={cx('countDownUnitNew')}
                    key={`${index}-2`}
                    style={{
                        color: entranceAPP
                            ? materialConfig?.lottery_countdown_unit_font_color_inApp
                            : materialConfig?.lottery_no_countdown_font_color_outApp,
                        marginRight: '0rem'
                    }}
                >
                    {result.unit}
                </span>
            ];
        }

        return pre.concat([
            [
                <span
                    className={cx('countDownTimeNew', env.isAndroid && 'is-android')}
                    key={`${index}-1`}
                    style={{
                        color: materialConfig?.lottery_countdown_font_color
                    }}
                >
                    {result.count.toString()[0]}
                </span>,
                <span
                    className={cx('countDownTimeNew', env.isAndroid && 'is-android')}
                    key={`${index}-3`}
                    style={{
                        color: materialConfig?.lottery_countdown_font_color
                    }}
                >
                    {result.count.toString()[1]}
                </span>,
                <span
                    className={cx('countDownUnitNew')}
                    key={`${index}-2`}
                    style={{
                        color: entranceAPP
                            ? materialConfig?.lottery_countdown_unit_font_color_inApp
                            : materialConfig?.lottery_no_countdown_font_color_outApp
                    }}
                >
                    {result.unit}
                </span>
            ]
        ]);
    }, '');
}

function CountDown(props) {
    const {entranceAPP, showStartTime, materialConfig} = props;

    const [countdown, setCountdown] = useState(() => {
        return +props.countdown < 0 ? 0 : +props.countdown;
    });
    const [status, setStatus] = useState(-1);
    const refreshInterval = useRef(null);
    const [noStart, setNoStart] = useState(false); // 倒计时结束，直播未开始状态
    const [noCountDownText, setNoCountDownText] = useState('');
    const timeOut = useRef(null);
    const chat = useRef(new MultiChat());
    const chatReliable = useRef(new MultiChat());
    const [chatLock, setChatLock] = useState(false);
    const liveUpdateLock = useRef(false);
    const pageOpenTime = new Date().getTime();

    const update = useCallback(() => {
        return getEndStatus().then((data) => {
            // -1 表示直播还未开始,继续倒计时
            const newStatus = +data.status;
            // 提前开播手动设置5s轮询；延迟开播，按照接口返回时间设置倒计时
            const count = liveUpdateLock.current === true ? 5 : +data.countdown < 0 ? 0 : +data.countdown;
            const show = (newStatus === -1 && count <= 0) || newStatus === 0;
            props.setShowLiveBtn && props.setShowLiveBtn(!show);
            setNoStart(show);

            setStatus(newStatus); // 更新状态
            setCountdown(count); // 更新倒计时

            if (newStatus !== -1) {
                // 直播开始，停止定时 refresh
                clearTimeout(refreshInterval.current);
                props.refresh && props.refresh();
            }
        });
    }, [props]);

    useEffect(() => {
        if (showStartTime === 0) {
            setNoCountDownText(materialConfig?.lottery_no_countdown_text_1);
            return;
        }
        setNoCountDownText(noStart
            ? materialConfig?.lottery_no_countdown_text_3
            : materialConfig?.lottery_no_countdown_text_2);
    }, []);

    useEffect(() => {
        update();
    }, []);

    useEffect(() => {
        // 倒计时大于30分钟，或已建立消息通道，不再建立IM消息通道
        if (countdown >= 60 * 30 + 5 || chatLock === true) {
            return;
        }
        setChatLock(true);
    }, [countdown]);

    useUpdateEffect(() => {
        // 建立IM消息必达通道
        chatReliable.current.start({
            status: props.status,
            chatID: props.reliableMcastId
        });

        // 建立IM消息普通通道，延迟3秒建立
        setTimeout(() => {
            chat.current.start({
                status: props.status,
                chatID: props.chatMcastId
            });
        }, 3000);

        const min = 3;
        const max = 10;

        const handleLiveStart = () => {

            // 随机延迟3-10秒触发直播开始事件，打散用户触发时机
            const randomTime = Math.floor(Math.random() * (max - min + 1)) + min;

            setTimeout(() => {
                liveUpdateLock.current = true;
                update();
            }, randomTime * 1000);
        };
        // 监听im消息
        chatReliable.current.on(MessageType.LIVE_START, handleLiveStart);
        chat.current.on(MessageType.LIVE_START, handleLiveStart);

        return () => {
            chatReliable.current.off(MessageType.LIVE_START, handleLiveStart);
            chat.current.off(MessageType.LIVE_START, handleLiveStart);
        };
    }, [chatLock]);


    useEffect(() => {
        registerPageHidden(
            () => {},
            () => {
                const leaveTime = Math.floor((new Date().getTime() - pageOpenTime) / 1000);
                const resetCountDown = +props.countdown - leaveTime;
                setCountdown(resetCountDown > 0 ? resetCountDown : 0);
            }
        );
    }, []);

    useEffect(() => {
        if (countdown > 0) {
            timeOut.current = setTimeout(() => {
                setCountdown(countdown - 1);
            }, 1000);
        }
        else if (status === -1) {
            update();
            // 倒计时结束，开始每三秒请求一次直播状态
            refreshInterval.current = setInterval(() => {
                update();
            }, 3000);
        }

        // 清除计时器
        return () => {
            clearTimeout(timeOut.current);
            clearInterval(refreshInterval.current);
        };
    }, [countdown, status, update]);

    const hasCount = countdown <= 24 * 60 * 60 && countdown > 0 && !noStart; // 24小时的倒计时

    return (
        <React.Fragment>
            <div
                className={cx('count-down-inApp', {
                    'count-down-outApp': !entranceAPP
                })}
                style={
                    entranceAPP ? {
                        width: `${materialConfig?.lottery_countdown_width_inApp}rem`,
                        marginTop: `${materialConfig?.lottery_countdown_margin_top_inApp}rem`,
                        marginLeft: `${materialConfig?.lottery_countdown_margin_left_inApp}rem`
                    } : {
                        width: `${materialConfig?.lottery_countdown_width_outApp}rem`,
                        marginTop: `${materialConfig?.lottery_countdown_margin_top_outApp}rem`,
                        marginLeft: `${materialConfig?.lottery_countdown_margin_left_outApp}rem`
                    }
                }
            >
                {
                    !hasCount ? <div
                        className={cx('common-no-count-down-waiting-text-inApp', {
                            // eslint-disable-next-line max-len
                            'common-no-count-down-waiting-text-inApp-noWrap': entranceAPP && showStartTime === 1 && !noStart,
                            'common-no-count-down-waiting-text-outApp': !entranceAPP,
                            // eslint-disable-next-line max-len
                            'common-no-count-down-waiting-text-outApp-noWrap': !entranceAPP && showStartTime === 1 && !noStart
                        })}
                        style={{
                            color: entranceAPP
                                ? materialConfig?.lottery_no_countdown_font_color
                                : materialConfig?.lottery_no_countdown_font_color_outApp,
                            fontSize: `${entranceAPP
                                ? materialConfig?.lottery_no_countdown_font_size
                                : materialConfig?.lottery_no_countdown_font_size_outApp
                            }rem`
                        }}
                    >{noCountDownText}</div>
                        : <>
                            <div
                                className={cx('count-text-common', {
                                    'count-text-common-outApp': !entranceAPP
                                })}
                                style={{
                                    backgroundImage: `url(${entranceAPP
                                        ? materialConfig?.lottery_countdown_icon_inApp
                                        : materialConfig?.lottery_countdown_icon_outApp
                                    })`
                                }}
                            ></div>
                            <div
                                className={cx(entranceAPP ? 'count-content-common' : 'count-content-common-outApp')}
                            >
                                {hasCount && <CountDownTimeStr
                                    timestamp={countdown}
                                    entranceAPP={entranceAPP}
                                    materialConfig={materialConfig}
                                />}
                            </div>
                        </>
                }
            </div>

        </React.Fragment>
    );
}

export default CountDown;
