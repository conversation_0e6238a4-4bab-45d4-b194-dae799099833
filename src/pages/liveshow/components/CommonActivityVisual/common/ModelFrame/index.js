/**
 * @file 活动预约页模块框架
 * @abstract 非首屏模块框架
 * <AUTHOR>
 */

import React, {useRef, useEffect, useState} from 'react';
import {activityShowLog} from '@/pages/liveshow/log';
import cls from 'classnames/bind';
import styles from './index.module.less';

const cx = cls.bind(styles);

const ModuleFrame = props => {
    const {
        titleBg,
        titleHeight,
        backgroundbg,
        bottomBg,
        bottomHeight,
        children,
        item
    } = props;

    const wrapperRef = useRef(null);
    const [isVisible, setIsVisible] = React.useState(false);

    useEffect(() => {
        const observerOptions = {
            root: null, // 使用视口作为根元素
            rootMargin: '0px',
            threshold: 0.5 // 当元素50%以上进入视口时触发
        };

        let module_type;
        switch (item?.type) {
            case 1:
                module_type = 'guests_team';
                break;
            case 2:
                module_type = item?.layout === 'horizontal' ? 'newest_news' : 'past_review';
                break;
            case 3:
                module_type = 'program';
                break;
            default:
                return '';
        }

        const observerCallback = (entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !isVisible) {
                    setIsVisible(true); // 标记为可见
                    activityShowLog('module', 15263, {
                        content_type: module_type
                    })
                    observer.unobserve(entry.target); // 断开观察器，停止进一步的回调
                }
            });
        };

        const observer = new IntersectionObserver(observerCallback, observerOptions);

        if (wrapperRef.current) {
            observer.observe(wrapperRef.current);
        }

        // 清理函数，用于组件卸载时注销观察目标
        return () => {
            if (wrapperRef.current && observer) {
                observer.unobserve(wrapperRef.current);
                observer.disconnect(); // 断开观察器，释放资源
            }
        };
    }, [isVisible]);

    const [didLoadMainImage, setDidLoadMainImage] = useState(false);
    const [didLoadTitleImage, setDidLoadTitleImage] = useState(false);
    const [didLoadBottomImage, setDidLoadBottomImage] = useState(false);

    useEffect(() => {
        if (backgroundbg) {
            setDidLoadMainImage(false);
            const bgImage = new Image();
            bgImage.onload = () => {
                setDidLoadMainImage(true);
            };
            bgImage.src = backgroundbg;
        }
    }, [backgroundbg]);

    useEffect(() => {
        if (titleBg) {
            setDidLoadTitleImage(false);
            const titleImage = new Image();
            titleImage.onload = () => {
                setDidLoadTitleImage(true);
            };
            titleImage.src = titleBg;
        }
    }, [titleBg]);

    useEffect(() => {
        if (bottomBg) {
            setDidLoadBottomImage(false);
            const bottomImage = new Image();
            bottomImage.onload = () => {
                setDidLoadBottomImage(true);
            };
            bottomImage.src = bottomBg;
        }
    }, [bottomBg]);

    return (
        <div className={cx('module-frame-wrapper')} ref={wrapperRef}>
            {
                didLoadTitleImage && (
                    <div
                        className={cx('module-frame-title')}
                        style={{
                            backgroundImage: `url('${titleBg}')`,
                            height: titleHeight,
                        }}
                    ></div>
                )
            }
            {
                didLoadMainImage && (
                    <div
                        className={cx('module-frame-body')}
                        style={{
                            backgroundImage: `url('${backgroundbg}')`,
                        }}
                    >
                        {children}
                    </div>
                )
            }
            {
                didLoadBottomImage && (
                    <div
                        className={cx('module-frame-bottom')}
                        style={{
                            backgroundImage: `url('${bottomBg}')`,
                            height: bottomHeight,
                        }}
                    ></div>
                )
            }
        </div>
    );
}

export default ModuleFrame;
