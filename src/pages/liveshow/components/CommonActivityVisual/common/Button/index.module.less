@import url('../../assets/style.module.less');

.button-wrap {
    position: relative;
    font-size: 0;
    text-align: center;
}

.enter-bubble-wrap {
    position: absolute;
    left: 50%;
    top: -.25rem;
    transform: translateX(-50%);
}

.main-btn-bubble {
    position: absolute;
    margin-top: -.15rem;
    right: -.223rem;
    height: .3505rem;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    animation: bubble-zoomIn 1067ms 1;
    z-index: 2;
}

.enter-btn {
    width: 1.65rem;
    height: .53333rem;
    margin: .066rem auto 0;
    background-position: center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    z-index: 2;

    .enter-btn-content {
        display: flex;
        width: 1.65rem;
        height: .53333rem;
        justify-content: center;
        align-items: center;

        .enter-btn-countdown {
            font-family: "PingFang SC";
            background-clip: text !important;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }

    .main-btn-text {
        font-family: "FZZhengHeiS-EB-GB";
        font-size: .23rem;
        line-height: .24rem;
        font-weight: 400;
        white-space: pre-wrap;
        background: var(--background);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .enter-btn-subtext {
        margin-top: .057rem;
        font-family: "PingFang SC";
        font-size: .12rem;
        font-style: normal;
        font-weight: 400;
        line-height: .12rem;
    }
}

.enter-btn-animation {
    animation: btn-zoomIn 833ms 1;
}

.enter-btn-zoomIn-click {
    animation: btn-zoomIn 833ms 1, btn-click 2467ms 1 300ms;
}

.enter-btn-click {
    animation: btn-click 2467ms 1 300ms;
}

.click-hand {
    position: absolute;
    top: .06rem;
    right: -.35rem;
    width: 1.1rem;
    height: .86rem;
    z-index: 2;
}

@keyframes bubble-zoomIn {
    0% {
        transform: scale(0);
    }

    62.5% {
        transform: scale(0);
    }

    84.3% {
        transform: scale(1.1);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes btn-zoomIn {
    0% {
        transform: scale(0);
    }

    52% {
        transform: scale(0);
    }

    80% {
        transform: scale(1.1);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes btn-click {
    0% {
        transform: scale(1);
    }

    4.8% {
        transform: scale(1.1);
    }

    16.2% {
        transform: scale(1);
    }

    83.8% {
        transform: scale(1);
    }

    88.6% {
        transform: scale(1.1);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes download-move {
    0% {
        background-position-y: 0;
    }

    100% {
        background-position-y: .5rem;
    }
}
