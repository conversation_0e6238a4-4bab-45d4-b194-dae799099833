/**
 * @File         : 按钮组件（动画、气泡、 小手点击）
 * <AUTHOR> <EMAIL>
*/

import React, {useState, useEffect} from 'react';
import Lottie from 'react-lottie';
import cn from 'classnames/bind';
import animationData from '../../Lottery/lottie/hand.json';
import styles from './index.module.less';

const cx = cn.bind(styles);

const Button = props => {
    const {
        hasCountDown, // 是否开启倒计时
        countDownNum, // 倒计时时间
        onClick, // 点击事件
        onCountDownFinished, // 倒计时结束事件
        hasBubble, // 是否显示气泡
        bubbleWidth, // 气泡宽度
        bubbleBg, // 气泡背景
        hasClick, // 是否显示小手点击
        btnText, // 按钮文字
        btnTextSize, // 按钮文字大小
        btnBg, // 按钮背景
        btnTextColor, // 按钮文字颜色
        width = '1.65rem', // 按钮宽度
        height = '0.533rem', // 按钮高度
        iconFillType, // wx、zan
        style = {}, // 按钮样式
        animationStart = false, // 动画是否开始
        animationOnComplete, // 动画结束回调
        hasZoomIn = true, // 是否开启按钮zoomin动画
        hasSubText = false ,// 是否显示按钮副文案
        btnSubText, //  按钮副文案
    } = props;

    const guideHand = {
        loop: 1,
        autoplay: animationStart,
        animationData: animationData,
        delay: 2000,
        rendererSettings: {
          preserveAspectRatio: 'xMidYMid slice',
        },
    };

    const [seconds, setSeconds] = useState(countDownNum); // 倒计时

    useEffect(() => {
        let intervalId;
        if (seconds > 0) {
            intervalId = setInterval(() => {
                setSeconds(prevSeconds => {
                    if (prevSeconds > 1) {
                        return prevSeconds - 1;
                    }
                    clearInterval(intervalId);
                    hasCountDown && onCountDownFinished();
                    return 0; // 保持状态一致性，确保seconds为0
                });
            }, 1000);
        }
        return () => clearInterval(intervalId); // 组件卸载时清除定时器
    }, [hasCountDown, onCountDownFinished, seconds]);

    return (
        <div className={cx('button-wrap')} style={{...style}}>
            {
                hasBubble && (
                    <div
                        className={cx('main-btn-bubble')}
                        style={{
                            width: bubbleWidth,
                            backgroundImage: `url('${bubbleBg}')`,
                        }}
                    ></div>
                )
            }
            <div
                className={cx('enter-btn', {
                    'enter-btn-zoomIn-click': hasClick && animationStart && hasZoomIn,
                    'enter-btn-click': hasClick && animationStart && !hasZoomIn,
                    'enter-btn-animation': animationStart && !hasClick && hasZoomIn
                })}
                style={{
                    width,
                    height,
                    backgroundImage: `url('${btnBg}')`,
                }}
                onClick={onClick}
            >
                <div
                    className={cx('enter-btn-content')}
                    style={{
                        width,
                        height
                    }}
                >
                    <span
                        className={cx('main-btn-text enter-btn-main-text')}
                        style={{
                            '--background': btnTextColor,
                            fontSize: `${btnTextSize}rem`,
                            lineHeight: `${btnTextSize}rem`
                        }}
                    >
                        <div
                            className={cx('main-btn-text enter-btn-main-text')}
                        >{btnText}
                        </div>
                        {
                            hasCountDown && (<span className={cx('enter-btn-countdown')}>({seconds}s)</span>)
                        }
                        {
                            hasSubText && (
                                <div
                                    className={cx('main-btn-text enter-btn-subtext')}
                                >{btnSubText}
                                </div>
                            )
                        }
                    </span>
                </div>
            </div>
            {
                hasClick && (
                    <div className={cx('click-hand')}>
                        {
                            <Lottie options={guideHand} />
                        }
                    </div>
                )
            }
        </div>
    );
};

export default Button;
