/**
 * @file 嘉宾阵容列表
 * <AUTHOR>
 */
import React, {useEffect, useState} from 'react';
import cls from 'classnames/bind';
import styles from './index.module.less';

const cx = cls.bind(styles);

const Program = props => {

    return (
        <>
            <div
                className={cx('program-wrapper')}
                style={{
                    backgroundImage: `url(${props.program_bg})`,
                }}
            >
            </div>
        </>
    );
}

export default Program;
