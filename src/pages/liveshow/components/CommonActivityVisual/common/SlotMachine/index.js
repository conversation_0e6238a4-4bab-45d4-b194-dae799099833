/**
 * @file  歌会活动页-抽奖模块--老虎机
 * <AUTHOR>
 */

import React, {useCallback, useEffect, useState, useRef} from 'react';
import cls from 'classnames/bind';
import {carouselVerticalLottery, stopLotteryAtOneItem} from '../../animation';
import 'swiper/dist/css/swiper.min.css';
import {activityShowLog, activityClickLog} from '@/pages/liveshow/log';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import style from './index.module.less';

const cx = cls.bind(style);

const SlotMachine = props => {
    const {
        newVisual,
        giftItemBg,
        itemWidth,
        itemHeight,
        lotteryState = 0, // 0:复位 1：抽奖中 2：停止
        lotteryResult // 中奖结果 gift_id
    } = props;
    const prize = newVisual?.draw?.prize || [];
    const prizeList = [...prize];
    // 老虎机滚动条数
    const lineList = [{}, {}, {}];
    const positionSeek = [0, Math.ceil(prizeList.length / 2), prizeList.length - 1];
    const tweenList = useRef([]);
    const tweenList2 = useRef([]);

    useEffect(() => {
        initLottery();
    }, []);

    useEffect(() => {
        switch(lotteryState) {
            case 1:
                handleLottery();
                break;
            case 2:
                handleStop(lotteryResult);
                break;
            default:
                handleReset();
        }
    }, [lotteryState, lotteryResult]);

    const initLottery = () => {
        lineList.forEach((item, index) => {
            const lotteryListDom = document.getElementById(`lottery-list-${index}`);
            tweenList.current[index] = carouselVerticalLottery(lotteryListDom, 30 - index * 0.5);
        });
        initPosition();
    }

    // 初始化每个滚动条的位置
    const initPosition = () => {
        lineList.forEach((item, index) => {
            tweenList.current[index].seek(positionSeek[index]);
        });
    }

    const handleLottery = async () => {
        for (let index = 0; index < lineList.length; index++) {
            tweenList.current[index]?.timeScale(20);
            await new Promise(resolve => setTimeout(resolve, 400));
        }
    }

    const matchGiftIndex = (lotteryResult) => {
        const index = prizeList.findIndex(item => +item.id === lotteryResult);
        if (index === 0) {
            return prizeList.length;
        }
        return index;
    };

    const handleStop = async (lotteryResult) => {
        const giftIndext = matchGiftIndex(lotteryResult);
        const DomHeight = document.getElementById(`lottery-list-item-0`).clientHeight;

        for (let index = 0; index < lineList.length; index++) {
            const lotteryListDom = document.getElementById(`lottery-list-${index}`);
            if (tweenList.current[index]) {
                tweenList.current[index].kill();
                // eslint-disable-next-line max-len
                tweenList2.current[index] = stopLotteryAtOneItem(lotteryListDom, 0.5, giftIndext, DomHeight, adjustHeight());
            }
            await new Promise(resolve => setTimeout(resolve, 400));
        }
        setTimeout(() => {
            handleReset()
        }, 2000);
    }

    const adjustHeight = () => {
        let res = 0;
        const DomHeight = document.getElementById(`lottery-list-item-0`).clientHeight;
        const lotteryHeight = document.getElementById(`lottery-container`)?.clientHeight;
        if (lotteryHeight && lotteryHeight > DomHeight) {
            res = (lotteryHeight - DomHeight) * 0.5;
        }
        return res;
    }

    const handleReset = () => {
        for (let index = 0; index < lineList.length; index++) {
            if (tweenList2.current[index] || tweenList.current[index]) {
                tweenList2.current[index] && tweenList2.current[index].kill();
                tweenList.current[index]?.timeScale(1);
                tweenList.current[index].restart();
            }
            initPosition();
        }
    }

    return (
        <>
            <div className={cx('lottery-container')} id='lottery-container'>
                {
                    lineList.map((item, index) => {
                        return (
                            <div
                                id={`lottery-list-${index}`}
                                key={index}
                                className={cx('lottery-list')}
                            >
                                <div id={`lottery-animation-wrapper-${index}`}>
                                    {
                                        prizeList.map((item, index) => {
                                            return (
                                                <div
                                                    className={cx('lottery-list-item')}
                                                    key={index}
                                                    id={`lottery-list-item-${index}`}
                                                    style={{
                                                        width: itemWidth,
                                                        height: itemHeight,
                                                        backgroundImage: `url('${giftItemBg}')`
                                                    }}
                                                >
                                                    <div
                                                        className={cx('lottery-list-item-img')}
                                                        style={{
                                                            backgroundImage: `url(${item.image})`
                                                        }}
                                                    ></div>
                                                </div>
                                            );
                                        })
                                    }
                                </div>
                                <div id={`backup-lottery-animation-wrapper-${index}`}>
                                    {
                                        prizeList.map((item, index) => {
                                            return (
                                                <div
                                                    className={cx('lottery-list-item')}
                                                    key={index}
                                                    style={{
                                                        width: itemWidth,
                                                        height: itemHeight,
                                                        backgroundImage: `url('${giftItemBg}')`
                                                    }}
                                                >
                                                    <div
                                                        className={cx('lottery-list-item-img')}
                                                        style={{
                                                            backgroundImage: `url(${item.image})`
                                                        }}
                                                    ></div>
                                                </div>
                                            );
                                        })
                                    }
                                </div>
                            </div>
                        )
                    })
                }
            </div>
        </>
    );
};

export default SlotMachine;
