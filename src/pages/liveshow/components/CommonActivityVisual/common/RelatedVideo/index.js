/**
 * @file 活动预约页视频展示模块
 * @abstract canSlide参数配置该模块是否能够滑动
 * <AUTHOR>
 */

import React, {useEffect, useState} from 'react';
import {activityShowLog, activityClickLog} from '@/pages/liveshow/log';
import cls from 'classnames/bind';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import styles from './index.module.less';

const cx = cls.bind(styles);

const RelatedVideo = props => {
    const {canSlide = false, list = [], jumpScheme} = props;

    // 点击跳转视频落地页
    const handleVideoClick = (scheme) => {
        boxjsUtils.invokeSchema({schema: scheme});
    };

    // 跳转百度直播号主页
    const handleLinkClick = () => {
        activityClickLog('b_check_more', 15263);
        boxjsUtils.invokeSchema({schema: jumpScheme});
    }

    const RelatedVideoItem = ({item}) => {
        const [didLoadCoverImage, setDidLoadCoverImage] = useState(false);

        useEffect(() => {
            if (item?.cover) {
                setDidLoadCoverImage(false);
                const coverImage = new Image();
                coverImage.onload = () => {
                    setDidLoadCoverImage(true);
                };
                coverImage.src = item?.cover;
            }
        }, [item?.cover]);

        useEffect(() => {
            activityShowLog('module_resource', 15263, {
                nid: item?.nid,
                content_type: canSlide ? 'newest_news' : 'past_review',
            });
        }, []);
        return (
            <div className={cx('video-item-wrapper', {
                'video-item-wrapper-can-slide': canSlide,
            })}>
                {
                    didLoadCoverImage && (
                        <>
                            <div
                                className={cx('video-item-cover')}
                                style={{
                                    backgroundImage: `url('${item?.cover || {}}')`,
                                }}
                                onClick={() => {
                                    activityClickLog('module_resource', 15263, {
                                        nid: item?.nid,
                                        content_type: canSlide ? 'newest_news' : 'past_review',
                                    });
                                    handleVideoClick(item?.scheme);
                                }}
                            >
                                <div className={cx('video-item-play-icon')}></div>
                            </div>
                        </>
                    )
                }
                <div className={cx('video-item-desc')}>{item?.title}</div>
            </div>
        );
    };

    return (
        <>
            <div
                className={cx('related-video-wrapper', {
                    'can-slide': canSlide,
                })}
            >
                {
                    list.map((item, index) => {
                        return (
                            <RelatedVideoItem
                                item={item}
                                key={index}
                            />
                        );
                    })
                }
            </div>
            {
                !canSlide && <div
                    className={cx('related-video-link')}
                    onClick={handleLinkClick}
                >
                    去<span>百度直播</span>查看更多
                    <div className={cx('related-video-link-arrow')}>
                        <div className={cx('related-video-link-arrow-1')}></div>
                        <div className={cx('related-video-link-arrow-2')}></div>
                    </div>
                </div>
            }
        </>
    );
}

export default RelatedVideo;
