.related-video-wrapper {
    width: 3.66rem;
    display: flex;
    flex-wrap: wrap;
    margin: 0 auto;
}

.can-slide {
    flex-wrap: nowrap;
    overflow-x: auto;

    &::-webkit-scrollbar {
        display: none;
    }

    -ms-overflow-style: none;
    scrollbar-width: none;
}

.video-item-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 1.14rem;
    height: 1.94rem;
    flex: 0 0 calc(33.333% - .24rem);
    box-sizing: border-box;
    margin-bottom: .167rem;
    margin-right: .12rem;

    &:nth-child(3n+3) {
        margin-right: 0;
    }
}

.video-item-wrapper-can-slide {
    margin-bottom: 0;

    &:nth-child(3n+3) {
        margin-right: .12rem;
    }

    &:last-child {
        margin-right: 0;
    }
}

.video-item-cover {
    width: 1.14rem;
    height: 1.5rem;
    border-radius: .06rem;
    background-position: center;
    background-size: contain;
    background-repeat: no-repeat;

    .video-item-play-icon {
        position: relative;
        margin: .59rem auto 0;
        width: .3rem;
        height: .3rem;
        background: url('../../assets/img/play_icon.png');
        background-position: center;
        background-size: contain;
        background-repeat: no-repeat;
    }
}

.video-item-desc {
    width: 100%;
    height: .32rem;
    margin-top: .12rem;
    font-size: .14rem;
    font-style: normal;
    font-weight: 400;
    line-height: .16rem;
    color: #000;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
}

.related-video-link {
    display: flex;
    flex-direction: row;
    width: 100%;
    margin-top: .056rem;
    color: #773434;
    font-family: "PingFang SC";
    font-size: .16rem;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    justify-content: center;
    text-decoration: underline;

    span {
        font-weight: bold;
    }
}

.related-video-link-arrow {
    display: flex;
    flex-direction: column;
    margin-left: .09rem;
    align-items: center;
    justify-content: center;
}

.related-video-link-arrow-1 {
    width: .085rem;
    height: .012rem;
    background-color: #773434;
    border: #773434 solid .011rem;
    border-radius: .01rem;
    transform: rotate(.125turn);
}

.related-video-link-arrow-2 {
    width: .085rem;
    height: .012rem;
    margin-top: .025rem;
    background-color: #773434;
    border: #773434 solid .011rem;
    border-radius: .01rem;
    transform: rotate(.875turn);
}
