/**
 * @file  歌会活动页-抽奖按钮
 * <AUTHOR>
 */

import React, {useCallback, useEffect, useState, useRef} from 'react';
import cls from 'classnames/bind';
import 'swiper/dist/css/swiper.min.css';
import Button from '../Button';
import {getLotteryResult} from '../../../../service/api';
import {activityShowLog, activityClickLog} from '@/pages/liveshow/log';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import style from './index.module.less';

const cx = cls.bind(style);

const MainBtn = props => {
    const {
        newVisual,
        handleToast,
        inviteTask,
        btnText,
        isLogin,
        subStatus,
        handleSubscribeAndFollow,
        drawLottery,
        refresh,
        isAnti,
        handleShare,
        handleMaskListStatus,
        showGuideHand,
        hasBubble,
        materialConfig
    } = props;

    const {wallet_url = ''} = newVisual;
    const [hasDrawNum, setHasDrawNum] = useState(inviteTask?.draw_number >= 1);
    const [activityStatus, setActivityStatus] = useState(inviteTask?.activity_status ? inviteTask?.activity_status : 0);
    const [hasSubText, setHasSubText] = useState(false);
    const inviteUserNum = inviteTask?.invite_user_num || 2;
    const [invitedNum, setInvitedNum] = useState(+inviteTask?.list?.length || 0);
    const [lotteryBtnText, setLotteryBtnText] = useState(btnText);

    useEffect(() => {
        if (hasDrawNum || !isLogin || subStatus === 0 || activityStatus !== 0) {
            setLotteryBtnText(btnText);
            setHasSubText(false);
            return;
        }
        setLotteryBtnText('邀好友抽奖');
        setHasSubText(true);
    }, [btnText, hasDrawNum, subStatus, activityStatus]);

    useEffect(() => {
        let clickBtnText;
        switch (lotteryBtnText) {
            case '预约直播\n并抽奖':
                clickBtnText = 'yuyue_and_choujiang';
                break;
            case '点击抽奖':
                clickBtnText = 'choujiang';
                break;
            case '邀好友抽奖':
                clickBtnText = 'invite_friend';
                break;
            case '抽奖机会\n已用完':
                clickBtnText = 'choujiang_finish';
                break;
            case '抽奖活动\n已结束':
                clickBtnText = 'end';
                break;
            default:
                clickBtnText = '';
        }
        activityShowLog('yuyue_live_and_choujiang', 15263, {
            click_btn: clickBtnText
        });
    }, [lotteryBtnText]);

    useEffect(() => {
        setHasDrawNum(inviteTask?.draw_number >= 1);
        setActivityStatus(inviteTask?.activity_status ? inviteTask?.activity_status : 0);
        setInvitedNum(+inviteTask?.list?.length || 0);
    }, [inviteTask]);

    // 跳转钱包页
    const jumpToWallet = () => {
        activityClickLog('b_my_award', 15263);
        boxjsUtils.invokeSchema({schema: wallet_url});
    };

    // 打开助力记录 需要登录
    const openHelpList = () => {
        if (!isLogin) {
            boxjsUtils.login().then(() => {
                window.setTimeout(() => {
                    refresh();
                }, 500);
            });
            return;
        }
        activityClickLog('b_help_record', 15263);
        handleMaskListStatus(true, 'assist');
    }

    const handleClk = useCallback(async () => {
        if (isLogin) {
            if (activityStatus !== 0) {
                activityClickLog('yuyue_live_and_choujiang', 15263, {
                    click_btn: activityStatus === 1 ? 'choujiang_finish' : 'end'
                });
                return;
            }
            if (isAnti) {
                handleToast('is-anti');
                return;
            }
            if (subStatus === 0) { // 未预约
                activityClickLog('yuyue_live_and_choujiang', 15263, {
                    click_btn: 'yuyue_and_choujiang'
                });
                handleSubscribeAndFollow();
                return;
            }
            // 助力分享
            if (!hasDrawNum) {
                activityClickLog('yuyue_live_and_choujiang', 15263, {
                    click_btn: 'invite_friend'
                });
                handleShare(true);
                return;
            }
            activityClickLog('yuyue_live_and_choujiang', 15263, {
                click_btn: 'choujiang'
            });
            drawLottery();
        } else {
            // 没有登录去登录,登录成功后刷新数据
            boxjsUtils.login().then(() => {
                window.setTimeout(() => {
                    refresh();
                }, 500);
            });
        }
    }, [isLogin, refresh, subStatus, hasDrawNum, activityStatus]);

    return (
        <div
            className={cx('lottery-buttons')}
        >
            <div
                className={cx('lottery-buttons-left')}
                style={{backgroundImage: `url('${materialConfig?.lottery_left_btn_bg}')`}}
                onClick={openHelpList}
            ></div>
                <Button
                    btnText={lotteryBtnText}
                    width={`${materialConfig?.lotter_main_btn_width}rem`}// 按钮宽度
                    height={`${materialConfig?.lotter_main_btn_height}rem`} // 按钮高度
                    btnTextColor={materialConfig?.lotter_main_btn_font_color}
                    btnBg={materialConfig?.lotter_main_btn_bg}
                    onClick={handleClk}
                    hasClick={showGuideHand}
                    animationStart={showGuideHand}
                    hasBubble={hasBubble && activityStatus === 0}
                    bubbleWidth={`${materialConfig?.lotter_main_btn_bubble_width}rem`} // 气泡宽度
                    bubbleBg={materialConfig?.lotter_main_btn_bubble_bg} // 气泡背景
                    hasZoomIn={false}
                    hasSubText={hasSubText}
                    btnSubText={`(${invitedNum > inviteUserNum ? inviteUserNum : invitedNum}/${inviteUserNum})`}
                    style={{opacity: activityStatus === 0 ? 1 : 0.7}}
                />
            <div
                className={cx('lottery-buttons-right')}
                style={{backgroundImage: `url('${materialConfig?.lottery_right_btn_bg}')`}}
                onClick={jumpToWallet}
            ></div>
        </div>
    );
};

export default MainBtn;
