.guests-wrapper {
    width: 3.66rem;
    display: flex;
    margin: .05rem auto 0;
    overflow: hidden;
    justify-content: center;
}

.long-guests-list {
    justify-content: start;
    flex-wrap: nowrap;
    overflow-x: auto;

    &::-webkit-scrollbar {
        display: none;
    }

    -ms-overflow-style: none;
    scrollbar-width: none;
}

.guests-item-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: .84rem;
    height: 1.186rem;
    box-sizing: border-box;
    margin-right: .2733rem;

    &:last-child {
        margin-right: 0;
    }
}

.more-than-three-item {
    margin-right: .1533rem;
}

.guests-item-avatar {
    position: relative;
    width: .667rem;
    height: .667rem;
    background-position: center;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    border-radius: 50%;

    .guests-avatar-circle {
        position: absolute;
        width: .677rem;
        height: .677rem;
        background-position: center;
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}

.guests-follow-btn {
    position: relative;
    width: .333rem;
    height: .2rem;
    margin-top: -.11rem;
    flex-shrink: 0;
    border-radius: .1rem;
    background-image: url('../../assets/img/guests-follow-btn-unfollowed.png');
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}

.guests-follow-btn-followed {
    background-image: url('../../assets/img/guests-follow-btn-followed.png');
}

.guests-follow-btn-default {
    opacity: 0;
    pointer-events: none;
}

.guests-item-name {
    width: 100%;
    margin-top: .06rem;
    color: rgba(0, 0, 0, .9);
    text-align: center;
    font: normal 600 .14rem "PingFang SC";
    line-height: .14rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.guests-item-desc {
    width: 100%;
    margin-top: .0667rem;
    color: rgba(0, 0, 0, .4);
    text-align: center;
    font: normal 400 .12rem "PingFang SC";
    line-height: .14rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
