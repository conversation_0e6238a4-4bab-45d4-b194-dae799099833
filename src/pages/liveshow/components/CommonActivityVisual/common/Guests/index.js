/**
 * @file 嘉宾阵容列表
 * <AUTHOR>
 */
import React, {useCallback, useEffect, useState} from 'react';
import cls from 'classnames/bind';
import {activityClickLog} from '@/pages/liveshow/log';
import DefaultGuestsAvatar from '../../assets/img/default_guests_avatar.png';
import {getFollowRes, getUnfollowRes} from '@/pages/liveshow/service/api'
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import styles from './index.module.less';

const cx = cls.bind(styles);
const defaultGuestsListItem = {
    name: "敬请期待",
    avatar: DefaultGuestsAvatar,
    desc: "更多明星阵容",
    followed: -1,
};

const Guests = props => {
    const {list = [], circle, isLogin, handleToast} = props;
    const longList = list.length > 3;

    const guestsList = list.length >= 2
        ? list.concat()
        : [...list, ...Array(2 - list.length).fill(defaultGuestsListItem)];

    const GuestsItem = useCallback(({item}) => {
        const [followBtnState, setFollowBtnState] = useState(-1);

        useEffect(() => {
            setFollowBtnState(item?.followed);
        }, [item]);

        // 头像点击事件
        const handleAvatarClick = useCallback(() => {
            activityClickLog('guests_team_head', 15263, {
                content_name: item?.name
            });
            // 如果嘉宾无百家号，跳转搜索页链接
            if (item?.bjh_user === 0) {
                let encodedUrl = encodeURIComponent(item.search_url);
                boxjsUtils.invokeSchema({schema: `baiduboxapp://v1/browser/open?upgrade=1&url=${encodedUrl}&append=1`});
                return;
            }
            // 如果有百家号，跳转百家号详情页
            boxjsUtils.invokeSchema({schema: item.scheme});
        }, [item]);

        // 关注按钮点击事件
        const handleBtnClick = () => {
            if (!isLogin) {
                boxjsUtils.login().then(() => {
                    window.setTimeout(() => {
                        refresh();
                    }, 500);
                });
                return;
            }
            activityClickLog('guests_team_like', 15263, {
                content_name: item?.name
            });
            handleFollowClick();
        };

        // 处理关注状态
        const handleFollowClick = useCallback(async () => {
            try {
                let followRes = {};
                // 已关注
                if (followBtnState === 1) {
                    followRes = await getUnfollowRes({
                        uk: item.uk,
                        third_id: item.third_id,
                        type: item.type,
                    });
                } else { // 未关注
                    followRes = await getFollowRes({
                        uk: item?.uk,
                        third_id: item?.third_id,
                        type: item?.type,
                    });
                }

                if (followRes.errno === 0) {
                    setFollowBtnState(followBtnState === 1 ? 0 : 1);
                    followBtnState === 0 && handleToast('follow-success');
                } else {
                    console.error('关注操作失败', followRes.errmsg);
                }
            } catch (error) {
                console.error('关注操作发生异常', error);
            }
        }, [followBtnState, item]);

        return (
            <div className={cx('guests-item-wrapper', {
                'more-than-three-item': longList
            })}>
                <div
                    className={cx('guests-item-avatar')}
                    style={{
                        backgroundImage: `url('${item?.avatar || DefaultGuestsAvatar}')`,
                    }}
                    onClick={handleAvatarClick}
                >
                    <div
                        className={cx('guests-avatar-circle')}
                        style={{
                            backgroundImage: `url('${circle}')`,
                        }}
                    ></div>
                </div>
                <div
                    className={cx('guests-follow-btn', {
                        'guests-follow-btn-followed': followBtnState === 1,
                        'guests-follow-btn-default' : followBtnState === -1 || item.bjh_user === 0,
                    })}
                    onClick={handleBtnClick}
                ></div>
                <div className={cx('guests-item-name')}>{item?.name}</div>
                <div className={cx('guests-item-desc')}>{item?.desc}</div>
            </div>
        );
    }, []);

    return (
        <>
            <div className={cx('guests-wrapper', {
                'long-guests-list': longList,
            })}>
                {
                    guestsList.map((item, index) => {
                        return (
                            <GuestsItem
                                item={item}
                                key={index}
                            />
                        );
                    })
                }
            </div>
        </>
    );
}

export default Guests;
