/**
 * @file 挽留弹窗
 * <AUTHOR>
 */

import React, { useEffect } from 'react';
import cls from 'classnames/bind';
import RedPacketWrapper from '../RedPacketWrapper';
import styles from './index.module.less';
import Button from '../common/Button';
import {activityShowLog, activityClickLog} from '@/pages/liveshow/log';

const cx = cls.bind(styles);

const Back = (props) => {
    const {materialConfig} = props;

    useEffect(() => {
        activityShowLog('pop_stay', 15263);
    }, []);

    const handleCancel = () => {
        activityClickLog('stay_exit', 15263);
        props.onCancel?.();
    }

    const handleOk = () => {
        activityClickLog('stay_yuyue_and_choujiang', 15263);
        props.onOk?.();
    }

    return (
        <>
            <RedPacketWrapper
                materialConfig={materialConfig}
                outerTextCustom={
                    <div className={cx('outer-text')}>
                        <div>你要放弃</div>
                        <div>最高千元奖品吗</div>
                    </div>
                }
                btnAreaShow={false}
                bgType={4}
                onClose={props.onClose}
                logoHidden
                outTextBgShow
                outTextShow
                outTextBgAnimationShow
                closeOnMaskClick={false}
                BtnAreaCustom={
                    <div className={cx('back-btn-area')}>
                        <Button
                            width={'1.59rem'}
                            height={'0.51rem'}
                            btnBg={'https://ala-gift.cdn.bcebos.com/gift/2024-12/1734606596819/exit.png'}
                            onClick={handleCancel}
                        />
                        <Button
                            btnText={'预约并抽奖'}
                            width={'1.59rem'}
                            height={'0.51rem'}
                            btnTextColor={materialConfig?.lotter_main_btn_font_color}
                            btnBg={materialConfig?.lotter_main_btn_bg}
                            onClick={handleOk}
                        />
                    </div>
                }
            >
                <div className={cx('back-content')}>
                    <div className={cx('back-tip')}>预约直播100%抽好礼</div>
                    <div className={cx('gift')}></div>
                </div>
            </RedPacketWrapper>
        </>
    );
}

export default Back;
