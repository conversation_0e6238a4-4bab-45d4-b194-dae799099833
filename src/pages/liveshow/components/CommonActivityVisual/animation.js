import gsap from 'gsap';

export const carouselVerticalLottery = (carouselEle, duration) => {
    const slides = carouselEle.children;
    const element = slides[0];
    const height = element.offsetHeight;
    const tween = gsap.timeline({repeat: -1, repeatDelay: 0})
    .to(slides, {
        duration,
        y: `-=${height}`,
        ease: 'none',
    })
    return tween;
};

export const stopLotteryAtOneItem = (carouselEle, duration, index, domHeight, adjustHeight) => {
    const slides = carouselEle.children;
    const tween = gsap.timeline()
    .to(slides, {
        duration: duration * 0.25,
        y: `-${(index * domHeight + domHeight * 0.3) - adjustHeight}`,
        ease: 'none',
    })
    .to(slides, {
        duration: duration * 0.25,
        y: `-${(index * domHeight - domHeight * 0.1) - adjustHeight}`,
        ease: 'none',
    })
    .to(slides, {
        duration: duration * 0.2,
        y: `-${(index * domHeight + domHeight * 0.1) - adjustHeight}`,
        ease: 'none',
    })
    .to(slides, {
        duration: duration * 0.15,
        y: `-${(index * domHeight - domHeight * 0.05) - adjustHeight}`,
        ease: 'none',
    })
    .to(slides, {
        duration: duration * 0.15,
        y: `-${index * domHeight - adjustHeight}`,
        ease: 'none',
    })
    return tween;
};

const toggleClass = (elements, type, clsName) => {
    [...elements].forEach(item => {
        item.classList[type](clsName);
    });
};

// 上下轮播
export const carouselVertical = carouselEle => {
    const slides = carouselEle.children;
    const element = slides[0];
    const length = element.children.length;
    const height = element.offsetHeight;
    const duration = 3 * length;
    // 上下线性轮播, steps配合transition样式
    const tween = gsap
        .timeline({repeat: -1})
        // step
        .to(slides, {
            duration,
            y: `-=${height}`,
            ease: `steps(${length})`,
            onComplete() {
                toggleClass(slides, 'remove', 'animated');
                tween.seek(1.6);
                requestAnimationFrame(() => {
                    toggleClass(slides, 'add', 'animated');
                });
            },
        });
    return tween;
};
