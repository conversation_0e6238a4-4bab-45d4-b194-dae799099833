.common-main-btn-wrap {
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    pointer-events: auto;
    z-index: 2;

    &.lottery-btn {
        // margin-top: -3.33rem;
    }

    .lottery-title {
        position: relative;
        width: 100%;
        color: #212121;
        font: 600 .16rem/.224rem "PingFang SC";
        text-align: center;
        pointer-events: none;
        background-position: center;
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }

    .lottery-title-subscribe-icon {
        position: absolute;
        top: 0;
        right: .75rem;
        width: 1.03rem;
        height: .28rem;
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
    }

    .lottery-title-text {
        display: flex;
        flex-direction: column;
        position: absolute;
        text-align: right;
        font-family: "PingFang SC";
        font-style: normal;
        font-weight: 600;
        overflow: hidden;
    }

    .animated {
        transition: transform .3s ease-in-out !important;
    }

    .lottery-machine-bg {
        position: relative;
        width: 100%;
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
    }

    .lottery-machine-cover {
        position: absolute;
        height: 100%;
        width: 100%;
        z-index: 2;
        pointer-events: none;
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
    }

    .lottery-machine {
        position: relative;
        width: 100%;
        height: .83rem;
        background-size: cover;
        background-repeat: no-repeat;
    }

    .txt-subscribe-now {
        height: .23rem;
        width: 1.035rem;
        background: url('../assets/img/subscribe_btn_text.png') no-repeat center/100% 100%;
    }

    .txt-subscribe-for-gift {
        height: .23rem;
        width: 1.34rem;
        background: url('../assets/img/subscribe_for_gift_text.png') no-repeat center/100% 100%;
    }

    .count-tips {
        width: .43333rem;
        height: .18333rem;
        background: #FFF;
        border: .0067rem solid #D76700;
        border-radius: .36667rem;
        position: absolute;
        top: -1.15rem;
        right: .34rem;
        color: #D76700;
        font-family: "PingFang SC";
        font-size: .09333rem;
        line-height: .18333rem;
        align-content: center;
        text-align: center;
        z-index: 3;
    }

    .subscribed-btn {
        top: .22rem;
        right: .287rem;
    }

    .no-countdown-subscribed-btn {
        top: -.75rem;
    }

    .subscribed-btn-color {
        color: #fff;
        background: #FF856A;
    }
}

.guide-hand {
    position: absolute;
    top: 1.743rem;
    left: 2.613rem;
    width: .8283rem;
    height: .7758rem;
    background-size: 100%;
    background-repeat: no-repeat;
}
