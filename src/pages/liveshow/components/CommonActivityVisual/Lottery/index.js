/**
 * @file  Lottery 抽奖区域
 * <AUTHOR>
 */

import React, {useCallback, useEffect, useRef, useState} from 'react';
import cls from 'classnames/bind';
import {activityShowLog, activityClickLog} from '@/pages/liveshow/log';
import {carouselVertical} from '../animation';
import SlotMachine from '../common/SlotMachine';

import style from './index.module.less';

const cx = cls.bind(style);

const Lottery = ({
    entranceAPP,
    inviteTask,
    newVisual = {},
    lotteryState,
    lotteryResult,
    subStatus,
    materialConfig,
    appointmentNumber
}) => {

    const tweenList = useRef();
    const appointmentNumberString = `${appointmentNumber}${materialConfig?.lottery_title_text_3 || ''}`;

    const [lotteryTitleTextList, setLotteryTitleTextList] = useState([
        inviteTask?.draw_number_used > 0 ? materialConfig?.lottery_title_text_2 : materialConfig?.lottery_title_text_1,
        appointmentNumberString
    ]);

    useEffect(() => {
        setLotteryTitleTextList([inviteTask?.draw_number_used > 0
            ? materialConfig?.lottery_title_text_2
            : materialConfig?.lottery_title_text_1, appointmentNumberString]);
    },[inviteTask]);

    useEffect(() => {
        initCarousel();
    },[]);

    // 初始化滚动
    const initCarousel = () => {
        const titleTextDom = document.getElementById('lottery-title-text');
        tweenList.current = carouselVertical(titleTextDom);
    }

    return (
        <div
            className={cx('common-main-btn-wrap', {
                'lottery-btn': entranceAPP,
            })}
            style={{
                marginTop: `${materialConfig?.lottery_margin}rem`
            }}
        >
            {
                subStatus === 1 && <div
                    className={cx('lottery-title-subscribe-icon')}
                    style={{
                        backgroundImage: `url('${materialConfig?.subscribed_icon}')`,
                    }}
                ></div>
            }
            <div
                className={cx('lottery-title')}
                style={{
                    backgroundImage: `url('${materialConfig?.lottery_title_bg}')`,
                    height: `${materialConfig?.lottery_title_height}rem`
                }}
            >
                <div
                    className={cx('lottery-title-text')}
                    id='lottery-title-text'
                    style={{
                        color: materialConfig?.lottery_title_text_font_color,
                        fontSize: `${materialConfig?.lottery_title_text_font_size}rem`,
                        top: `${materialConfig?.lottery_title_text_position_top}rem`,
                        right: `${materialConfig?.lottery_title_text_position_right}rem`
                    }}
                >
                    <ul className={cx('animated')}>
                        {
                            lotteryTitleTextList.map((item, index) => {
                                return <li key={index}>{item}</li>
                            })
                        }
                    </ul>
                    <ul
                        className={cx('animated')}
                    >
                        <li>{lotteryTitleTextList[0]}</li>
                    </ul>
                </div>
            </div>
            <div
                className={cx('lottery-machine-bg')}
                style={{
                    height: `${materialConfig?.lottery_cover_height}rem`,
                    backgroundImage: `url('${materialConfig?.lottery_machine_bg}')`,
                }}
            >
                <div
                    className={cx('lottery-machine-cover')}
                    style={{
                        height: `${materialConfig?.lottery_cover_height}rem`,
                        backgroundImage: `url('${materialConfig?.lottery_machine_cover}')`,
                    }}
                ></div>
                <div
                    className={cx('lottery-machine')}
                    style={{
                        width: `${materialConfig?.lottery_machine_width}rem`,
                        height: `${materialConfig?.lottery_machine_height}rem`,
                        margin: materialConfig?.lottery_machine_margin
                    }}
                >
                    <SlotMachine
                        newVisual={newVisual}
                        giftItemBg={materialConfig?.lottery_gift_item_bg}
                        itemWidth={`${materialConfig?.lottery_gift_item_width}rem`}
                        itemHeight={`${materialConfig?.lottery_gift_item_height}rem`}
                        lotteryState={lotteryState}
                        lotteryResult={lotteryResult}
                    />
                </div>
            </div>
        </div>
    );
};

export default Lottery;
