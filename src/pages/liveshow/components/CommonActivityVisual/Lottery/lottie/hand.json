{"v": "5.8.1", "fr": 25, "ip": 0, "op": 49, "w": 400, "h": 300, "nm": "合成 1", "ddd": 0, "assets": [{"id": "image_0", "w": 165, "h": 151, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "image_1", "w": 123, "h": 124, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "comp_0", "nm": "预合成 7", "fr": 25, "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "预合成 6", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [561, 260, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [621, 250, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 1242, "h": 500, "ip": 0, "op": 50, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "白圈0616.png", "cl": "png", "refId": "image_1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 14, "s": [80]}, {"t": 20, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [789.241, 307.977, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [61.5, 62, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 10, "s": [50, 50, 100]}, {"t": 20, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ip": 10, "op": 60, "st": 10, "bm": 0}]}, {"id": "comp_1", "nm": "预合成 6", "fr": 25, "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "空 2", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [621, 250, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 50, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 2, "nm": "小手0616.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [-20]}, {"t": 20, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [1000.478, 393.182, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [925.508, 331.225, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 20, "s": [1000.478, 393.182, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [82.5, 75.5, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [120, 120, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 10, "s": [90, 90, 100]}, {"t": 20, "s": [120, 120, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\namp = 0.1;\nfreq = 2;\ndecay = 5;\n$bm_rt = n = numKeys;\nif (n == 0) {\n    $bm_rt = value;\n} else {\n    $bm_rt = t = $bm_sub(time, key(n).time);\n    if (t > 0) {\n        v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n        $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, 2), Math.PI), t))), Math.exp($bm_mul(decay, t))));\n    } else {\n        $bm_rt = value;\n    }\n    ;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "色相/饱和度", "np": 11, "mn": "ADBE HUE SATURATION", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "通道控制", "mn": "ADBE HUE SATURATION-0002", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {}, {"ty": 0, "nm": "主色相", "mn": "ADBE HUE SATURATION-0004", "ix": 3, "v": {"a": 0, "k": -13, "ix": 3}}, {"ty": 0, "nm": "主饱和度", "mn": "ADBE HUE SATURATION-0005", "ix": 4, "v": {"a": 0, "k": 65, "ix": 4}}, {"ty": 0, "nm": "主亮度", "mn": "ADBE HUE SATURATION-0006", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 7, "nm": "彩色化", "mn": "ADBE HUE SATURATION-0007", "ix": 6, "v": {"a": 0, "k": 0, "ix": 6}}, {"ty": 0, "nm": "着色色相", "mn": "ADBE HUE SATURATION-0008", "ix": 7, "v": {"a": 0, "k": 0, "ix": 7}}, {"ty": 0, "nm": "着色饱和度", "mn": "ADBE HUE SATURATION-0009", "ix": 8, "v": {"a": 0, "k": 25, "ix": 8}}, {"ty": 0, "nm": "着色亮度", "mn": "ADBE HUE SATURATION-0010", "ix": 9, "v": {"a": 0, "k": 0, "ix": 9}}]}], "ip": 0, "op": 52, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "预合成 7", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-63, 25, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [621, 250, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 1242, "h": 500, "ip": 0, "op": 60, "st": 0, "bm": 0}], "markers": []}