@font-face {
    font-family: 'Baidu Number';
    src: url(../assets/font/baidunumber-Medium.ttf) format('truetype');
}

.clip-text {
    color: transparent;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.amount-gradient-text {
    background: linear-gradient(180deg, #FF718A 17.22%, #FF231E 80%);
    .clip-text();
}

.unit-gradient-text {
    background: linear-gradient(180deg, #FF5E70 0%, #FF3131 100%);
    .clip-text();
}

.money {
    font-size: 0;

    .money-content {
        display: inline-block;
        position: relative;

        .money-amount {
            color: #fff;
            display: inline-block;
            position: relative;
            top: .02rem;
            font-size: .9rem;
            font-family: Baidu Number;
            font-weight: 500;
        }

        .money-unit {
            color: #fff;
            display: inline-block;
            font-family: PingFang TC;
            font-size: .16rem;
            font-weight: 500;
        }

        .money-tip {
            position: absolute;
            display: inline-block;
            top: .12rem;
            right: -.04rem;
            transform: translateX(50%);
            padding-top: .03rem;
            width: .457rem;
            height: .21rem;
            background-image: url('../assets/img/money_bubble.png');
            background-size: 100%;
            animation: fadeIn .3s linear both;

            &.android {
                top: .12rem;
            }
        }
    }
}
