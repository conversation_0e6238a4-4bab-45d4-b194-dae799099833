/**
 * @file 金额展示组件
 * <AUTHOR>
 */

import React, {useEffect, useState} from 'react';
import cls from 'classnames/bind';
import {env} from '@/utils/env';
import {handleDecimalNum} from '../../../utils/formatNum';
import styles from './index.module.less';

const cx = cls.bind(styles);

const MoneyBox = ({
    amount = 0,
    gradientSwitch = true,
    showTip = false,
    styleClassName = ''
}
) => {

    const [showMoney, setShowMoney] = useState(0);

    useEffect(() => {
        const res = handleDecimalNum(amount / 100, 2, 3);
        setShowMoney(res.numStr);
    }, [amount]);

    return (
        <div className={cx('money',
            {
                [styleClassName]: !!styleClassName
            }
        )}
        >
            <div className={cx('money-content')}>
                <span className={cx('money-amount',
                    {
                        'amount-gradient-text': gradientSwitch
                    }
                )}
                >
                    {showMoney}
                </span>
                <span className={cx('money-unit',
                    {
                        'unit-gradient-text': gradientSwitch
                    }
                )}
                >元</span>
                {
                    !!showTip && (
                        <span className={cx('money-tip', {
                            'android': env.isAndroid
                        })}
                        ></span>)
                }
            </div>
        </div>
    );
};

export default MoneyBox;