@import (reference) '../../../assets/style-util.less';

.live-video-common {
    width: 100%;
    height: 7.36rem;
    background: rgba(0, 0, 0, .5);
    margin: auto;
    position: absolute;

    // 直播中
    &.is-living {

        // 播放器设置
        #live-video-common {
            .hplayer-controller-mask,
            .hplayer-controller {
                display: none;
            }
        }
    }

    &.is-video-loading {
        #live-video-common .hplayer-bezel .diplayer-loading-icon {
            display: block !important;
        }
    }

    #live-video-common {
        width: 100%;
        height: 7.36rem;
        background-size: 100% 100%;
        background-position: center center;

        .hplayer-mobile-play .deplayer-play-icon {
            content: url('../assets/img/play_icon.png');
            top: 46%;
            z-index: 3;
        }

        .hplayer-mobile-play .deplayer-pause-icon {
            top: 46%;
            z-index: 3;
        }
    }

    .hplayer-bezel {
        border-radius: .25rem;
    }
}

.video-mask-common {
    position: relative;
    width: 100%;
    height: 7.36rem;
    background: black;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 42pr;
    color: #fff;

    .live-video.is-hor:not(.is-no-video) & {
        background-color: transparent;
    }

    .play-wrapper {
        width: 100%;
        height: 100%;
        background-position: center center;
        background-repeat: no-repeat;
        background-size: 240pr 240pr;

        .live-video.is-hor & {
            width: 264pr;
            height: 264pr;
        }
    }

    .group-wrapper {
        .common-flex-center-column;

        color: #fff;

        .group-title {
            margin-bottom: 40pr;
            font-weight: bolder;
            font-size: 60pr;
        }

        .group-tip {
            opacity: .6;
            font-size: 42pr;
            margin-bottom: 55pr;
        }

        .group-btn {
            min-width: 504pr;
            height: 144pr;
            border-radius: 72pr;
            font-weight: bold;
            font-size: 48pr;
            line-height: 144pr;
            color: #fff;
            text-align: center;
            padding: 0 6pr;
            box-sizing: content-box;
            .common-btn-bgcolor;
        }
    }

    .paid-wrapper {
        .common-flex-center-column;

        .paid-title {
            margin-bottom: 45pr;
            font-size: 60pr;
            color: #fff;
        }

        .paid-price-container {
            margin-bottom: 81pr;
            font-size: 42pr;
            color: #f60;

            .discount {
                margin-left: 21pr;
                text-decoration: line-through;
                color: #b8b8b8;
            }
        }

        .paid-btn {
            min-width: 504pr;
            height: 144pr;
            font-size: 48pr;
            line-height: 144pr;
            text-align: center;
            border-radius: 72pr;
            color: #fff;
            .common-btn-bgcolor;
        }
    }

    .end-no-review-wrapper {
        .common-flex-center-column;

        .mb31 {
            margin-bottom: 31pr;
        }

        .tips {
            font-size: 42pr;
            white-space: nowrap;
            color: #999;
        }

        .end-text {
            font-size: 60pr;
            color: #fff;
        }

        .watched-num {
            margin-bottom: 51pr;
        }
    }
}
