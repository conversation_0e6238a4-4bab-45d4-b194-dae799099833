/**
 * @file 简介模块
 * <AUTHOR>
 * @date 2021-11-09 20:37:48
 */

import React from 'react';
import {isSuperEmpty as isEmpty} from '@/utils/isEmpty';
import style from './index.less';

function ShowElement(params) {
    const {ele, imgOnError, imgOnLoad} = params;
    const type = ele.type;
    const props = {
        dangerouslySetInnerHTML: {
            __html: ele.data,
        },
    };
    switch (type) {
        case 'image': {
            const imgData = ele.data.original;
            return (
                <div className={style.imageContain}>
                    <div
                        style={{
                            paddingTop: `${(100 * imgData.height) / imgData.width}%`,
                        }}
                    >
                        <img
                            src={imgData.src ? imgData.src : imgData.org_src}
                            onError={(e) => imgOnError(e, imgData)}
                            onLoad={(e) => imgOnLoad(e)}
                        />
                    </div>
                </div>
            );
        }
        case 'segment':
            return <div className={style.segment} />;
        default:
            return ele.data ? <p {...props} /> : null;
    }
}

function RichSnippets(props) {
    const {description, new_description} = props;

    if (isEmpty(description) && isEmpty(new_description)) {
        return null;
    }

    const imgOnError = (event, data) => {
        // 加载失败重试
        const target = event.target;
        const hasTry = target.getAttribute('hasTry');
        if (hasTry) {
            return;
        }
        target.setAttribute('src', data.src);
        target.setAttribute('hasTry', true);
    };

    const imgOnLoad = (e) => {
        try {
            const box = e.target.parentNode.parentNode;
            box.style.backgroundImage = `url(${e.target.src})`;
        } catch (error) {
            console.log(error);
        }
    };

    return (
        <div className={style.description}>
            <div className={style.descriptionTitle}>简介</div>
            {isEmpty(new_description) ? (
                <div className={style.descriptionContent}>{description}</div>
            ) : (
                new_description &&
                new_description.length > 0 &&
                new_description.map((ele, key) => <ShowElement key={key} {...{ele, imgOnError, imgOnLoad}} />)
            )}
        </div>
    );
}

export default RichSnippets;
