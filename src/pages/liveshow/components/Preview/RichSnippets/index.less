@import '~@baidu/nano-theme/index.less';

.description {
    box-sizing: border-box;
    width: 100%;
    padding-top: .36rem;

    p {
        .theme-color(GC3);

        font-size: .16rem;
        line-height: .24rem;
    }
}

.descriptionTitle {
    margin-bottom: .21rem;
    font-size: .19rem;
    font-weight: 800;

    .theme-color(GC1);
}

.descriptionContent {
    display: -webkit-box;
    overflow: hidden;
    font-size: .16rem;
    line-height: .21rem;
    white-space: pre-line;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    .theme-color(GC1);
}

.imageContain {
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    max-width: 100%;
    margin: .21rem auto 0;
    border-width: 1px;
    border-style: solid;
    border-radius: .09rem;
    background-image: url(https://b.bdstatic.com/searchbox/icms/searchbox/img/BdrainrwLandingMainContent/placeholder-light.png);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 21px 21px;

    .theme-background(GC57);
    .theme-border(FC19);

    .noPadding & {
        border-radius: 0;
    }
    // ai识图新增样式
    .aiPic {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 100%;
    }

    .nightmode & {
        background-image: url(https://b.bdstatic.com/searchbox/icms/searchbox/img/BdrainrwLandingMainContent/placeholder-night.png);
    }

    img {
        top: 0;
        width: 100%;
        height: 100%;
    }
}

.segment {
    width: 100%;
    height: .12rem;
}

span.bjh-h3 {
    display: block;
    margin: .21rem 0 .055rem;
    font-size: .16rem;
    font-weight: 700;
    line-height: .16rem;

    .theme-color(GC1);
}

span.bjh-p {
    font-size: .16rem;
    line-height: .25rem;

    .theme-color(GC3);

    &.bjh-text-align-center {
        display: inline-block;
        width: 100%;
        text-align: center;
    }

    &.bjh-text-align-right {
        display: inline-block;
        width: 100%;
        text-align: right;
    }
}

span.bjh-strong,
span.bjh-bold {
    font-weight: 700;

    .theme-color(FC9);
}

.mainContent ul,
.mainContent ol,
span.bjh-ul,
span.bjh-ol {
    display: block;
    margin: 0;
}

span.bjh-ul {
    list-style: disc inside;

    .theme-color(GC3);
}

span.bjh-ol {
    list-style: decimal inside;

    .theme-color(GC3);
}

span.bjh-li {
    display: list-item;
    margin: .105rem 0 !important;

    span.bjh-p {
        display: inline;
    }
}
