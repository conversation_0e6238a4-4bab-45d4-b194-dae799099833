/**
 * @file 预约模板入口
 * <AUTHOR>
 * @date 2021-11-09 20:37:48
 */

import React, {useEffect, useRef, useState} from 'react';
import {PageStore} from '@/pages/liveshow/service/register';
import {getAppSource, getAppSchemeHead, getUrlParam} from '@/pages/liveshow/utils/getUrlParam';
import {execInvoke, getInvokeInfo, setCommonParams, createMcpWxTag, getExitInfo} from '@baidu/mcp-sdk';
import {matrixAppSchemeHeaderList, reflow, openReflowByUrl} from '@/pages/liveshow/utils/reflow';
import {registerPageHidden} from '@/utils/index';
import {env, isOppoBrowser} from '@/utils/env';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import {registerCallback} from '@/pages/liveshow/utils/dataChannel';
import {logDialogShow} from '@/pages/liveshow/utils/thunder';
import {previewShowLog, commonClickLog} from '@/pages/liveshow/log';
import {invokeKeys, APP_NAME_SCENE, needGetMcpInfo} from '@/pages/liveshow/config/const';
import GroupBuy from '../GroupBuy';
import QuestionList from '../GroupBuy/QuestionList';
import GroupRule from '../GroupBuy/GroupRule';
import CourseDetail from '../CourseDetail';
import PreHeader from './PreHeader';
import AuthorArea from './AuthorArea';
import CountDown from './CountDown';
import LiveButtons from './LiveButtons';
import RichSnippets from './RichSnippets';
import LiveSpeaker from './LiveSpeaker';
import LiveSeries from './LiveSeries';
import BooksDialog from './BooksDialog';
import ReflowBar from './ReflowBar';
import Coupon from './Coupon';
import Goods from './Goods';
import style from './index.less';
// import './tomas.less';

// 不执行回流 / 不创建微信开放标签
const noreflow = PageStore.queryParams.noreflow === 'all';
// 回流渠道
const source = getAppSource() || PageStore.queryParams.source || '';

const appSchemeHead = getAppSchemeHead();
// 回流矩阵场景 || iOS Lite H5 页内观看
const isMatrixApp = matrixAppSchemeHeaderList.includes(appSchemeHead) || (env.isIOS && env.isLiteBox);
// 调起位信息（mcp内配置API返回）此处因为在触发播放器事件后，导致状态信息清空
let invokeInfo = {};

const hasArrValue = (arr) => {
    return arr && arr.length > 0;
};

function Preview({data, refresh}) {
    const childRef = useRef();

    const [showDialog, setShowDialog] = useState(false);
    const [showLiveBtn, setShowLiveBtn] = useState(true);
    // 优惠券状态，默认0未获取，1没有，2有
    const [couponStatus, setCouponStatus] = useState(0);
    const [multiple, setMultiple] = useState(1);

    // 是否展示全局调起蒙层 默认不展示
    const [isShowWhole, setIsShowWhole] = useState(false);

    useEffect(() => {
        dealMultiple();
    }, []);

    useEffect(() => {
        const callback = (info) => {
            const payData = info[data.pay_nid] || [];
            payData.forEach((item) => {
                if (item && item.type === 'pay_result' && item.status) {
                    childRef.current && childRef.current.subscribe();

                    boxjsUtils.invokeBookshelf(data.shelf_info);

                    boxjsUtils.invokeSchema({schema: boxjsUtils.ADD_VIP_TAG_SCHEME});

                    logDialogShow();
                    setShowDialog(true);

                    refresh();
                }
            });
        };

        registerCallback(callback, 'h5_live_preview');
    }, [data, refresh]);

    useEffect(() => {
        registerPageHidden(
            () => {},
            () => {
                refresh?.(); // 从后台切到前台重新请求下数据，解决回流到端内再返回时端内外数据不一致问题
            }
        );
    }, []);

    // 由于register.js中updateFontSize对预约页的font-size处理
    // 特对414设计稿产出的样式进行等比缩放
    // 计算缩放比例
    const dealMultiple = () => {
        const cw = document.documentElement.clientWidth;
        let nowFt = cw <= 360 ? 90 : 100;
        const r = Math.min(1, cw * 100 / 414 / nowFt);
        console.log('dealMultiple:', r);
        setMultiple(r);
    };

    const {
        cover,
        video_url,
        isGroupPaid,
        countdown,
        appointment_status,
        is_paying_user,
        pay_info,
        has_pay_service,
        is_login,
        topic_select,
        description,
        new_description,
        compilation_list,
        lecturer_list,
        has_reflow_bar,
        pay_nid,
        series_nid,
        compilation_id,
        status,
        template_id,
        nid,
        chat_mcast_id,
        reliable_mcast_id
    } = data;

    // 隐藏顶部回流bar
    const hideReflowBar = series_nid
        || !has_reflow_bar
        || boxjsUtils.platformInfo.isBox
        || boxjsUtils.platformInfo.isLiteBox
        || isOppoBrowser()
        || boxjsUtils.platformInfo.isHarmonyBox;

    // 展示顶部padding区
    const showReflowTopPad = !hideReflowBar && (!isMatrixApp || ['lite'].indexOf(source) > -1);

    useEffect(() => {
        // 预约页面展现打点
        previewShowLog(status, '', nid, 0, '15267', true);
    }, [status]);

    // 统一创建微信开放标签
    const createWxTags = invokeInfo => {
        const {action_rule, log_id} = invokeInfo;
        const partRuleInfo = action_rule.pos_part[0];
        const wholeRuleInfo = action_rule.pos_whole[0];

        // 回流bar按钮
        const reflowbarDom = document.querySelector('.new-backflow-wrap');
        // 预约按钮
        const subscribeDom = document.querySelector('.preview-btn-wrap');

        // 全局调起蒙层
        const wholeMaskDom = document.querySelector('.whole-mask');

        // 全局
        wholeMaskDom && createMcpWxTag({targetDom: wholeMaskDom}, 'pos_whole', wholeRuleInfo, log_id);

        const partDoms = [reflowbarDom, subscribeDom];
        partDoms.forEach(dom => {
            dom && createMcpWxTag({targetDom: dom}, 'pos_part', partRuleInfo, log_id);
        });
    };

    const generateInvokeInfo = scheme => {
        const res = {};

        invokeKeys.forEach(key => {
            res[`pos_${key}`] = [
                {
                    // 调起scheme
                    'share_scheme': scheme
                }
            ];
        });

        return res;
    };

    useEffect(() => {
        // 设置通用调起信息
        setCommonParams({
            app: 'wise',
            scene: APP_NAME_SCENE[source] || 'sharepage',
            from: '',
            ext_sid: getUrlParam('ext_sid') || '' // 透传url上sid参数
        });
        const fetchMCPData = async () => {
            // 部分端未接入口令 无schema_info
            const invokeScheme = data && data.schema_info && data.schema_info.enter_cmd || '';

            if (invokeScheme) {
                // 获取调起位详细信息
                const info = await getInvokeInfo({
                    invoke_info: generateInvokeInfo(invokeScheme)
                });
                invokeInfo = info;
                // 全局蒙层是否退场
                const wholeInfo = getExitInfo('pos_whole');
                const wholeRule = (info && info.action_rule && info.action_rule.pos_whole) || '';
                if (wholeInfo && wholeInfo.isExit !== undefined && Array.isArray(wholeRule)) {
                    // 全局蒙层是否退场
                    const wholeActionFlag = (wholeRule[0] && wholeRule[0].action_flag) || 0;

                    if (wholeInfo.isExit || +wholeActionFlag !== 1) {
                        setIsShowWhole(false);
                    }
                    else {
                        setIsShowWhole(true);
                        previewShowLog(status, 'whole_mask', nid, 0, '15263', true);
                    }
                }

                // 存在调起信息 && 安卓 && 微信 && 回流主板 新增微信开放标签校验
                if (!isMatrixApp && env.isAndroid && env.isWechat
                        && info && info.action_rule) {
                    info && createWxTags(info);
                }
            }
        };

        // 回流手百 且 支持回流时执行 !isMatrixApp
        if (needGetMcpInfo && !noreflow) {
            fetchMCPData();
        }

    }, []);

    // 执行具体调起行为
    const execMcpInvoke = async (key) => {
        // 支持url参数判断不执行回流
        if (noreflow) {
            return;
        }

        if (invokeKeys.indexOf(key) === -1) {
            window.console.log('该调起位未登记,请先行登记');
            // 执行默认调起行为
            return;
        }
        const invokeInfoForPos = invokeInfo && invokeInfo.action_rule && invokeInfo.action_rule[`pos_${key}`];

        if (!invokeInfoForPos || !Array.isArray(invokeInfoForPos) || !invokeInfoForPos[0]) {
            window.console.log('无相关调起位信息');
            // 执行默认调起 兼容接口返回异常情况
            reflow({roomId: PageStore.roomId});
            return;
        }
        try {
            const res = await execInvoke(
                `pos_${key}`,
                invokeInfoForPos[0],
                invokeInfoForPos[0].log_id
            );
            // 调起失败后新增调起兜底行为
            if ([10000, 10001, 10002, 10003].indexOf(res.status) === -1) {
                // 默认调起行为
                reflow({roomId: PageStore.roomId});
            }
            window.console.log(res.msg);
        }
        catch (e) {
            window.console.log(e);
            // 执行默认调起行为
            reflow({roomId: PageStore.roomId});
        }
    };

    const handleInvoke = () => {
        // 在调起主板、极速版、好看时 执行mcp调起
        if (!isMatrixApp || ['lite', 'haokan'].indexOf(source) > -1) {
            execMcpInvoke('part');
            return;
        }

        // 调起其他矩阵 执行默认调起
        openReflowByUrl();
    };

    const handleWholeClk = () => {
        commonClickLog(status, 'whole_mask', nid, 0, '15263', true);

        execMcpInvoke('whole');

        // 全局蒙层是否退场
        const wholeInfo = getExitInfo('pos_whole');
        const wholeRule = (invokeInfo && invokeInfo.action_rule && invokeInfo.action_rule.pos_whole) || '';
        if (wholeInfo && wholeInfo.isExit !== undefined && Array.isArray(wholeRule)) {
            // 全局蒙层是否退场
            const wholeActionFlag = (wholeRule[0] && wholeRule[0].action_flag) || 0;

            if (wholeInfo.isExit || +wholeActionFlag !== 1) {
                setIsShowWhole(false);
            }
            else {
                setIsShowWhole(true);
            }
        }
    };

    return (
        <div className={`white-screen-patrol livePreview ${source}Matrix ${showReflowTopPad && 'reflow-top-pad'}`}>
            {/* 全局调起蒙层 */}
            {isShowWhole && !isMatrixApp && <div className='whole-mask' onClick={handleWholeClk}></div>}

            <PreHeader {...{cover, video_url}} />

            <AuthorArea {...data} refresh={refresh} handleInvoke={handleInvoke} />

            <div className={style.mainArea}>
                {/* 这里加系列课,优先级问PM,有series_nid的话只展示系列课信息和倒计时 */}
                {series_nid ? (
                    <React.Fragment>
                        <CountDown
                            status={status}
                            countdown={countdown}
                            refresh={refresh}
                            chatMcastId={chat_mcast_id}
                            reliableMcastId={reliable_mcast_id}
                        />

                        <CourseDetail
                            isLogin={is_login}
                            series_nid={series_nid}
                            appointment_status={appointment_status}
                            refresh={refresh}
                        />
                    </React.Fragment>
                ) : (
                    <React.Fragment>
                        {isGroupPaid ? (
                            <GroupBuy {...data} />
                        ) : (
                            <React.Fragment>
                                {/* 倒计时 */}
                                <CountDown
                                    status={status}
                                    countdown={countdown}
                                    refresh={refresh}
                                    setShowLiveBtn={setShowLiveBtn}
                                    chatMcastId={chat_mcast_id}
                                    reliableMcastId={reliable_mcast_id}
                                />
                                {/* 电商优惠券 */}
                                {
                                    +template_id === 2 && <Coupon changeCoupon={setCouponStatus} multiple={multiple} />
                                }
                                {/* 按钮区域 */}
                                {showLiveBtn && (
                                    <LiveButtons
                                        cRef={childRef}
                                        {...{
                                            appointment_status,
                                            is_login,
                                            is_paying_user,
                                            pay_info,
                                            has_pay_service,
                                            pay_nid,
                                            refresh,
                                            couponStatus,
                                            handleInvoke,
                                            noreflow
                                        }}
                                    />
                                )}
                                {/* 商品 */}
                                {
                                    +template_id === 2 && <Goods couponStatus={couponStatus} multiple={multiple} />
                                }
                            </React.Fragment>
                        )}

                        {hasArrValue(topic_select) ? (
                            <QuestionList topic_select={topic_select} />
                        ) : (
                            <RichSnippets description={description} new_description={new_description} />
                        )}

                        {isGroupPaid && <GroupRule />}

                        {/* 主讲人 */}
                        {hasArrValue(lecturer_list) && <LiveSpeaker speakerList={lecturer_list} />}

                        {/* 系列直播 */}
                        {hasArrValue(compilation_list) ? (
                            <LiveSeries seriesList={compilation_list} compilation_id={compilation_id} />
                        ) : (
                            <div className={style.empty} />
                        )}
                    </React.Fragment>
                )}
            </div>

            {/* 弹窗与浮层 */}
            {showDialog && <BooksDialog setShowDialog={setShowDialog} />}
            {!hideReflowBar && <ReflowBar
                has_reflow_bar={has_reflow_bar}
                series_nid={series_nid}
                isGroupPaid={isGroupPaid}
                handleInvoke={handleInvoke}
                noreflow
                source={source}
                isMatrixApp={isMatrixApp}
            />}
        </div>
    );
}

export default Preview;
