@import '~@baidu/nano-theme/index.less';

.theme-root() !important;

@keyframes circleBreath {
    0% {
        border-width: 1px;
        transform: scale(1.12);
    }

    12.41% {
        border-width: 1px;
        transform: scale(1.12);
    }

    87.59% {
        border-width: 1px;
        transform: scale(1.32);
    }

    100% {
        opacity: 0;
        border-width: 0;
        transform: scale(1.32);
    }
}

@keyframes avatarBreath {
    0% {
        transform: scale(.95);
    }

    50% {
        transform: scale(1.01);
    }

    100% {
        transform: scale(.95);
    }
}

.authorArea {
    .authorHeader,
    & {
        .theme-background(GC9);

        box-sizing: border-box;
        display: flex;
        flex-wrap: nowrap;
        width: 100%;
        padding: .15rem .17rem;
        justify-content: space-between;
        align-items: center;
        line-height: 1;
    }

    .authorHeader {
        padding: 0;
    }

    .author {
        height: 100%;
        display: flex;
        align-items: center;
    }

    .avatarContainer {
        box-sizing: content-box;
        position: relative;
        width: .35rem;
        height: .35rem;
        margin-right: .07rem;
        border-radius: 50%;
        .theme-background(GC57);

        border-width: 1px;
        border-style: solid;
        .theme-border(GC56);

        &::after {
            position: absolute;
            z-index: 6;
            left: 0;
            top: 0;
            transform-origin: left top;
            box-sizing: border-box;
            pointer-events: none;
            content: ' ';
            width: 300%;
            height: 300%;
            transform: scale(.33333333);
            border: 1px solid rgba(0, 0, 0, .1);
            border-radius: 50%;
        }
    }

    .marginBig {
        margin-right: .12rem;
    }

    .avatarHover {
        position: absolute;
        left: 50%;
        top: 50%;
        width: .27rem;
        height: .27rem;
        background: url(https://b.bdstatic.com/searchbox/icms/searchbox/img/image_holder_light.png) 0 0 no-repeat;
        background-size: .27rem .27rem;
        transform: translate(-50%, -50%);
        z-index: 1;
    }

    .avatar {
        width: .35rem;
        height: .35rem;
        border-radius: 50%;
        position: relative;
        z-index: 2;
    }

    .authorContent {
        height: .35rem;
        margin-right: .17rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .authorContent .authorName {
        .theme-color(GC1);

        font-size: .16rem;
    }

    .authorContent.midPos {
        justify-content: center;
    }

    .authorContent .authorFollow {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        height: .12rem;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        line-height: .14rem;
        -webkit-box-orient: vertical;
        font-size: .12rem;
        .theme-color(GC4);
    }

    .liveHeadBreath {
        animation: avatarBreath ease-in-out 1330ms infinite;
    }
}

.authorHeaderFixed {
    position: fixed;
    top: 0;
    z-index: 99;
    padding: .11rem .17rem;

    .liveCircleBreath {
        width: .28rem;
        height: .28rem;
    }

    .liveCircle {
        width: .34rem;
        height: .34rem;
    }

    .avatar,
    .avatarContainer {
        width: .28rem;
        height: .28rem;
    }

    .authorContent .authorName {
        font-size: .14rem;
    }

    .authorContent {
        height: .28rem;
    }

    .authorContent.midPos {
        justify-content: center;
    }

    .authorContent .authorFollow {
        font-size: .1rem;
    }

    .avatarHover {
        background-size: .22rem .22rem;
    }
}

.stickHide {
    display: none;
}

.stickShow {
    display: block;
}

.authorInfo {
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    padding: 0 .17rem;
    justify-content: space-between;
    align-items: center;
    line-height: 1;

    .authorTitle {
        font-size: .19rem;
        font-weight: 700;
        .theme-color(GC1);

        letter-spacing: 0;
        line-height: .27rem;
    }

    .authorAppointment {
        width: 100%;
        margin-top: .15rem;
        height: .14rem;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: space-between;
        font-size: .14rem;
        .theme-color(GC2);

        line-height: .14rem;

        div {
            position: relative;
        }

        div::before {
            position: absolute;
            width: .14rem;
            height: .14rem;
            content: ' ';
        }

        .aptTime {
            padding-left: .22rem;
        }

        .aptTime::before {
            left: 0;
            background: url(https://b.bdstatic.com/searchbox/icms/searchbox/img/time-light.png) 0 0 no-repeat;
            background-size: contain;
        }

        .aptCount::before {
            left: -.2rem;
            background: url(https://b.bdstatic.com/searchbox/icms/searchbox/img/person-light.png) 0 0 no-repeat;
            background-size: contain;
        }

        .buyedCount {
            color: #f35;
        }
    }
}

.darkmode .authorAppointment .aptTime::before {
    background: url(https://b.bdstatic.com/searchbox/icms/searchbox/img/time-dark.png) 0 0 no-repeat;
    background-size: .14rem .14rem;
}

.darkmode .authorAppointment .aptCount::before {
    background: url(https://b.bdstatic.com/searchbox/icms/searchbox/img/person-dark.png) 0 0 no-repeat;
    background-size: .14rem .14rem;
}

.darkmode .avatarHover {
    background: url(https://b.bdstatic.com/searchbox/icms/searchbox/img/image_holder_dark.png) 0 0 no-repeat;
    background-size: .27rem .27rem;
}

.liveTag {
    box-sizing: content-box;
    position: absolute;
    left: -.04rem;
    bottom: -.09rem;
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: .4rem;
    height: .16rem;
    background: #f33;
    border: .013rem solid #fff;
    border-radius: .095rem;
    color: #fff;
    z-index: 10;

    .liveTagText {
        font-size: .12rem;
        transform: scale(.834);
        display: flex;
        align-items: center;
        line-height: .16rem;
        padding-left: .02rem;
    }
}

.darkmode .liveTag {
    color: #fff;
    background: #7f1919;
    border-color: #191919;
    .theme-background(GC47);
    .theme-color(FC46);
}

.haokanMatrix .liveTag {
    background: #ff4141;
}

.youjiaMatrix .liveTag {
    background: #00cecf;
}

.baidudictMatrix .liveTag {
    background: #00cb8a;
}

.fortunecatMatrix .liveTag {
    background: #c9a687;
}

.hiphopMatrix .liveTag {
    background: #5dff6c;
}

.liveCircleContainer {
    position: absolute;
    top: -.03rem;
    left: -.03rem;

    .liveCircleBreath {
        width: .35rem;
        height: .35rem;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        border-radius: 50%;
        border: 0 solid #f33;
        animation: circleBreath ease-in-out 1330ms infinite;
    }

    .liveCircle {
        width: .41rem;
        height: .41rem;
        border: .014rem solid #f33;
        box-sizing: border-box;
        border-radius: 50%;
    }
}

.darkmode .liveCircle {
    border-color: #7f1919;
}

.youjiaMatrix .liveCircle {
    border: .014rem solid #00cecf;
}

.baidudictMatrix .liveCircle {
    border: .014rem solid #00cb8a;
}

.fortunecatMatrix .liveCircle {
    border: .014rem solid #c9a687;
}

.hiphopMatrix .liveCircle {
    background: #5dff6c;
}

.haokanMatrix .liveCircle {
    border: .014rem solid #ff4141;
}

.darkmode .liveCircleBreath {
    border-color: #f33;
}

.youjiaMatrix .liveCircleBreath {
    border: 0 solid #00cecf;
}

.baidudictMatrix .liveCircleBreath {
    border: 0 solid #00cb8a;
}

.fortunecatMatrix .liveCircleBreath {
    border: 0 solid #c9a687;
}

.hiphopMatrix .liveCircleBreath {
    border: 0 solid #5dff6c;
}

.haokanMatrix .liveCircleBreath {
    border: 0 solid #ff4141;
}
