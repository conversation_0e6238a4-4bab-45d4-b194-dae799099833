/**
 * @file 作者信息+关注区域组件
 * <AUTHOR> ji<PERSON><PERSON><PERSON><PERSON>@baidu.com on 2020-06-15 20:59:32.
 */

import React, {useEffect, useRef, useState} from 'react';
import FollowBtn from '@/pages/liveshow/components/common/FollowBtn';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import {getStartTimeStr} from '@/pages/liveshow/utils/time';
import {logHeadClick} from '@/pages/liveshow/utils/thunder';
import {backflow} from '@/utils/backflow';
import cls from 'classnames';
import style from './index.less';

function AuthorArea(props) {
    const authorArea = useRef(null);

    const [state, setState] = useState({
        fakePositionHeight: 0,
        isStickTop: false,
        offsetTop: 0
    });

    useEffect(() => {
        const height = authorArea.current.clientHeight;
        const offsetTop = authorArea.current.offsetTop;
        setState((sta) => ({
            ...sta,
            offsetTop,
            fakePositionHeight: height
        }));
    }, []);

    useEffect(() => {
        const onScroll = () => {
            const scrollTop = document.scrollingElement.scrollTop;
            if (isStickTop !== scrollTop >= state.offsetTop) {
                setState((sta) => {
                    return {
                        ...sta,
                        isStickTop: scrollTop >= state.offsetTop
                    };
                });
            }
        };
        window.addEventListener('scroll', onScroll);
        return () => window.removeEventListener('scroll', onScroll);
    }, [state]);

    const toAuthorPage = () => {
        const {isLive, follow_id, follow_type, living_cmd, isGroupPaid, handleInvoke} = props;

        if (boxjsUtils.platformInfo.isBox) {
            if (isLive) {
                // 跳转NA直播键
                boxjsUtils.invokeSchema({schema: living_cmd});
            }
            else {
                logHeadClick({
                    id: follow_id,
                    type: 'author',
                    page: 'preview'
                });
                boxjsUtils.toAuthorPage(follow_id, follow_type === 'ugc');
            }
        }
        else {
            if (boxjsUtils.platformInfo.isLiteBox) {
                logHeadClick({
                    id: follow_id,
                    type: 'author',
                    page: 'preview'
                });
                boxjsUtils.toAuthorPage(follow_id, follow_type === 'ugc');
                return;
            }

            if (isGroupPaid) {
                backflow({url: location.href});
            }
            else {
                // 更新为MCP 调起
                handleInvoke();
                // openReflowByUrl(living_h5);
            }
        }
    };

    const {
        avatar,
        source,
        title,
        start_time,
        end_time,
        appointment_number,
        identification,
        isLive,
        group_buy_info,
        isGroupPaid,
        followStatus,
        follow_id,
        follow_type,
        is_login,
        refresh,
        template
    } = props;

    const {fakePositionHeight, isStickTop} = state;

    return (
        <div>
            <div
                ref={authorArea}
                className={cls({
                    [style.authorArea]: true,
                    [style.authorHeaderFixed]: isStickTop
                })}
            >
                <div className={style.authorHeader}>
                    <div className={style.author}>
                        <div
                            className={cls({[style.avatarContainer]: true, [style.marginBig]: false && isLive})}
                            onClick={toAuthorPage}
                        >
                            <img
                                className={cls({[style.avatar]: true, [style.liveHeadBreath]: false && isLive})}
                                src={avatar}
                            />
                            <div className={style.avatarHover} />
                            {/* 暂屏蔽预约页面 主播直播中呼吸态 */}
                            {/* {isLive && !isStickTop && (
                                <div className={style.liveTag}>
                                    <span className={style.liveTagText}>直播中</span>
                                </div>
                            )}
                            {isLive && (
                                <div className={style.liveCircleContainer}>
                                    <div className={style.liveCircleBreath} />
                                    <div className={style.liveCircle} />
                                </div>
                            )} */}
                        </div>
                        <div className={cls({[style.authorContent]: true, [style.midPos]: !identification})}>
                            <div className={style.authorName} onClick={toAuthorPage}>
                                {source}
                            </div>
                            {identification && <div className={style.authorFollow}>{identification}</div>}
                        </div>
                    </div>

                    <FollowBtn
                        isPreview
                        {...{
                            followStatus,
                            third_id: follow_id,
                            type: follow_type,
                            is_login,
                            refresh,
                            template
                        }}
                    />
                </div>
            </div>
            <div
                className={isStickTop ? style.stickShow : style.stickHide}
                style={{height: `${fakePositionHeight}px`}}
            />
            <div className={style.authorInfo}>
                <div className={style.authorTitle}>{title}</div>
                <div className={style.authorAppointment}>
                    <div className={style.aptTime}>{getStartTimeStr(start_time, end_time)}</div>
                    {isGroupPaid ? (
                        <div className={style.aptCount}>
                            <span className={style.buyedCount}>{+group_buy_info.group_buyed_count}</span>/
                            {+group_buy_info.group_buy_sell_limit}已参团
                        </div>
                    ) : (
                        appointment_number > 0 && (
                            <div className={style.aptCount}>
                                {appointment_number}
                                人预约
                            </div>
                        )
                    )}
                </div>
            </div>
        </div>
    );
}

export default AuthorArea;
