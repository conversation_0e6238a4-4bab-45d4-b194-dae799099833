/**
 * @file 主讲人组件
 * <AUTHOR> ji<PERSON><PERSON><PERSON><PERSON>@baidu.com on 2020-06-16 11:04:58.
 */

import React from 'react';
import style from './index.less';

export default function LiveSpeaker({speakerList}) {
    return (
        <div className={style.speakerContain}>
            <h4>主讲人</h4>
            <ul className={style.speakerList}>
                {speakerList.map((ele, key) => (
                    <li key={key}>
                        <div className={style.speakerTitle}>
                            <div className={style.speakerAvatar}>
                                <img src={ele.avatar} alt='' className={style.speakerIcon} />
                                <div className={style.speakerAvatarHover} />
                            </div>
                            <span className={style.speakerName}>{ele.name}</span>
                        </div>
                        <p className={style.speakerDec}>{ele.description}</p>
                    </li>
                ))}
            </ul>
        </div>
    );
}
