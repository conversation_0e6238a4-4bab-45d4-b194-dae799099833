@import '~@baidu/nano-theme/index.less';

.speakerContain {
    margin-top: .36rem;

    h4 {
        font-size: .19rem;
        font-weight: 800;

        .theme-color(GC1);
    }

    .speakerList {
        display: flex;
        width: 100%;
        flex-wrap: wrap;

        li {
            width: 100%;
            margin-top: .21rem;
            padding: .12rem;
            list-style: none;
            border-radius: .05rem;

            .theme-background(GC86);

            .speakerTitle {
                display: flex;
                flex-direction: row;
                width: 100%;
                height: .35rem;
                flex-wrap: nowrap;
                justify-content: flex-start;

                .speakerAvatar {
                    position: relative;
                    overflow: hidden;
                    box-sizing: border-box;
                    width: .35rem;
                    height: .35rem;
                    margin-right: .07rem;
                    border-width: 1px;
                    border-style: solid;
                    border-radius: 50%;

                    .theme-background(GC57);
                    .theme-border(GC56);

                    &::after {
                        content: ' ';
                        position: absolute;
                        z-index: 7;
                        top: 0;
                        left: 0;
                        box-sizing: border-box;
                        width: 300%;
                        height: 300%;
                        border: 1px solid rgba(0, 0, 0, .1);
                        border-radius: 50%;
                        transform: scale(.33333333);
                        transform-origin: left top;
                        pointer-events: none;
                    }

                    .speakerIcon {
                        position: relative;
                        z-index: 2;
                        width: .35rem;
                        height: .35rem;
                        border-radius: 50%;
                    }

                    .speakerAvatarHover {
                        position: absolute;
                        z-index: 1;
                        top: 50%;
                        left: 50%;
                        width: .27rem;
                        height: .27rem;
                        background: url(https://b.bdstatic.com/searchbox/icms/searchbox/img/image_holder_light.png) 0 0 no-repeat;
                        background-size: .27rem .27rem;
                        transform: translate(-50%, -50%);
                    }
                }

                .speakerName {
                    padding-left: .01rem;
                    font-size: .16rem;
                    line-height: .35rem;

                    .theme-color(GC1);
                }
            }

            .speakerDec {
                margin-top: .075rem;
                font-size: .16rem;
                line-height: .25rem;

                .theme-color(GC3);
            }
        }
    }
}
