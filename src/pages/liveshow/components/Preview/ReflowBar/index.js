/**
 * @file 预约模块弹窗
 * <AUTHOR>
 * @date 2021-11-15 22:07:19
 */
import React, {useState, useEffect} from 'react';
import cls from 'classnames';
import {getAppName} from '@/pages/liveshow/utils/getUrlParam';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import {isOppoBrowser, env} from '@/utils/env';
import {backflow} from '@/utils/backflow';
import style from './index.less';
import { commonShowLog, commonClickLog, reflowCallbackLog} from '@/pages/liveshow/log';
import baiduboxapp_logo from './img/baiduboxapp_logo.png';
import lite_logo from './img/lite_logo.png';

const appName = getAppName();

// 新版顶bar回流logo
const newLogoMap = {
    'baiduboxapp': baiduboxapp_logo,
    'lite': lite_logo
};

export default function ReflowBar({
    has_reflow_bar,
    series_nid,
    isGroupPaid,
    noreflow = false,
    handleInvoke,
    source = '',
    isMatrixApp = false
}) {

    if (
        series_nid ||
        !has_reflow_bar ||
        boxjsUtils.platformInfo.isBox ||
        boxjsUtils.platformInfo.isLiteBox ||
        isOppoBrowser()
    ) {
        return null;
    }
    const [show, setShow] = useState(true);

    const classBox = cls({
        [style.backflowWrap]: true,
        [style.top]: true,
        [style.contentPadding]: true,
    });

    const closeReflowBar = () => {
        setShow(false);
    };

    const reflow = (type) => {
        if (type !== 'wechatFail') {
            commonClickLog('-1', 'pre_top_btn', '', 0, '15263', true);
        }
        if (isGroupPaid) {
            backflow({url: location.href});
        } else {
            handleInvoke();
        }
    };

    useEffect(() => {
        // 预约按钮展现打点
        commonShowLog('-1', 'pre_top_btn', '', 0, '15263', true);
    }, []);

    return show ? (
        <div className={style.backFlow}>
            {
                // 回流主板 或 极速版 升级为新版顶bar
                (!isMatrixApp || ['lite'].indexOf(source) > -1) ? (
                    <div className="new-backflow-wrap" onClick={reflow}>
                        <img className="matrix-logo" src={newLogoMap[source] || baiduboxapp_logo} />
                        <div className="backflow-app">APP内播放</div>
                    </div>
                ) : (
                    <div className={classBox}>
                        <div className={style.backflowClose} onClick={closeReflowBar} />
                        <div className='back-reflow-outer'>
                            <div className={style.backflowBox} onClick={reflow}>
                                <div className={style.bdLogo} />
                                <div className={style.flowText}>
                                    <p className={style.title}>
                                        打开
                                        {appName}
                                        APP
                                    </p>
                                </div>
                            </div>
                            <div className={style.backflowApp} onClick={reflow}>
                                <span>打开</span>
                            </div>
                        </div>
                    </div>
                )
            }
        </div>
    ) : (
        ''
    );
}
