@import '~@baidu/nano-theme/index.less';

.contentPadding {
    padding-left: 17px;
    padding-right: 17px;
}

.backFlow {
    margin: 0;
    padding: 0;
    position: relative;
    width: 100%;
    height: .56rem;
    z-index: 99;

    .new-backflow-wrap {
        box-sizing: border-box;
        height: .56rem;
        position: fixed;
        display: flex;
        align-items: center;
        justify-content: space-between;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 2;
        background-color: rgba(255, 255, 255, 1);
        padding: .106rem .17rem;

        .matrix-logo {
            width: auto;
            height: .34667rem;
            object-fit: contain;
        }

        .backflow-app {
            width: .89rem;
            height: .33rem;
            background-color: #4e6ef2;
            border-radius: .2rem;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: .13rem;
            font-weight: 500;
        }
    }

    .backflowWrap {
        box-sizing: border-box;
        height: .56rem;
        position: fixed;
        display: flex;
        align-items: center;
        justify-content: center;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 2;
        background-color: rgba(0, 0, 0, .7);

        &.top {
            top: 0;
        }

        .backflowClose {
            display: inline-block;
            width: .18rem;
            height: .18rem;
            margin-right: .1rem;
            background-image: url('https://b.bdstatic.com/searchbox/icms/searchbox/img/reflow-close.png');
            background-size: .18rem .18rem;
        }

        .back-reflow-outer {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .backflowBox {
            flex: 1;
            display: flex;
            align-items: center;

            .bdLogo {
                background: url('https://s.bdstatic.com/common/openjs/openBox/shareEntry-logo.png') no-repeat;
                background-size: .34rem;
                width: .34rem;
                height: .34rem;
                margin-right: .06rem;
            }

            .flowText {
                flex: 1;

                p {
                    text-align: left;
                    font-size: .14rem;
                    color: #fff;
                }
            }
        }

        .backflowApp {
            margin-left: .12rem;

            span {
                display: block;
                width: .57rem;
                height: .28rem;
                border-radius: .05rem;
                background-color: #38f;
                font-size: .13rem;
                text-align: center;
                line-height: .28rem;
                color: #fff;
            }
        }
    }
}

.haokanMatrix {
    .backFlow {
        .backflowWrap {
            .backflowApp {
                span {
                    background: #ff4141;
                }
            }

            .bdLogo {
                background: url(https://mpics.bdstatic.com/qm/202207/pic_6eumaJ_1657261544.png) no-repeat;
                background-size: .34rem;
            }
        }
    }
}

.youjiaMatrix {
    .backFlow {
        .backflowWrap {
            .backflowApp {
                span {
                    background: #00cecf;
                }
            }

            .bdLogo {
                background: url(https://youjia.baidu.com/static/car-pc/favicon.ico) no-repeat;
                background-size: .34rem;
            }
        }
    }
}

.baidudictMatrix {
    .backFlow {
        .backflowWrap {
            .backflowApp {
                span {
                    background: #00cb8a;
                }
            }

            .bdLogo {
                background: url(https://b.bdstatic.com/searchbox/icms/searchbox/img/baidudict.png) no-repeat;
                background-size: .34rem;
            }
        }
    }
}

.fortunecatMatrix {
    .backFlow {
        .backflowWrap {
            .backflowApp {
                span {
                    background: #c9a687;
                }
            }

            .bdLogo {
                background: url(https://b.bdstatic.com/searchbox/icms/searchbox/img/fortunecat.png) no-repeat;
                background-size: .34rem;
            }
        }
    }
}

.hiphopMatrix {
    .backFlow {
        .backflowWrap {
            .backflowApp {
                span {
                    background: #5dff6c;
                }
            }

            .bdLogo {
                background: url(./img/hiphop.png) no-repeat;
                background-size: .34rem;
            }
        }
    }
}
