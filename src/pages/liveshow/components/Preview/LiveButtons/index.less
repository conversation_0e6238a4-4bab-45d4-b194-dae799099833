@import '~@baidu/nano-theme/index.less';

.theme-root() !important;

.preview-btn-wrap {
    position: relative;
}

.subscribeSubBtn {
    width: 100%;
    position: relative;
    height: .399rem;
    border-radius: .2rem;
    .theme-color(GC67);
    .theme-background(GC51);

    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 700;

    &.btnCancel,
    &.subscribeCancel {
        .theme-color(GC4);
        .theme-background(GC54);
    }

    &.btnCancel::after,
    &.subscribeCancel::after {
        content: '';
        display: block;
        position: absolute;
        left: 0;
        top: 0;
        width: 300%;
        height: 300%;
        .theme-border(GC55);

        border-style: solid;
        border-width: 2px;
        border-radius: .6rem;
        transform: scale(.333);
        transform-origin: 0 0;
    }

    &:active {
        opacity: .2;
    }

    &:disabled {
        opacity: .7;
    }
}

.haokanMatrix {
    .subscribeSubBtn {
        background: #ff4141;
    }
}

.youjiaMatrix {
    .subscribeSubBtn {
        background: #00cecf;
    }
}

.baidudictMatrix {
    .subscribeSubBtn {
        background: #00cb8a;
    }
}

.fortunecatMatrix {
    .subscribeSubBtn {
        background: #c9a687;
    }
}

.hiphopMatrix {
    .subscribeSubBtn {
        background: #5dff6c;
    }
}

.purchaseSubBtn {
    position: relative;
    height: .39rem;
    .theme-color(GC62);
    .theme-background(GC80);

    border-radius: .2rem;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 700;

    &::after {
        content: '';
        display: block;
        position: absolute;
        left: 0;
        top: 0;
        width: 300%;
        height: 300%;
        box-sizing: border-box;
        .theme-border(GC81);

        border-style: solid;
        border-width: 2px;
        border-radius: .6rem;
        transform: scaleX(.332) scaleY(.3299);
        transform-origin: 0 0;
    }

    &.btnToFans {
        background-image: linear-gradient(180deg, #ff1f66 0%, #ff4d4d 100%);
        color: #fff;
        border-color: none;
        position: relative;
    }

    &.btnToFans::after {
        border-color: #fff;
        border: none;
    }

    &.btnToBookList {
        .theme-color(GC68);
        .theme-background(GC52);
    }

    &.btnToBookList::after {
        .theme-border(GC53);
    }

    &:active {
        opacity: .2;
    }

    .fans-icon {
        width: .6rem;
        height: .17rem;
        background-image: url(./img/fans-icon.png);
        background-size: 100% 100%;
        position: absolute;
        right: 0;
        top: -.1rem;
        display: inline-block;
    }
}

.subscribeBtnWrapper {
    width: 100%;
    box-sizing: border-box;
    bottom: 0;
    left: 0;
    display: flex;
    justify-content: space-between;
    .theme-background(GC9);

    .subscribeSubBtn,
    .purchaseSubBtn {
        width: 49%;
    }

    .btnCancel::after {
        transform: scale(.32);
    }
}
