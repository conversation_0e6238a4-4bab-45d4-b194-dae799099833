/**
 * @file 预约按钮组件
 * <AUTHOR>
 * @date 2021-11-09 20:37:48
 */

import React, { useState, useRef, useEffect, useImperativeHandle } from 'react';
import { useUpdateEffect } from 'ahooks';
import cls from 'classnames';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import { env, isLargerVersion } from '@/utils/env';
import {
    subscribe
} from '@/pages/liveshow/service/api';
import { logPurchaseShow, logPurchaseClick, logItemClick } from '@/pages/liveshow/utils/thunder';
import { commonShowLog, commonClickLog, reflowCallbackLog} from '@/pages/liveshow/log';

import style from './index.less';

function showMoney(pay_info) {
    const discount = +pay_info.is_discount === 1;
    if (boxjsUtils.platformInfo.isIOS) {
        return discount ? pay_info.discount_money_ios_str : pay_info.pay_money_ios_str;
    } else {
        return discount ? pay_info.discount_money_android_str : pay_info.pay_money_android_str;
    }
}

function PayLive(props) {
    const purchaseBtn = useRef(null);

    useEffect(() => {
        logPurchaseShow();
    }, []);

    const { appointment_status, is_paying_user, pay_nid, pay_info, clickSubscribe } = props;

    const subscribeClassList = cls({
        [style.subscribeSubBtn]: true,
        [style.btnCancel]: +appointment_status === 1,
    });

    const payingUser = +is_paying_user === 1;
    const payClassList = cls({
        [style.purchaseSubBtn]: true,
        [style.btnToBookList]: payingUser,
        [style.btnToFans]: !payingUser,
    });

    const clickPurchaseBtn = () => {
        if (!boxjsUtils.platformInfo.isBox) {
            handleInvoke();
            return;
        }
        if (isLargerVersion(env.boxVersion, '11.18')) {
            logPurchaseClick();
            if (boxjsUtils.platformInfo.isLiteBox) {
                return;
            }
            if (+is_paying_user === 0) {
                // 去购买
                boxjsUtils.purchase({
                    id: pay_nid,
                    type: 'liveshow',
                });
            } else if (payingUser) {
                // 图书架
                boxjsUtils.invokeSchema({ schema: boxjsUtils.BOOK_LIST_SCHEME });
            }
        } else {
            boxjsUtils.showToast('该版本不支持购买，请升级客户端');
        }
    };

    return (
        <div className={style.subscribeBtnWrapper}>
            <div ref={purchaseBtn} className={payClassList} onClick={clickPurchaseBtn}>
                {payingUser ? (
                    '去书架'
                ) : (
                    <React.Fragment>
                        <span className='fans-icon'></span>
                        {`${showMoney(pay_info)} 购买直播`}
                    </React.Fragment>
                )}
            </div>

            <button className={subscribeClassList} onClick={clickSubscribe}>
                {+appointment_status === 1 ? '取消预约' : '立即预约'}
            </button>
        </div>
    );
}

function LiveButtons(props) {
    const [loading, setLoading] = useState(false);
    const { appointment_status, has_pay_service, is_login, refresh, is_paying_user, pay_info, pay_nid, cRef,
        couponStatus, noreflow = false, handleInvoke} = props;

    useImperativeHandle(cRef, () => ({
        subscribe: () => {
            clickSubscribe();
        },
    }));

    useUpdateEffect(() => {
        setLoading(false);
    }, [appointment_status]);

    const appointmentStatus = +appointment_status === 0;
    const payService = +has_pay_service === 1;

    const resSubEnd = (success, requestStatus) => {
        logItemClick({
            type: `${requestStatus}`,
            status: success ? '1' : '0',
        });
        const sucToast = requestStatus ? '预约成功' : '取消预约成功';
        const failToast = requestStatus ? '预约失败' : '取消预约失败';
        boxjsUtils.showToast(success ? sucToast : failToast);

        // 通知开启系统通知的弹窗
        if (requestStatus === 1) {
            setTimeout(() => {
                boxjsUtils.systemNotify('live_sub');
            }, 500);
        }

        if (success) {
            refresh && refresh();
        }

        setLoading(false);
    };

    const clickSubscribe = (type) => {
        if (loading) {
            return;
        }
        // 微信标签调起失败不再打点击点
        if (type !== 'wechatFail') {
            commonClickLog('-1', 'pre_sub_btn', '', 0, '15263', true);
        }
        // 百度app && 百度看看 && 百度极速版 && 鸿蒙百度app中可以直接进行预约，其他端回流到手百
        if (!boxjsUtils.platformInfo.isBox
            && !boxjsUtils.platformInfo.isLiteBox
            && !boxjsUtils.platformInfo.isHarmonyBox) {
            handleInvoke();
            return;
        }
        if (is_login) {
            setLoading(true);
            const requestStatus = appointmentStatus ? 1 : 0;
            subscribe(requestStatus)
                .then((success) => resSubEnd(success, requestStatus))
                .catch(() => resSubEnd(false, requestStatus));
        } else {
            // 没有登录去登录,登录成功后刷新数据
            boxjsUtils.login().then(() => {
                // 一定延时后调用(解决安卓11.20这个逻辑运行不稳定的问题)
                window.setTimeout(() => {
                    refresh();
                }, 500);
            });
        }
    };

    useEffect(() => {
        // 预约按钮展现打点
        commonShowLog('-1', 'pre_sub_btn', '', 0, '15263', true);

    }, []);

    const appointmentClass = cls({
        [style.subscribeSubBtn]: true,
        [style.subscribeCancel]: !appointmentStatus,
    });

    return (
        <div className='preview-btn-wrap'>
            {
                payService ? (
                    <PayLive
                        {...{ pay_nid, appointment_status, is_paying_user, pay_info }}
                        clickSubscribe={clickSubscribe}
                    ></PayLive>
                ) : (
                    <div className='subscribeSubBtnWrap'>
                        <button className={appointmentClass} onClick={clickSubscribe} disabled={loading}>
                            {appointmentStatus ? (couponStatus === 2 ? '领券并预约' : '立即预约') : '取消预约'}
                        </button>
                    </div>
                )
            }
        </div>
    );
}

export default LiveButtons;
