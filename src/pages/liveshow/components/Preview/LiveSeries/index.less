@import '~@baidu/nano-theme/index.less';
.theme-root() !important;

li {
    list-style: none;
}

.clearfix {
    zoom: 1;
}

.clearfix::before,
.clearfix::after {
    display: table;
    content: ' ';
}

.clearfix::after {
    clear: both;
}

.seriesContain {
    width: 100%;
    margin-top: .36rem;

    h4 {
        font-size: .19rem;
        margin-bottom: .21rem;
        font-weight: 800;
        .theme-color(GC1);
    }

    ul {
        width: 100%;

        li {
            float: left;
            width: 49%;
            display: flex;
            flex-wrap: wrap;
            justify-content: left;

            &.margin {
                margin-left: 2%;
            }

            margin-bottom: .15rem;

            .seriesBanner {
                width: 100%;
                border-radius: .09rem;
                height: 1.05rem;
                overflow: hidden;
                position: relative;
                .theme-background(GC57);

                border-width: 1px;
                border-style: solid;
                .theme-border(FC19);

                img {
                    width: 100%;
                    height: 100%;
                    position: relative;
                    z-index: 3;
                }

                .seriesTips {
                    height: .18rem;
                    position: absolute;
                    left: .05rem;
                    top: .05rem;
                    border-radius: .1rem;
                    overflow: hidden;
                    // background: rgba(0, 0, 0, 0.2);
                    .theme-background(FC135);

                    display: flex;
                    flex-wrap: nowrap;
                    flex-direction: row;
                    justify-content: space-around;
                    z-index: 4;

                    span {
                        font-size: .12rem;
                        text-shadow: 0 0 3px rgba(0, 0, 0, .2);
                        line-height: .18rem;
                        color: #fff;
                        padding: 0 .05rem;
                    }

                    span.seriesStatus {
                        height: 100%;
                        border-radius: .1rem;

                        .seriesStatusTxt {
                            display: inline-block;
                            padding: 0;
                            transform: scale(.9);
                        }
                    }

                    .preview {
                        .theme-background(FC94);
                    }

                    .live {
                        .theme-background(FC47);
                    }

                    .playback {
                        .theme-background(FC92);
                    }

                    span.seriesNums {
                        transform: scale(.9);
                        padding-left: .01rem;
                    }
                }

                .seriesHover {
                    position: absolute;
                    left: 50%;
                    top: 50%;
                    width: .27rem;
                    height: .27rem;
                    background: url(https://b.bdstatic.com/searchbox/icms/searchbox/img/image_holder_light.png) 0 0 no-repeat;
                    background-size: .27rem .27rem;
                    transform: translate(-50%, -50%);
                    z-index: 1;
                }
            }

            .seriesInfo {
                display: flex;
                flex-wrap: wrap;
                flex-direction: column;
                margin-top: .08rem;

                p {
                    // height: .42rem;
                    font-size: .16rem;
                    .theme-color(GC1);

                    line-height: .21rem;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                }

                .seriesAuthor {
                    margin-top: .3rem;
                    display: flex;
                    flex-wrap: nowrap;
                    flex-direction: row;
                    justify-content: left;
                    position: relative;

                    img {
                        width: .16rem;
                        height: .16rem;
                        border-radius: 50%;
                        box-sizing: border-box;
                        border-width: 1px;
                        border-style: solid;
                        .theme-border(GC56);

                        &::after {
                            position: absolute;
                            z-index: 6;
                            left: 0;
                            top: 0;
                            transform-origin: left top;
                            box-sizing: border-box;
                            pointer-events: none;
                            content: ' ';
                            width: 300%;
                            height: 300%;
                            transform: scale(.33333333);
                            border: 1px solid rgba(0, 0, 0, .1);
                            border-radius: 50%;
                        }
                    }

                    span {
                        padding-left: .06rem;
                        font-size: .12rem;
                        line-height: .16rem;
                        .theme-color(GC4);
                    }
                }
            }
        }
    }
}

.noMore {
    width: 100%;
    font-size: .14rem;
    line-height: .4rem;
    text-align: center;
    .theme-color(GC2);
    .theme-background(GC9);
}

i.preview {
    background: url(https://b.bdstatic.com/searchbox/icms/searchbox/img/appointment.png) 0 0 no-repeat;
}

i.live {
    background: url(https://b.bdstatic.com/searchbox/icms/searchbox/img/live-light.gif) 0 0 no-repeat;
}

i.playback {
    background: url(https://b.bdstatic.com/searchbox/icms/searchbox/img/playback.png) 0 0 no-repeat;
}

i.preview,
i.live,
i.playback {
    display: inline-block;
    vertical-align: middle;
    width: .11rem;
    height: .11rem;
    background-size: contain;
    margin-top: -.02rem;
}
