/**
 * @file 系列直播组建组件
 * <AUTHOR>
 * @date 2021-11-09 20:37:48
 */

import React, {useRef, useEffect} from 'react';
import throttle from 'lodash/throttle';
import includes from 'lodash/includes';
import {useUpdateEffect} from 'ahooks';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import {openReflowByUrl} from '@/pages/liveshow/utils/reflow';
import {logLiveSeriesShow, logLiveSeriesClick} from '@/pages/liveshow/utils/thunder';
import style from './index.less';

const LIVE_STATUS_MAP = {
    // 直播类型 1预告、2直播、3回放
    1: {
        desc: '预告',
        color: 'rgba(51, 136, 255, 1)',
        icon: 'preview',
    },
    2: {
        desc: '直播中',
        color: 'rgba(255, 51, 51, 1)',
        icon: 'live',
    },
    3: {
        desc: '回放',
        color: 'rgba(0,0,0,.5)',
        icon: 'playback',
    },
};

const dispatchShowLog = (seriesList, compilation_id) => {
    let showLogSiwtch = true;
    let showLogIdList = []; // 已经出现的合集 id
    let hasReportList = []; // 已经提交的 id
    return (parentNode) => {
        const childDom = parentNode.childNodes;
        const bound = parentNode.getBoundingClientRect();
        if (bound.height < 10 || !showLogSiwtch) {
            // 随便一个值 保证有内容
            return;
        }
        try {
            const clientHeight = document.documentElement.clientHeight;
            childDom.forEach((ele, index) => {
                const childBound = ele.getBoundingClientRect();
                const top = childBound.top;
                const bottom = childBound.bottom;
                if (top <= clientHeight && bottom <= clientHeight + 40) {
                    if (!includes(showLogIdList, index) && !includes(hasReportList, index)) {
                        // 计数统计上报
                        showLogIdList.push(index);
                    }
                    if (index === childDom.length - 1) {
                        // 全部统计完成 停止滚动统计
                        showLogSiwtch = false;
                    }
                }
            });
            if (showLogIdList.length) {
                logLiveSeriesShow({
                    compilation_id,
                    compilation_list: seriesList,
                    id: showLogIdList,
                    reportSuccess: () => {
                        hasReportList.push(...showLogIdList);
                        showLogIdList = [];
                    },
                });
            }
        } catch (error) {
            //
        }
    };
};

function ShowSeries({data, index, goToLiveRoom}) {
    const {title, live_type, feed_cover, user_num, avatar, author} = data;
    const {desc, icon} = LIVE_STATUS_MAP[live_type];
    return (
        <li className={index % 2 && style.margin} onClick={() => goToLiveRoom(data, index)}>
            <div className={style.seriesBanner}>
                <img src={feed_cover} alt='' />
                <div className={style.seriesTips}>
                    <span className={`${style.seriesStatus} ${style[icon]}`}>
                        <i className={style[icon]} />
                        <span className={style.seriesStatusTxt}>{desc}</span>
                    </span>
                    {user_num && (
                        <span className={style.seriesNums}>
                            {user_num}
                            {+live_type === 1 ? '预约' : '观看'}
                        </span>
                    )}
                </div>
                <div className={style.seriesHover} />
            </div>
            <div className={style.seriesInfo}>
                <p>{title}</p>
                <div className={style.seriesAuthor}>
                    <img src={avatar} alt='' />
                    <span>{author}</span>
                </div>
            </div>
        </li>
    );
}

function LiveSeries({seriesList, compilation_id}) {
    const componentRef = useRef(null);
    const showLog = useRef(dispatchShowLog(seriesList, compilation_id));

    useUpdateEffect(() => {
        showLog.current = dispatchShowLog(seriesList, compilation_id);
    }, [seriesList]);

    useEffect(() => {
        const ref = componentRef.current;
        const logFunc = throttle(() => showLog.current(ref), 500);

        if (ref && ref.childNodes.length) {
            window.addEventListener('scroll', logFunc);
        }

        return () => {
            window.removeEventListener('scroll', logFunc);
        };
    }, [componentRef.current, seriesList]);

    const goToLiveRoom = (data, index) => {
        const {cmd, preview_url} = data;
        if (!boxjsUtils.platformInfo.isBox) {
            openReflowByUrl(preview_url);
            return;
        }
        logLiveSeriesClick({
            pos: index,
            compilation_id,
            compilation_list: seriesList,
        });
        boxjsUtils.invokeSchema({schema: cmd});
    };

    return (
        <div className={style.seriesContain}>
            <h4>系列直播</h4>
            <ul className={style.clearfix} ref={componentRef}>
                {seriesList.map((ele, key) => {
                    return <ShowSeries key={key} data={ele.data} index={key} goToLiveRoom={goToLiveRoom} />;
                })}
            </ul>
            <div className={style.noMore}>已经到底啦</div>
        </div>
    );
}

export default LiveSeries;
