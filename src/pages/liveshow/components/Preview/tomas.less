.is-tomas {
    @factor: 1.26;

    .commonFont(@fontSize, @lineHeight) {
        font-size: @fontSize * @factor;
    }

    .commonFont(@fontSize, @lineHeight) when (isnumber(@lineHeight)) {
        line-height: (@lineHeight * @factor);
    }

    .commonWidth(@width, @height) {
        width: @width * @factor;
        height: @height * @factor;
    }

    .authorArea {
        .authorContent {
            height: auto;
        }

        .avatarContainer {
            flex: none;
            .commonWidth(.35rem, .35rem);

            img {
                width: 100%;
                height: 100%;
            }
        }

        .authorContent .authorName {
            .commonFont(.16rem, .18rem);
        }

        .authorContent .authorFollow {
            height: auto;
            .commonFont(.12rem, .14rem);
        }

        .cmFollowBtn {
            width: .57rem * @factor;
            height: .28rem * @factor;
            border-radius: .14rem * @factor;
            .commonFont(.13rem, .28rem);
        }
    }

    .authorInfo {
        .authorTitle {
            .commonFont(.19rem, .27rem);
        }

        .authorAppointment {
            .commonFont(.14rem, .16rem);

            div::before {
                .commonWidth(.14rem, .14rem);
            }
        }
    }

    .mainArea {
        .infoTitle {
            .commonFont(.14rem, false);
        }

        .subscribeSubBtn {
            height: .399rem * @factor;
            border-radius: .2rem * @factor;
            .commonFont(.16rem, false);
        }

        .descriptionTitle {
            .commonFont(.19rem, false);
        }

        .description p {
            .commonFont(.16rem, .24rem);
        }
    }
}