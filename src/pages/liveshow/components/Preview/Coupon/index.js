/**
 * <AUTHOR> lubiao
 * @File         : 电商优惠券
 * @Interface    : 接口文档说明
 * @Product      : 需求文档说明
 * @Date         : 2022-05-23 14:33:25
 * @LastEditors  : lubiao
 * @LastEditTime : 2022-07-26 17:09:54
*/
import React, { useEffect, useState } from 'react';
import { PageStore } from '@/pages/liveshow/service/register';
import { getCouponDetail } from '../../../service/api';
import boxjsUtils from '../../../utils/boxjsUtils';
import { openReflowByUrl } from '@/pages/liveshow/utils/reflow';

import './index.less';

const Coupon = props => {
    const { changeCoupon, multiple} = props;
    const [isReady, setIsReady] = useState(false);
    const [couponNum, setCounponNum] = useState();
    const [couponTitle, setCouponTitle] = useState();
    const [couponDesc, setCouponDesc] = useState();
    const [couponTime, setCouponTime] = useState();
    useEffect(() => {
        getData();
    }, []);

    const getData = async () => {
        try {
            const params = {
                room_id: PageStore.roomId
            }
            const res = await getCouponDetail(params);
            if (res && +res.errno === 0) {
                const info = res.data.coupon || {};
                const {
                    coupon_type_desc,
                    coupon_limit_desc,
                    coupon_start_time,
                    coupon_end_time,
                    coupon_full_price,
                    coupon_sale_price,
                    coupon_use_time_desc
                } = info;
                setCounponNum(coupon_sale_price);
                setCouponTitle(coupon_type_desc);
                setCouponDesc(coupon_limit_desc);
                setCouponTime(coupon_use_time_desc);
                // 判断优惠券是否为空值
                info.coupon_id ? changeCoupon(2) : changeCoupon(1);
                info.coupon_id && setIsReady(true);
            } else {
                changeCoupon(1);
            }
        } catch (e) {
            changeCoupon(1);
        }
    };

    // 点击回流
    const dealClick = () => {
        if (!boxjsUtils.platformInfo.isBox && !boxjsUtils.platformInfo.isLiteBox) {
            openReflowByUrl();
            return;
        }
    };

    return (
        isReady ?
            <div className='coupon-content' style={{transform: [`scale(${multiple})`]}}>
                <i className='icon'>直播间专享</i>
                <div className='price'>
                    <span className='unit'>￥</span>
                    <span className='number'>{couponNum}</span>
                </div>
                <div className='info'>
                    <span className='title'>{couponTitle}</span>
                    <span className='desc'>{couponDesc}</span>
                    <span className='time'>{couponTime}</span>
                </div>
                <div className='btn' onClick={dealClick}>
                    预约可领
                </div>
            </div>
            : null
    )
};

export default Coupon;
