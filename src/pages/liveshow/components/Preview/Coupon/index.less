@import '~@baidu/nano-theme/index.less';
.theme-root() !important;

.coupon-content {
    position: relative;
    margin: 0 auto .18rem auto;
    width: 3.8rem;
    height: .8917rem;
    background: #FFF1F0;
    background: url('./assets/coupon_bg.png') no-repeat center;
    background-size: 100% 100%;
    border-radius: .09rem;
    transform-origin: 0 0;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    color: #FF3355;

    .icon {
        position: absolute;
        top: 0;
        left: 0;
        width: .83rem;
        height: .2267rem;
        background-color: rgba(255, 51, 85, .16);
        border-radius: .09rem 0 .12rem 0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC-Medium;
        font-size: .12rem;
        line-height: .12rem;
        font-weight: 500;
        font-style: normal;
    }

    .price {
        position: relative;
        width: .97rem;
        display: flex;
        align-items: flex-end;
        justify-content: center;

        .unit {
            margin-bottom: .0233rem;
            height: .16rem;
            font-family: PingFangSC-Medium;
            font-size: .16rem;
            line-height: .16rem;
            font-weight: 500;
        }

        .number {
            height: .33rem;
            font-family: PingFangSC-Semibold;
            font-size: .33rem;
            line-height: .33rem;
            font-weight: 600;
        }
    }

    .info {
        position: relative;
        width: 1.7867rem;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;

        .title {
            font-family: PingFangSC-Medium;
            font-size: .16rem;
            letter-spacing: 0;
            line-height: .16rem;
            font-weight: 500;
        }

        .desc,
        .time {
            margin-top: .06rem;
            font-family: PingFangSC-Regular;
            font-size: .12rem;
            letter-spacing: 0;
            line-height: .12rem;
            font-weight: 400;
        }

    }

    .btn {
        position: relative;
        width: 1.04rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC-Semibold;
        font-size: .14rem;
        font-weight: 600;
        line-height: .14rem;
    }
}