@import '~@baidu/nano-theme/index.less';

.dialogContainer {
    position: fixed;
    left: .42rem;
    right: .42rem;
    top: 50%;
    .theme-background(BC81);

    transform: translateY(-50%);
    z-index: 1001;
    border-radius: .13rem;
    padding-top: .24rem;
    box-sizing: border-box;

    .dialogTitle {
        font-size: .2rem;
        text-align: center;
        line-height: 1.4;
    }

    .dialogBgContainer {
        width: 85.5%;
        margin: 0 auto .75rem;

        .dialogBg {
            background-image: url(https://b.bdstatic.com/searchbox/icms/searchbox/img/dialog-bg-light.png);
            background-size: cover;
            font-size: 0;

            .darkmode & {
                background-image: url(https://b.bdstatic.com/searchbox/icms/searchbox/img/dialog-bg-dark.png);
            }

            img {
                width: 100%;
                height: auto;
                opacity: 0;
                pointer-events: none;
            }
        }
    }

    .dialogBtn {
        position: absolute;
        width: 100%;
        height: .48rem;
        left: 0;
        right: 0;
        bottom: 0;
        line-height: .48rem;
        text-align: center;
        font-size: .18rem;
        border-bottom-left-radius: .13rem;
        border-bottom-right-radius: .13rem;
        .theme-color(GC1);
        .theme-background(NC30);

        &::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 1px;
            left: 0;
            top: 0;
            .theme-background(NC31);

            transform: scaleY(.333);
            transform-origin: 0 0;
        }

        &:active {
            opacity: .2;
        }
    }
}

.dialogMask {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    z-index: 1000;
    .theme-background(SC51);
}
