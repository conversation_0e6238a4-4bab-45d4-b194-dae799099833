/**
 * @file 预约模块弹窗
 * <AUTHOR>
 * @date 2021-11-15 22:07:19
 */
import React from 'react';
import {logDialogClick} from '@/pages/liveshow/utils/thunder';
import style from './index.less';

export const DIALOG_BG_LIGHT = 'https://b.bdstatic.com/searchbox/icms/searchbox/img/dialog-bg-light.png';

export default function BooksDialog({setShowDialog}) {
    const clickCloseBtn = () => {
        setShowDialog && setShowDialog(false);
        logDialogClick();
    };
    return (
        <React.Fragment>
            <div className={style.dialogContainer}>
                <div className={style.dialogTitle}>
                    在我的-书架
                    <br />
                    查看该内容
                </div>
                <div className={style.dialogBgContainer}>
                    <div className={style.dialogBg}>
                        <img src={DIALOG_BG_LIGHT} />
                    </div>
                </div>
                <div className={style.dialogBtn} onClick={clickCloseBtn}>
                    我知道了
                </div>
            </div>
            <div className={style.dialogMask} />
        </React.Fragment>
    );
}
