/**
 * @file 倒计时组件
 * <AUTHOR> ji<PERSON><PERSON><PERSON><PERSON>@baidu.com on 2020-06-15 20:59:32.
 */

import React, {useCallback, useEffect, useRef, useState} from 'react';
import {useUpdateEffect} from 'ahooks';
import {getEndStatus} from '@/pages/liveshow/service/api';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import {registerPageHidden} from '@/utils/index';
import {openReflowByUrl} from '@/pages/liveshow/utils/reflow';
import {getAppName} from '@/pages/liveshow/utils/getUrlParam';
import MultiChat, {MessageType} from '@/pages/liveshow/utils/multiChat';
import style from './index.less';

const minuteSecond = 60;
const hourSecond = minuteSecond * 60;
const daySecond = hourSecond * 24;
const isBox = boxjsUtils.platformInfo.isBox;
const isLiteBox = boxjsUtils.platformInfo.isLiteBox;
const appName = getAppName();

function CountDownTimeStr({timestamp = 0}) {
    if (timestamp === null) {
        return '---';
    }
    if (timestamp < 0) {
        timestamp = 0;
    }
    const timeArray = [
        {
            unit: '天',
            secondCount: daySecond
        },
        {
            unit: '时',
            secondCount: hourSecond
        },
        {
            unit: '分',
            secondCount: minuteSecond
        },
        {
            unit: '秒',
            secondCount: 1
        }
    ];
    let time = Math.ceil(timestamp);
    let resultArray = timeArray.map((timeItem) => {
        const count = Math.floor(time / timeItem.secondCount);
        time -= count * timeItem.secondCount;
        return {
            count: count.toString().length < 2 ? `0${count}` : count,
            ...timeItem
        };
    });
    return resultArray.reduce((pre, result, index) => {
        if (pre === '') {
            if (result.count > 0 || result.unit === '秒') {
                return [
                    <span className={style.countDownTime} key={`${index}-1`}>
                        {result.count}
                    </span>,
                    <span className={style.countDownUnit} key={`${index}-2`}>
                        {result.unit}
                    </span>
                ];
            }
            return pre;
        }
        return pre.concat([
            [
                <span className={style.countDownTime} key={`${index}-1`}>
                    {result.count}
                </span>,
                <span className={style.countDownUnit} key={`${index}-2`}>
                    {result.unit}
                </span>
            ]
        ]);
    }, '');
}

function CountDown(props) {
    const [countdown, setCountdown] = useState(() => {
        return +props.countdown < 0 ? 0 : +props.countdown;
    });
    const [noStart, setNoStart] = useState(false);
    const timeOut = useRef(null);
    const pageOpenTime = new Date().getTime();
    const chat = useRef(new MultiChat());
    const chatReliable = useRef(new MultiChat());
    const [chatLock, setChatLock] = useState(false);
    const liveUpdateLock = useRef(false);

    const update = useCallback(() => {
        return getEndStatus().then((data) => {
            // -1 表示直播还未开始,继续倒计时
            const status = +data.status;
            // 提前开播手动设置5s轮询；延迟开播，按照接口返回时间设置倒计时
            const count = liveUpdateLock.current === true ? 5 : +data.countdown < 0 ? 0 : +data.countdown;
            const show = (status === -1 && count <= 0) || status === 0;
            props.setShowLiveBtn && props.setShowLiveBtn(!show);
            setNoStart(show);

            if (status === -1) {
                setCountdown(count);
            }
            else {
                // 主播开始,刷新数据前往直播间
                props.refresh && props.refresh();
            }
        });
    }, [props]);

    useEffect(() => {
        update();
    }, []);

    useEffect(() => {
        registerPageHidden(
            () => {},
            () => {
                const leaveTime = Math.floor((new Date().getTime() - pageOpenTime) / 1000);
                const resetCountDown = +props.countdown - leaveTime;
                setCountdown(resetCountDown > 0 ? resetCountDown : 0);
            }
        );
    }, []);

    useEffect(() => {
        if (countdown <= 0) {
            // 倒计时结束逻辑
            update();
        }
        else {
            timeOut.current = setTimeout(() => {
                setCountdown(countdown - 1);
            }, 1000);
        }
        return () => {
            clearTimeout(timeOut.current);
        };
    }, [countdown]);

    useEffect(() => {
        // 倒计时大于30分钟，或已建立消息通道，不再建立IM消息通道
        if (countdown >= 60 * 30 + 5 || chatLock === true) {
            return;
        }
        setChatLock(true);
    }, [countdown]);

    useUpdateEffect(() => {
        // 建立IM消息必达通道
        chatReliable.current.start({
            status: props.status,
            chatID: props.reliableMcastId
        });

        // 建立IM消息普通通道，延迟3秒建立
        setTimeout(() => {
            chat.current.start({
                status: props.status,
                chatID: props.chatMcastId
            });
        }, 3000);

        const min = 3;
        const max = 10;

        const handleLiveStart = () => {

            // 随机延迟3-10秒触发直播开始事件，打散用户触发时机
            const randomTime = Math.floor(Math.random() * (max - min + 1)) + min;

            setTimeout(() => {
                liveUpdateLock.current = true;
                update();
            }, randomTime * 1000);
        };
        // 监听im消息
        chatReliable.current.on(MessageType.LIVE_START, handleLiveStart);
        chat.current.on(MessageType.LIVE_START, handleLiveStart);

        return () => {
            chatReliable.current.off(MessageType.LIVE_START, handleLiveStart);
            chat.current.off(MessageType.LIVE_START, handleLiveStart);
        };
    }, [chatLock]);

    const hasCount = countdown > 0 && !noStart;

    return (
        <React.Fragment>
            <div className={style.roomInfo}>
                <div className={style.subInfo}>
                    <div className={style.infoTitle}>{hasCount ? '距离开播还有' : '直播即将开始'}</div>
                    <div className={style.infoContent}>{hasCount && <CountDownTimeStr timestamp={countdown} />}</div>
                </div>
            </div>
            {noStart && (
                <div className={style.forbiddenSubcribe}>
                    <span className={style.forbiddenCoffee} />
                    <div className={style.preparing}>主播正在赶来，请稍候...</div>
                    {(isBox || isLiteBox)
                        ? <div className={style.forbiddenBtn}>敬请期待</div>
                        : (
                            <div className='subscribeSubBtn' onClick={openReflowByUrl}>
                                打开
                                {appName}
                                ，观看精彩直播
                            </div>
                        )}
                </div>
            )}
        </React.Fragment>
    );
}
export default CountDown;
