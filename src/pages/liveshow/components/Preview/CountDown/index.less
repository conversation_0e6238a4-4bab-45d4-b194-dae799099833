@import '~@baidu/nano-theme/index.less';
.theme-root() !important;

.infoContent {
    display: flex;
    flex-wrap: nowrap;
    flex-direction: row;
    min-height: .44rem;

    span {
        height: .44rem;
        text-align: center;
        line-height: .44rem;
        border-radius: .05rem;
    }

    .countDownTime {
        width: .44rem;
        font-size: .28rem;
        font-weight: 500;
        .theme-color(GC68);
        .theme-background(GC86);
    }

    .countDownUnit {
        padding: 0 .08rem;
        font-size: .14rem;
        font-weight: 700;
        .theme-color(GC1);
    }
}

.haokanMatrix {
    .infoContent {
        .countDownTime {
            color: #ff4141;
        }
    }
}

.youjiaMatrix {
    .infoContent {
        .countDownTime {
            color: #00cecf;
        }
    }
}

.baidudictMatrix {
    .infoContent {
        .countDownTime {
            color: #00cb8a;
        }
    }
}

.fortunecatMatrix {
    .infoContent {
        .countDownTime {
            color: #c9a687;
        }
    }
}

.hiphopMatrix {
    .infoContent {
        .countDownTime {
            color: #5dff6c;
        }
    }
}

.roomInfo {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: .3633rem 0 .21rem 0;
}

.roomInfo .subInfo {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.infoTitle {
    font-size: .14rem;
    .theme-color(GC1);

    position: relative;
    margin-bottom: .21rem;

    &::before,
    &::after {
        position: absolute;
        content: ' ';
        width: .57rem;
        top: .09rem;
        height: 1px;
        .theme-background(GC37);
    }

    &::before {
        left: -.08rem;
        transform: translateX(-100%) scaleY(.33);
    }

    &::after {
        transform: translateX(100%) scaleY(.33);
        right: -.08rem;
    }
}

.forbiddenSubcribe {
    margin-top: -.74rem;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    justify-content: center;
    font-weight: 700;

    .forbiddenCoffee {
        width: .42rem;
        height: .42rem;
        margin: 0 auto;
        background: url(https://b.bdstatic.com/searchbox/icms/searchbox/img/coffee_light.png) 0 0 no-repeat;
        background-size: contain;

        .darkmode & {
            background: url(https://b.bdstatic.com/searchbox/icms/searchbox/img/coffee_dark.png) 0 0 no-repeat;
            background-size: contain;
        }
    }

    .preparing {
        .theme-color(GC4);

        font-size: .14rem;
        line-height: .14rem;
        margin: .05rem auto .13rem;
    }

    .forbiddenBtn {
        width: 100%;
        height: .4rem;
        .theme-color(GC67);

        text-align: center;
        line-height: .4rem;
        border-radius: .2rem;
        .theme-background(GC51);

        opacity: .3;
    }
}
