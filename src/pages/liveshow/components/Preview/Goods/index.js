/**
 * <AUTHOR> lubiao
 * @File         : 用途
 * @Interface    : 接口文档说明
 * @Product      : 需求文档说明
 * @Date         : 2022-05-23 15:39:07
 * @LastEditors  : lubiao
 * @LastEditTime : 2022-07-26 17:06:58
*/
import React, { useEffect, useState } from 'react';
import OneItem from './One';
import GoodsList from './List';
import { getCoodsList } from '../../../service/api';
import './index.less';
import { PageStore } from '../../../service/register';
import boxjsUtils from '../../../utils/boxjsUtils';
import { openReflowByUrl } from '@/pages/liveshow/utils/reflow';

const Goods = props => {
    const { couponStatus, multiple } = props;
    const [isReady, setIsReady] = useState(false);
    const [goodsNum, setGoodsNum] = useState();
    const [list, setList] = useState([])

    useEffect(() => {
        getData();
    }, []);

    const getData = async () => {
        const params = {
            room_id: PageStore.roomId
        }
        const res = await getCoodsList(params);
        if (res && +res.errno === 0) {
            const data = res.data || {};
            const list = data.good_list || [];
            setList(list);
            setGoodsNum(list.length)
            setIsReady(list.length > 0);
        }
    };

    // 点击回流
    const dealClick = () => {
        if (!boxjsUtils.platformInfo.isBox && !boxjsUtils.platformInfo.isLiteBox) {
            openReflowByUrl();
            return;
        }
    }

    return (
        isReady && couponStatus === 1 ?
            <div className={`goods-content ${goodsNum > 1 ? 'more' : 'one'}`}
                style={{transform: [`scale(${multiple})`]}}
                onClick={dealClick}>
                <div className='goods-title'>
                    直播好物等你来选
                </div>
                {
                    goodsNum > 1 ? <GoodsList data={list} /> : <OneItem data={list[0]} />
                }
            </div>
            : null
    )
};

export default Goods;
