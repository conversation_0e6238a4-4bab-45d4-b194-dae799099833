/**
 * <AUTHOR> lubiao
 * @File         : 用途
 * @Interface    : 接口文档说明
 * @Product      : 需求文档说明
 * @Date         : 2022-05-23 15:58:09
 * @LastEditors  : lubiao
 * @LastEditTime : 2022-05-26 10:43:38
*/
import React from 'react';
import './index.less';

const OneItem = props => {
    const {title, image, price} = props.data;
    return (
        <div className='one-item'>
            <div className='pic'>
                <img src={image} />
            </div>
            <div className='desc'>
                <span className='title'>{title}</span>
                <span className='price'>{price}</span>
            </div>
        </div>
    )
};

export default OneItem;