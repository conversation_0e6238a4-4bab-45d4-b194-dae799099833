@import '~@baidu/nano-theme/index.less';
.theme-root() !important;

.one-item {
    position: relative;
    width: 100%;
    margin-top: .22rem;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .pic {
        width: .89rem;
        height: .89rem;
        border-radius: .09rem;
        overflow: hidden;

        img {
            position: relative;
            width: 100%;
        }
    }

    .desc {
        height: .89rem;
        margin-left: .08rem;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: space-between;

        .title {
            width: 2.78rem;
            height: .42rem;
            font-family: PingFangSC-Regular;
            font-size: .18rem;
            color: #1F1F1F;
            line-height: .21rem;
            font-weight: 400;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
        }

        .price {
            font-family: PingFangSC-Semibold;
            font-size: .16rem;
            color: #FF6600;
            letter-spacing: 0;
            line-height: .16rem;
            font-weight: 600;
        }
    }
}