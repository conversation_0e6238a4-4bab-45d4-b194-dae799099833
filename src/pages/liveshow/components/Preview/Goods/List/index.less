@import '~@baidu/nano-theme/index.less';
.theme-root() !important;

.goods-list {
    position: relative;
    width: 100%;
    margin-top: .22rem;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    overflow-y: hidden;
    overflow-x: auto;
    &::-webkit-scrollbar {
        width: 0;
    }

    .goods-item {
        flex: none;
        position: relative;
        width: 1.06rem;
        margin-right: .07rem;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;

        .pic {
            width: 1.06rem;
            height: 1.06rem;
            border-radius: .09rem;
            overflow: hidden;

            img {
                position: relative;
                width: 100%;
            }
        }
        

        .title {
            position: relative;
            margin-top: .025rem;
            width: 1.06rem;
            height: .46rem;
            font-family: PingFangSC-Regular;
            font-size: .14rem;
            color: #1F1F1F;
            line-height: .23rem;
            font-weight: 400;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
        }

        .price {
            position: relative;
            margin-top: .055rem;
            font-family: PingFangSC-Regular;
            font-size: .14rem;
            color: #FF6600;
            letter-spacing: 0;
            line-height: .14rem;
            font-weight: 400;
        }
    }
    
    .finall-tag {
        position: relative;
        margin: 0 .17rem 0 .1rem;
        width: .15rem;
        font-family: PingFangSC-Regular;
        font-size: .14rem;
        color: #1F1F1F;
        letter-spacing: 0;
        line-height: .17rem;
        font-weight: 400;
    }
}