/**
 * <AUTHOR> lubiao
 * @File         : 用途
 * @Interface    : 接口文档说明
 * @Product      : 需求文档说明
 * @Date         : 2022-05-23 15:58:09
 * @LastEditors  : lubiao
 * @LastEditTime : 2022-05-26 10:43:43
*/
import React from 'react';
import './index.less';

const GoodsList = props => {
    const { data } = props;
    return (
        <div className='goods-list'>
            {
                data.map((item, index) => (
                    index < 7 ?
                    <div className='goods-item' key={`goods-item-${index}`}>
                        <div className='pic'>
                            <img src={item.image} />
                        </div>
                        <div className='title'>{item.title}</div>
                        <div className='price'>{item.price}</div>
                    </div>
                    : null
                ))
            }
            {
                data.length >= 7 && <div className='finall-tag'>开播可看全部</div>
            }
        </div>
    )
};

export default GoodsList;