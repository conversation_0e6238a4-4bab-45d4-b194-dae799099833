@import '~@baidu/nano-theme/index.less';

.banner {
    width: 100%;
    height: 2.3267rem;
    position: relative;
    overflow: hidden;

    .video {
        position: relative;
        width: 100%;
        height: 2.3267rem;

        video {
            position: relative;
            z-index: 1;
        }

        .cover {
            font-size: 0;
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            bottom: 0;
            right: 0;

            .stop {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 3;
                width: .48rem;
                height: .48rem;
                background: url('./img/videoStop.png') no-repeat;
                background-size: cover;
            }

            img {
                height: 100%;
            }
        }
    }

    video {
        width: 100%;
        height: 100%;
        position: relative;
        z-index: 999;
        object-fit: cover;
    }

    img {
        width: 100%;
        height: auto;
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        right: 0;
        user-select: none;
        pointer-events: none;
    }
}
