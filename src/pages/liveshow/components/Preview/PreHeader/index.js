/**
 * @file 预约页头部播放逻辑
 * <AUTHOR>
 * @date 2021-11-12 15:04:42
 */

import React, {useRef, useEffect, useCallback, useState} from 'react';

import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import {PageStore} from '@/pages/liveshow/service/register';
import style from './index.less';

const isAndroidAndBox = boxjsUtils.platformInfo.isBox && boxjsUtils.platformInfo.isAndroid;

function PreHeader(props) {
    const [state, setState] = useState({
        autoPlay: false,
        show: true,
        androidAndBox: true, // 端上 安卓
    });

    const playVideo = (isManualPlay) => {
        setState((sta) => ({
            ...sta,
            show: false,
            androidAndBox: true,
            autoPlay: true,
        }));
        const video = videoRef.current;
        if (video && video.src) {
            // 如果是手动点的播放，打开声音，忽略url上的muted参数
            if (isManualPlay) {
                video.muted = false;
            }
            video.load();
            video.play();
        }
    };

    useEffect(() => {
        // 端内且wifi环境下自动播放
        if (boxjsUtils.platformInfo.isBox) {
            boxjsUtils.getNetInfo().then((network) => {
                if (network === 'wifi') {
                    playVideo();
                }
            });
        }
    }, []);

    const videoRef = useRef(null);
    const ref = useCallback((video) => {
        if (video && video.src) {
            videoRef.current = video;
            setTimeout(() => {
                // 端上 安卓 暂停状态（即自动播放失败）隐藏video标签显示封面图，通过手动触发播放
                if (isAndroidAndBox && video.paused) {
                    setState((sta) => ({
                        ...sta,
                        autoPlay: false,
                        androidAndBox: false,
                    }));
                }
            }, 500);

            // 大部分浏览器支持静音自动播放
            setTimeout(() => {
                // 直播预览页存在视频时（https://live.baidu.com/m/media/multipage/liveshow/index.html?room_id=7337736916&source=h5pre），如果没有muted参数，则带声音播放；有的话静音播放视频
                const {muted = '0'} = PageStore.queryParams;
                if (+muted === 0) {
                    video.muted = false;
                } else {
                    video.muted = true;
                }
            }, 200);

            video.onended = (e) => {
                let obj = {show: true};
                // 解决安卓手机video层级过高
                if (isAndroidAndBox) {
                    obj.androidAndBox = false;
                }
                setState((sta) => ({
                    ...sta,
                    ...obj,
                }));
            };
        }
    }, []);

    const {cover, video_url} = props;
    const {show, autoPlay, androidAndBox} = state;
    return (
        <div className={style.banner}>
            {video_url ? (
                <div className={style.video}>
                    {androidAndBox ? (
                        <video playsInline controls muted ref={ref} autoPlay={autoPlay} src={video_url}></video>
                    ) : null}
                    <div style={{zIndex: show ? 2 : 0}} className={style.cover}>
                        <img src={cover} alt='' />
                        <div onClick={() => playVideo(true)} className={style.stop} />
                    </div>
                </div>
            ) : (
                cover && <img src={cover} alt='' />
            )}
        </div>
    );
}

export default PreHeader;
