/**
 * @file 倒计时组件
 * <AUTHOR> on 2024-02-01 20:59:32.
 */

import React, {useCallback, useEffect, useRef, useState} from 'react';
import {getEndStatus} from '@/pages/liveshow/service/api';
import cls from 'classnames';
import style from './index.less';

const minuteSecond = 60;
const hourSecond = minuteSecond * 60;
const daySecond = hourSecond * 24;

function CountDownTimeStr({timestamp = 0}) {
    if (timestamp === null) {
        return '---';
    }
    if (timestamp < 0) {
        timestamp = 0;
    }
    const timeArray = [
        {
            unit: '天',
            secondCount: daySecond
        },
        {
            unit: '时',
            secondCount: hourSecond
        },
        {
            unit: '分',
            secondCount: minuteSecond
        },
        {
            unit: '秒',
            secondCount: 1
        }
    ];
    let time = Math.ceil(timestamp);
    let resultArray = timeArray.map((timeItem) => {
        const count = Math.floor(time / timeItem.secondCount);
        time -= count * timeItem.secondCount;
        return {
            count: count.toString().length < 2 ? `0${count}` : count,
            ...timeItem
        };
    });
    return resultArray.reduce((pre, result, index) => {
        if (pre === '') {
            if (result.count > 0 || result.unit === '秒') {
                return [
                    <span className={style.countDownTimeNew} key={`${index}-1`}>
                        {result.count.toString()[0]}
                    </span>,
                    <span className={style.countDownTimeNew} key={`${index}-3`}>
                        {result.count.toString()[1]}
                    </span>,
                    <span className={style.countDownUnitNew} key={`${index}-2`}>
                        {result.unit}
                    </span>
                ];
            }
            return pre;
        }
        return pre.concat([
            [
                <span className={style.countDownTimeNew} key={`${index}-1`}>
                    {result.count.toString()[0]}
                </span>,
                <span className={style.countDownTimeNew} key={`${index}-3`}>
                    {result.count.toString()[1]}
                </span>,
                <span className={style.countDownUnitNew} key={`${index}-2`}>
                    {result.unit}
                </span>
            ]
        ]);
    }, '');
}

/**
 * CountDown函数，用于实现倒计时功能。
 *
 * @param props {object} 包含以下属性：
 * - new_visual {object} 可选，默认为{}，新视觉页面的配置对象，包含preview_page字段，其中包含countdown_img字段，表示倒计时图片地址。
 *
 * @returns {JSX.Element} React元素，包含倒计时显示和相关逻辑。
 */
function CountDown(props) {
    const {
        new_visual = {},
    } = props;
    const [countdown, setCountdown] = useState(() => {
        return +props.countdown < 0 ? 0 : +props.countdown;
    });
    const [noStart, setNoStart] = useState(false);
    const timeOut = useRef(null);
    const update = useCallback(() => {
        return getEndStatus().then((data) => {
            // -1 表示直播还未开始,继续倒计时
            const status = +data.status;
            const count = +data.countdown < 0 ? 0 : +data.countdown;
            const show = (status === -1 && count > 0);
            props.setShowLiveBtn && props.setShowLiveBtn(!show);
            setNoStart(show);
            if (status === -1) {
                setCountdown(count);
            }
            else {
                // 主播开始,刷新数据前往直播间
                props.refresh && props.refresh();
            }
        });
    }, [props]);

    useEffect(() => {
        update();
    }, []);

    useEffect(() => {
        if (countdown <= 0) {
            // 倒计时结束逻辑
            update();
        }
        else {
            timeOut.current = setTimeout(() => {
                setCountdown(countdown - 1);
            }, 1000);
        }
        return () => {
            clearTimeout(timeOut.current);
        };
    }, [countdown]);

    const hasCount = countdown > 0;

    return (
        <React.Fragment>
            {noStart ? (
                <div
                    className='count-down-new'
                    style={{
                        backgroundImage: new_visual.preview_page?.countdown_img
                            ? `url(${new_visual.preview_page.countdown_img})` : null,
                    }}
                >
                    <div className='count-text'>
                        <span
                            className='text-tip'
                        >{hasCount ? new_visual.preview_page?.countdown_title : '直播即将开始'}</span>
                    </div>
                    <div className='count-content'>{hasCount && <CountDownTimeStr timestamp={countdown} />}</div>
                </div>
            ) : null}
        </React.Fragment>
    );
}
export default CountDown;
