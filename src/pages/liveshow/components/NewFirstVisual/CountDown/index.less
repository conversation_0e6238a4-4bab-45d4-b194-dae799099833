@import '~@baidu/nano-theme/index.less';
.theme-root() !important;

.count-down-new {
    width: 3.78333rem;
    height: 1.26667rem;
    padding: 0;
    background: url('https://mpics.bdstatic.com/qm/202402/pic_f69rDW_1706781681.png') center / contain no-repeat;
    // background-size: 100% 100%;
    // background-repeat: no-repeat;
    margin: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.count-text {
    color: #FFF;
    width: 1.95667rem;
    height: .07667rem;
    background: url('https://mpics.bdstatic.com/qm/202402/pic_LNnh6G_1706780401.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    text-align: center;
    font-family: "PingFang SC";
    font-size: .16667rem;
    letter-spacing: .04167rem;
    font-style: normal;
    font-weight: 500;
    line-height: .07667rem; /* 136.167% */
    margin-bottom: .15rem;
    margin-top: .34rem;

    .text-tip {
        text-align: center;
        margin-left: .05rem;
    }
}

.count-content {
    display: flex;
    flex-wrap: nowrap;
    flex-direction: row;
    height: .32rem;
    margin-bottom: .3rem;

    .countDownTimeNew {
        width: .18667rem;
        height: .32rem;
        border-radius: .04rem;
        // background: hsl(357, 66%, 47%);
        color: #FFF;
        text-align: center;
        // font-family: "baidu number";
        font-size: .28rem;
        font-style: normal;
        font-weight: 500;
        line-height: .32rem; /* 71.429% */
        margin-left: .02rem;
    }

    .countDownUnitNew {
        color: #FFF;
        text-align: center;
        font-family: "PingFang SC";
        font-size: .11667rem;
        font-style: normal;
        font-weight: 400;
        line-height: .2rem; /* 171.429% */
        margin-top: .1rem;
        margin-left: .06rem;
        margin-right: .06rem;
    }
}
