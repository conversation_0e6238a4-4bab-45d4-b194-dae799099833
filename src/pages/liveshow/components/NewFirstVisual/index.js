/**
 * @file 新版分享页视觉visual_id 为 1
 * <AUTHOR>
 */

import React, {useEffect, useCallback, useState} from 'react';
import {PageStore} from '@/pages/liveshow/service/register';
import {matrixAppSchemeHeaderList, refolwForConfig} from '@/pages/liveshow/utils/reflow';
import {getAppSchemeHead} from '@/pages/liveshow/utils/getUrlParam';
import {TemplateEnum, logTxtMap} from '@/pages/liveshow/config/const';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import {execInvoke, getInvokeInfo, setCommonParams, createMcpWxTag} from '@baidu/mcp-sdk';
import {env} from '@/utils/env';
import {NewH5ShowLog} from '@/pages/liveshow/log';
import {initShare} from '@/utils/share';
import cls from 'classnames';
import LiveVideo from './LiveVideo';
import CountDown from './CountDown';
import Dialog from './Dialog';
import MainBtn from './MainBtn';
import ShareBtn from './ShareBtn';
import './index.less';

// mcp 场景信息
const APP_NAME_SCENE = {
    'baiduboxapp': 'sharepage',
    'lite': 'sharepagelite', // 新增scene
    'haokan': 'sharepagehaokan', // 新增scene
    '': 'sharepage'
};

// 预约页外部资源
const sids = ['tiebak', 'wangpan', 'ditu'];

// 调起位
const posKey = 'pos_part';

const source = PageStore.queryParams.source;
const appSchemeHead = getAppSchemeHead();

// 回流矩阵场景
const isMatrixApp = matrixAppSchemeHeaderList.includes(appSchemeHead);
const entranceAPP = (env.isMainBox && !isMatrixApp) || (env.isAndroid && env.isLiteBox);

const NewFirstVisual = ({data, refresh}) => {
    const {
        template,
        status,
        schema_info, // 回流NA直播间信息
        countdown,
        tiebaLiveCmd,
        screen,
        nid,
        back_flow = {},
        new_visual = {},
        share
    } = data;

    const {enter_cmd = schema_info.enter_cmd} = new_visual;

    // 是否展示回流弹窗
    const [showModel, setShowModel] = useState({
        show: false,
        type: 'reflow',
    });
    // 初始化 视频未播放之前
    const [isInitStatus, setInitStatus] = useState(true);
    const [invokeAppInfo, setInvokeAppInfo] = useState({});
    const isPreview = template === TemplateEnum.Preview; // 是否为预约直播

    // 矩阵端内（安卓 & 非预约态 & 主板或极速版）
    const showFakePoster = env.isAndroid && !isPreview && (env.isMainBox || env.isLiteBox);
    let invokeInfo = {}; // mcp调起信息

    useEffect(() => {
        // 初始化分享
        initShare({
            linkUrl: share.share_url,
            title: share.main_title,
            content: share.sub_title,
            source: 'xcw_live',
            iconUrl: share.cover,
            imageUrl: share.cover,
            mediaType: 'all',
        });

        // 仅主板 或 安卓Lite 内直接调起NA直播间
        if (entranceAPP) {
            jumpToNA();
        }
    }, []);

    useEffect(() => {
        NewH5ShowLog(4,'subscribe','15263',logTxtMap[status]);
    }, [status]);

    const jumpToNA = useCallback(() => {
        // 直播中、回放 符合条件自动跳转直播间
        if ([0, 3].indexOf(status) !== -1 && schema_info) {
            boxjsUtils.invokeSchema({schema: enter_cmd});
            return;
        }
    }, [status, schema_info]);

    const clkFakePoster = useCallback(() => {
        if (!showFakePoster) {
            return;
        }

        jumpToNA();
    }, [showFakePoster]);

    const onClickShowModel = res => {
        setShowModel({
            show: res.show,
            type: res.type,
        });
    };

    const handleInvoke = () => {
        invokeApp(posKey);
    }

    useEffect(() => {
        // 非入口矩阵内 （回流主板 或 极速版）执行mcp调起逻辑
        if (!entranceAPP) {
            getMcpData(enter_cmd);
        }
    }, []);

    // 获取MCP信息
    const getMcpData = useCallback(async schema => {
        if (!schema) {
            return;
        }

        // 设置通用调起信息
        setCommonParams({
            app: 'wise',
            scene: APP_NAME_SCENE[source] || 'sharepage',
            ext_sid: sids.indexOf(source) !== -1 ? source : '' // 贴吧、网盘、地图
        });

        // 获取调起位详细信息
        const info = await getInvokeInfo({
            'invoke_info': {
                [posKey]: [{
                    'share_scheme': schema
                }]
            }
        });
        invokeInfo = info;
        setInvokeAppInfo({...info});

        // 存在调起信息 && 安卓 && 微信 && 回流主板 新增微信开放标签校验 isLiveDomain &&
        if (!isMatrixApp && env.isAndroid && env.isWechat && info.action_rule) {
            createWxTags(info);
        }
    }, []);

    // 创建微信开放标签
    const createWxTags = invokeInfo => {
        const {action_rule: actionRule, log_id: logId} = invokeInfo;
        const liveShareInfo = actionRule[posKey][0];
        const shareContainer = document.querySelector('.main-btn'); // TODO: 弹窗微信开放标签待添加
        if (shareContainer && liveShareInfo) {
            createMcpWxTag({targetDom: shareContainer}, posKey, liveShareInfo, logId);
        }
    };

     // mcp 调起
     const invokeApp = useCallback(async (pos) => {
        const invokeConfig = {
            appName: appSchemeHead,
            toStore: true,
            scheme: new_visual.enter_cmd,
            token: new_visual.share_token
        };
        const invokeInfoForPos = invokeInfo && invokeInfo.action_rule && invokeInfo.action_rule[pos];
        if (!invokeInfoForPos || !Array.isArray(invokeInfoForPos) || !invokeInfoForPos[0]) {
            // 执行默认调起 兼容接口返回异常情况
            refolwForConfig(invokeConfig);
            return;
        }

        try {
            const res = await execInvoke(
                pos,
                invokeInfoForPos[0],
                invokeInfoForPos[0].log_id
            );
        }
        catch (e) {
            // 执行默认调起行为
            refolwForConfig(invokeConfig);
        }
    }, [invokeInfo]);

    return (
        <div className='white-screen-patrol new-first-visual'>
            <div
                className='back'
                style={{
                    backgroundImage: new_visual.preview_page?.backgrounds
                        ? `url(${new_visual.preview_page.backgrounds})` : null,
                }}
            ></div>
            <div className='content'>
                <div
                    className={cls({
                        'middle-back': true,
                        'fake-poster' : showFakePoster
                    })}
                    style={{
                        backgroundImage: new_visual.preview_page?.tv_empty_bg
                            ? `url(${new_visual.preview_page.tv_empty_bg})` : null,
                    }}
                    onClick={clkFakePoster}
                >
                    {
                        showFakePoster ? (
                            <img className="play-icon" src="https://mpics.bdstatic.com/qm/202203/pic_4Y8ocS_1646113849.png" />
                        ) : null
                    }
                </div>
                <div
                    className='top-icon'
                    style={{
                        backgroundImage: new_visual.preview_page?.merchants_icon
                            ? `url(${new_visual.preview_page.merchants_icon})` : null,
                    }}
                ></div>
                <div
                    className='title'
                    style={{
                        backgroundImage: new_visual.preview_page?.title_img
                            ? `url(${new_visual.preview_page.title_img})` : null,
                    }}
                ></div>
                <div
                    className='star-avatar'
                    style={{
                        backgroundImage: new_visual.preview_page?.head_portrait
                            ? `url(${new_visual.preview_page.head_portrait})` : null,
                    }}
                ></div>
                {
                    !showFakePoster && (
                        <LiveVideo
                            {...data}
                            className='center'
                            updatePageData={refresh}
                            isBaijiahaoTag={false}
                            onClickShowModel={onClickShowModel}
                            setInitStatus={setInitStatus}
                            isInitStatus={isInitStatus}
                            isAutoPlay={false}
                            handleInvoke={handleInvoke}
                            jumpToNA={jumpToNA}
                            isPreview={isPreview}
                        />
                    )
                }
                <div
                    className='video-screen'
                    style={{
                        backgroundImage: new_visual.preview_page?.tv_border_img
                            ? `url(${new_visual.preview_page.tv_border_img})` : null,
                    }}
                ></div>
                <CountDown {...data} status={status} countdown={countdown} refresh={refresh} />
                <div className={cls({'button-preview':+status === -1, 'button-live':+status !== -1})}>
                    {(entranceAPP || (env.isLiteBox && env.isIOS)) && <ShareBtn data={data} refresh={refresh} />}
                    {!(env.isLiteBox && env.isIOS) && <MainBtn
                        data={data}
                        handleInvoke={handleInvoke}
                        jumpToNA={jumpToNA}
                        refresh={refresh}
                    />}
                </div>
                <div className='parting-line-preview'></div>
                <div className='bottom-icon-preview'></div>
            </div>
            {/* 回流弹窗 */}
            <Dialog
                showModel={showModel}
                tiebaLiveCmd={tiebaLiveCmd}
                onClickShowModel={onClickShowModel}
                status={status}
                nid={nid}
                screen={screen}
                outerSchemeParams={data.schemeParams}
                backFlow={back_flow}
                handleInvoke={handleInvoke}
                invokeInfo={invokeAppInfo}
            />
        </div>
    );
};

export default NewFirstVisual;
