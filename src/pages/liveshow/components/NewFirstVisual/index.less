.new-first-visual {
    position: relative;
    width: 100%;
    min-height: 100%;

    .back {
        font-size: 16px;
        width: 100%;
        min-height: 100%;
        position: absolute;
        z-index: 0;
        background: url('https://mpics.bdstatic.com/qm/202402/pic_ECQKLB_1707360717.jpg');
        background-size: cover;
        background-repeat: no-repeat;
        pointer-events: none;
    }

    .content {
        width: 100%;
        min-height: 100%;
        position: relative;
        padding-top: .44rem;
        overflow: hidden;
        box-sizing: border-box;
    }

    .middle-back {
        width: 3.46333rem;
        height: 1.9rem;
        border-radius: .06667rem;
        position: absolute;
        background: url('https://mpics.bdstatic.com/qm/202402/pic_n1OSIF_1707361364.png') center / cover no-repeat;
        // background-size: cover;
        // background-repeat: no-repeat;
        z-index: 2;
        margin-top: 2.45rem;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        justify-content: center;
        align-items: center;

        &.fake-poster {
            background-image: url('https://mpics.bdstatic.com/qm/202402/pic_4ad27Q_1707370205.png');
        }
    }

    .top-icon {
        width: .64667rem;
        height: .29667rem;
        background: url('https://mpics.bdstatic.com/qm/202402/pic_fMXW5I_1706866124.png');
        background-size: cover;
        background-repeat: no-repeat;
        margin: auto;
    }

    .title {
        width: 3.03667rem;
        height: .8rem;
        margin: auto;
        margin-top: .07rem;
        position: relative;
        z-index: 100;
        background: url('https://mpics.bdstatic.com/qm/202402/pic_D0j99k_1706781117.png') center / contain no-repeat;
    }

    .star-avatar {
        width: 4.05333rem;
        height: 2.88333rem;
        z-index: 1;
        position: absolute;
        top: .85rem;
        left: 50%;
        transform: translateX(-50%);
        background: url('https://mpics.bdstatic.com/qm/202402/pic_qK0Orh_1707288285.png') center / contain no-repeat;
    }

    .play-icon {
        width: 50px;
        height: 50px;
        // transform: translate(-50%,-50%) translateZ(0);
    }

    .video-screen {
        width: 3.76rem;
        height: 2.91667rem;
        margin: auto;
        position: relative;
        margin-top: 1.04rem;
        z-index: 1;
        background: url('https://mpics.bdstatic.com/qm/202402/pic_g8vdh9_1707360528.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        pointer-events: none;
    }

    .button-preview {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: nowrap;
        margin-top: .15rem;
        overflow: hidden;
    }

    .button-live {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: nowrap;
        margin-top: .32rem;
        margin-bottom: .5rem;
        overflow: hidden;
    }

    .parting-line-preview {
        width: 3.79667rem;
        height: .04667rem;
        margin: auto;
        margin-top: .21rem;
        background: url('https://mpics.bdstatic.com/qm/202402/pic_zKjEHY_1706866757.png');
        background-size: cover;
        background-repeat: no-repeat;
    }

    .bottom-icon-preview {
        width: 1.31667rem;
        height: .44rem;
        background: url('https://mpics.bdstatic.com/qm/202402/pic_xwC3Zs_1706866078.png');
        background-size: cover;
        background-repeat: no-repeat;
        margin: auto;
        margin-top: .09rem;
        margin-bottom: .4rem;
    }
}
