/**
 * @file 直播落地页弹窗组件（新版）
 * <AUTHOR>
 * @date 2022-04-19 14:00:18
 */

import React, { useEffect } from 'react';
import { Button, Dialog } from '@baidu/nano-react';
import { getAppName, getAppSchemeHead } from '@/pages/liveshow/utils/getUrlParam';
import { reflow, matrixAppSchemeHeaderList} from '@/pages/liveshow/utils/reflow';
import { PageStore } from '@/pages/liveshow/service/register';
import { commonShowLog, commonClickLog } from '@/pages/liveshow/log';
import { env } from '@/utils/env';
import {isAndroid} from '@baidu/xbox-native/detect';
import {createMcpWxTag} from '@baidu/mcp-sdk';
import './index.less';

const appName = getAppName();
let textMap = {
    reflow: {
        align: 'left',
        title: '直播邀请',
        content: `该直播使用最新版本${appName}APP观看体验更好，我在${appName}APP等你，快来看我的直播吧。`,
    },
    pay: {
        align: 'center',
        title: '购买付费直播',
        content: `该直播为付费内容\n请打开最新版${appName}APP进行购买`,
    },
};
// 回流矩阵场景
const schemeHead = getAppSchemeHead();
const isMatrixApp = matrixAppSchemeHeaderList.includes(schemeHead);

function LiveDialog(props) {
    const {
        showModel,
        onClickShowModel,
        isPaid,
        tiebaLiveCmd,
        status,
        nid,
        screen,
        outerSchemeParams = {},
        backFlow = {},
        handleInvoke,
        invokeInfo
    } = props;
    console.log(props);
    const { show, type } = showModel;

    if (!show) {
        return '';
    }
    // 安卓微信场景新增relative属性
    const isShowWeTag = !isMatrixApp && isAndroid() && env.browserType === 'wechat';
    const reflowScheme = reflow({
        roomId: PageStore.roomId,
        tiebaCmd: tiebaLiveCmd,
        isPaid: isPaid,
        outerSchemeParams,
        status,
        queryParams: {
            pop_name: type === 'pay' ? 'pay_live_payment_plugin' : ''
        }
     }, false, true);

    useEffect(() => {
        commonShowLog(status, 'windows_btn', nid, screen, '15263', true);
    }, []);

    const onClose = () => {
        onClickShowModel({
            type: type,
            show: false,
            value: '2',
        });
    };

    const info = textMap[type];

    // 如果云控有下发分享信息，优先取云控下发数据
    if (backFlow) {
        info.title = backFlow.title || info.title;
        info.content = backFlow.content || info.content;
    }

    const onClick = e => {
        if (e) {
            e.preventDefault();
            e.stopPropagation();
            commonClickLog(status, 'windows_btn', nid, screen, '15263', true);
        }

        onClickShowModel({
            type: type,
            show: false,
            value: '1',
        });

        let obj = {
            roomId: PageStore.roomId,
            tiebaCmd: tiebaLiveCmd,
            isPaid: isPaid,
            outerSchemeParams,
            status
        };
        if (type === 'pay') {
            obj.queryParams = {
                pop_name: 'pay_live_payment_plugin',
            };
        }

        if (!isMatrixApp) {
            handleInvoke('part');
        }
        else {
            reflow(obj);
        }
    };

    useEffect(() => {
        console.log(invokeInfo);
        if (env.isAndroid && env.isWechat && !isMatrixApp && invokeInfo && invokeInfo.action_rule) {
            const {action_rule, log_id} = invokeInfo;
            console.log(5555);
            // 创建微信开放标签
            const btnDom = document.querySelector('.dialog-footer');
            btnDom && createMcpWxTag({targetDom: btnDom}, 'pos_part', action_rule.pos_part[0], log_id);
        }
    }, [invokeInfo]);

    return (
        <Dialog
            className="dialog-wrapper"
            title={info.title}
            closeType='inside'
            showCancel
            showClose
            onClose={onClose}
        >
            <div
                className="dialog-body"
                style={{ textAlign: info.align }}
            >
                {info.content}
            </div>
            <div className="dialog-footer"
                style={{position: `${isShowWeTag ? 'relative' : 'none'}`}}
            >
                <Button
                    className="dialog-btn"
                    type='primary'
                    onClick={onClick}
                >
                    打开{appName}APP
                </Button>
            </div>
        </Dialog>
    );
}

export default LiveDialog;
