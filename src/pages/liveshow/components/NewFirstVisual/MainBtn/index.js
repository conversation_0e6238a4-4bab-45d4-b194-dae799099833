/**
 * @file  MainBtn 主按钮
 * <AUTHOR>
 */

import React, {useCallback, useEffect, useState} from 'react';
import {PageStore} from '@/pages/liveshow/service/register';
import {TemplateEnum, LiveStatus} from '@/pages/liveshow/config/const';
import {subscribe} from '@/pages/liveshow/service/api';
import {matrixAppSchemeHeaderList} from '@/pages/liveshow/utils/reflow';
import {getAppSchemeHead} from '@/pages/liveshow/utils/getUrlParam';
import {NewH5ClickLog} from '@/pages/liveshow/log';
import {execInvoke, getInvokeInfo, setCommonParams, createMcpWxTag, isNotInvoke, getExitInfo} from '@baidu/mcp-sdk';
import boxjsUtils from '@/pages/liveshow/utils/boxjsUtils';
import {env} from '@/utils/env';
import {logTxtMap} from '@/pages/liveshow/config/const';
import './index.less';

// 按钮文案
const btnTxtMap = {
    '0': '立即观看',
    '2': '直播结束',
    '3': '精彩回顾'
};

const entranceAPP = (env.isMainBox) || (env.isAndroid && env.isLiteBox);

const source = PageStore.queryParams.source;

const appSchemeHead = getAppSchemeHead();
// 回流矩阵场景
const isMatrixApp = matrixAppSchemeHeaderList.includes(appSchemeHead);

const MainBtn = ({data, refresh, handleInvoke, jumpToNA}) => {
    const {
        template,
        status,
        schema_info, // 回流NA直播间信息
        user_info, // 用户信息
        appointment_status = 0,
        new_visual = {},
    } = data;

    const isPreview = template === TemplateEnum.Preview; // 是否为预约直播

    const isLogin = user_info.is_login; // 是否登录

    const [btnTxt, setBtnTxt] = useState(''); // 按钮文案

    const isInvokeMain = ['', 'baiduboxapp'].indexOf(source) !== -1 || source !== 'lite'; // 是否调起主板 TODO: 需修正

    useEffect(() => {
        let btnTxt = '';
        if (isPreview) {
            btnTxt = +appointment_status === 1 ? '已预约' : '立即预约';
        }
        else {
            btnTxt = btnTxtMap[status] || '去看回放';
        }
        setBtnTxt(btnTxt);
    }, [appointment_status, template, status]);

    const handleSubEnd = (success, requestStatus) => {
        const sucToast = requestStatus ? '预约成功' : '取消预约成功';
        const failToast = requestStatus ? '预约失败' : '取消预约失败';
        boxjsUtils.showToast(success ? sucToast : failToast);

        // 更新按钮文案
        success && setBtnTxt(requestStatus ? '已预约' : '立即预约');

        // 通知开启系统通知的弹窗
        if (requestStatus === 1) {
            setTimeout(() => {
                boxjsUtils.systemNotify('live_sub');
            }, 500);
        }

        if (success) {
            refresh && refresh();
        }
    };

    const handleClk = useCallback(() => {
        // 矩阵内 预约态
        NewH5ClickLog(4,'subscribe_btn','15263',logTxtMap[status]);
        if (entranceAPP) {
            if (isPreview) {
                if (isLogin) {
                    const requestStatus = +appointment_status === 0 ? 1 : 0;
                    subscribe(requestStatus)
                        .then((success) => handleSubEnd(success, requestStatus))
                        .catch(() => handleSubEnd(false, requestStatus));
                } else {
                    // 没有登录去登录,登录成功后刷新数据
                    boxjsUtils.login().then(() => {
                        // 一定延时后调用(解决安卓11.20这个逻辑运行不稳定的问题)
                        window.setTimeout(() => {
                            refresh();
                        }, 500);
                    });
                }
            }
            else {
                jumpToNA();
            }
        }
        else {
            // 其他场景调起
            handleInvoke();
        }
    }, [appointment_status, isLogin, isPreview]);

    return (
        <div
            className='main-btn'
            onClick={handleClk}
            style={{
                color: new_visual.preview_page?.subscribe_btn_color,
                backgroundImage: new_visual.preview_page?.subscribe_btn_img
                    ? `url(${new_visual.preview_page.subscribe_btn_img})` : null,
            }}
        >
            {btnTxt}
        </div>
    );
};

export default MainBtn;
