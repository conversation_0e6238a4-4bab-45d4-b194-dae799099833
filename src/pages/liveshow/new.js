import React, {useEffect, useState} from 'react';
import {useRequest} from 'ahooks';
import {registerTemplateInit} from '@/pages/liveshow/service/register';
import {handleSSRInfo, updatePageData} from '@/pages/liveshow/service/model';
import ErrorPage from '@/pages/liveshow/components/common/ErrorStatus';
import AdLiveShow from '@/pages/liveshow/components/AdLiveShow';
import Loading from '@/pages/liveshow/components/Loading';
import vSpeed from '@baidu/mvideo-vspeed';
import '@/pages/liveshow/utils/errorMonitor';
import './assets/index.less';
import {ABTestProvider} from '@/abtest-sdk';
import {initMonitor as initAdMonitor} from './components/AdLiveShow/monitor';
import {abTestConfig} from './components/AdLiveShow/monitor/abtest';
import Preview from './components/Preview';

/**
 * 根据SSR情况返回初始值
 * @param {Object} staticContext SSR数据
 * @return {Object} 初始值对象
*/
const getSSRData = staticContext => {
    const res = staticContext?.mainRes || window?.staticContext?.mainRes;
    const mainInfoData = {
        ssrStatus: 0,
        ssrMainData: {}
    };

    // 仅预约态 && visual_id为6 通用预约活动页返回静态数据
    if (+res?.errno === 0
        && +res.data?.[371]?.status === -1 && +res.data[371].visual_id === 6
    ) {
        const resInfo = handleSSRInfo(res.data);
        // 接口返回异常 ||（直播预约态且系统时间大于开播时间）
        if (resInfo?.errCode
            || (+resInfo.status === -1 && Math.ceil(Date.now() / 1000) >= resInfo?.start_time)
        ) {
            return mainInfoData;
        }
        mainInfoData.ssrStatus = 0;
        mainInfoData.ssrMainData = resInfo;
    }

    return mainInfoData;
};

// SSR 数据处理 在服务端
const ssrData = getSSRData(window.staticContext || {});
export default function OldApp() {
    const {ssrStatus, ssrMainData} = ssrData;
    const [info, setInfo] = useState({
        ...ssrMainData
    });

    // 页面error状态
    const [pageError, setPageError] = useState(ssrStatus);

    const {run, error} = useRequest(updatePageData, {
        onSuccess: (data) => {
            setInfo(data);
        }
    });

    useEffect(() => {
        if (+window?.staticContext?.mainRes?.errno === 0) {
            vSpeed.send({
                'fmp-sync': window.performance.now()
            });
        }
    }, []);

    // 动态更新页面error态
    useEffect(() => {
        setPageError(error);
    }, [error]);

    useEffect(() => {
        const registerData = async () => {
            registerTemplateInit(info);
        };
        registerData();
    }, [info.template]);

    useEffect(() => {
        // 商业H5页面 则初始化统计脚本，防止产生在脏数据/实验不准确
        initAdMonitor();
    }, []);

    if (pageError) {
        return <ErrorPage error={error} />;
    }
    if (info.template === 'preview') {
        return <Preview data={info} refresh={run} />;
    }
    if (info.template) {
        return (
            <ABTestProvider abTestConfig={abTestConfig}>
                <AdLiveShow data={info} refresh={run} />
            </ABTestProvider>
        );
    }

    return <Loading />;
}
