import {getCuid, isDependencyLoaded, requestRoomEnterInfoWithAntiCheats} from './utils';
import {getPrefetchDatasource, setPrefetchAvailable} from './window';

export function fetchAPIRequst() {
    // 如果 CDN 加载失败，只标记预请求不可用，不影响正常业务
    if (!isDependencyLoaded()) {
        return;
    }

    const prefetchDatasource = getPrefetchDatasource();
    setPrefetchAvailable(true);

    const params = new URLSearchParams(window.location.search);
    const roomId = params.get('room_id');

    function prefetchAPIController(name) {
        prefetchDatasource[name] = apiRequestRoomEnterInfoWithAntiCheats({uid: getCuid(), roomId});
    }

    async function apiRequestRoomEnterInfoWithAntiCheats({uid, roomId}) {
        const res = await requestRoomEnterInfoWithAntiCheats({uid, roomId});
        prefetchDatasource.$source.requestRoomEnterInfoWithAntiCheats = res;
        return res;
    }

    prefetchAPIController('requestRoomEnterInfoWithAntiCheats');
}
