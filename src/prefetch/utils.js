/* eslint-disable max-len */
import crc32 from 'crc-32';
import {jsonp} from './jsonpApi';

export async function request({path, params}) {
    const defaultParams = {
        reqid: guid()
    };
    const finalParams = {
        ...defaultParams,
        path,
        params: JSON.stringify(params)
    };
    finalParams.sign = signGenerator(finalParams);

    const formData = new URLSearchParams();
    Object.entries(finalParams).forEach(([key, value]) => {
        formData.append(key, value);
    });
    try {
        const response = await fetch(`https://cluelive.baidu.com/${path}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: formData,
            credentials: 'include'
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data?.data || {};
    }
    catch (error) {
        console.error('Request failed:', error);
        return {};
    }
}
export function signGenerator(formData) {
    // 按 key 进行排序
    const sortedKeys = Object.keys(formData).sort();
    // 生成排序后的字符串
    let sortedString = '';
    sortedKeys.forEach(key => {
        sortedString += `${key}=${formData[key]}`;
    });
    sortedString += 'tiebaclient!!!';
    // eslint-disable-next-line no-undef
    return MD5(sortedString);
}

export function rand16Num(len) {
    const result = [];
    for (let i = 0; i < len; ++i) {
        result.push('0123456789abcdef'.charAt(Math.floor(Math.random() * 16)));
    }
    return result.join('');
}

export function guid() {
    const curr = new Date().valueOf().toString();
    return ['4b534c47', rand16Num(4), '4' + rand16Num(3), rand16Num(4), curr.substring(0, 12)].join('-');
}

export function once(fn) {
    let called = false;
    let result;

    return function (...args) {
        if (!called) {
            result = fn.apply(this, args);
            called = true;
        }
        return result;
    };
}

export function isIOS() {
    return /iP(hone|od|ad)/i.test(navigator.userAgent);
}
export function isAndroid() {
    return /Android/i.test(navigator.userAgent);
}

export const requestUa = `${window.innerWidth}_${window.innerHeight}_${isIOS() ? 'IOS'
    : isAndroid() ? 'ANDROID' : '0'}_1`;

// 网络类型，1_0代表wifi 0_2代表2g、0_3代表3G、0_13代表4g、0_16代表5g,前端拿不到可不传
export function getNetwork() {
    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
    const networkMap = {
        'slow-2g': '0_2',
        '2g': '0_2',
        '3g': '0_3',
        '4g': '0_13',
        '5g': '0_16' // 部分设备可能不会返回 5G
    };

    if (connection && connection.effectiveType) {
        return networkMap[connection.effectiveType] || '1_0';
    }
    return '1_0';
}


export class SettingItem {
    constructor(key) {
        this.key = key;
    }

    get() {
        try {
            let data = localStorage.getItem(this.key);
            if (data) {
                return JSON.parse(data);
            }
        }
        catch (err) {
            console.warn(err);
            return '';
        }
    }

    isTrue(defaultVal = false) {
        let data = this.get();
        if (!data) {
            return defaultVal;
        }
        return data !== '0' && data !== 'false';
    }

    set(data) {
        try {
            localStorage.setItem(this.key, JSON.stringify(data));
        }
        catch (error) {
            //
        }
    }

    clear() {
        try {
            localStorage.removeItem(this.key);
        }
        catch (error) {
            //
        }
    }
}

// 随机定长字符串
export const randomString = length => {
    let chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    for (let i = length; i > 0; --i) {
        result += chars[Math.floor(Math.random() * chars.length)];
    }
    return result;
};


export const getCuid = () => {
    const BDUID = 'BIDUPSID';
    const key = 'deviceId';
    let db = new SettingItem(key);
    let deviceId = db.get();

    if (!deviceId) {
        // eslint-disable-next-line no-undef
        deviceId = Cookies.get(BDUID) || randomString(32);
        // eslint-disable-next-line no-undef
        deviceId = MD5(deviceId);
        db.set(deviceId);
    }

    return deviceId;
};

export const getUrlAllParam = (link = '') => {
    let paramStr = '';
    if (link && typeof link === 'string') {
        paramStr = link.substring(link.indexOf('?') + 1, link.length).replace(/#\/$/, '');
    }
    else {
        const hash = window.location.hash;
        paramStr = window.location.search.substr(1) || hash.substring(hash.indexOf('?') + 1, hash.length);
    }
    const urlList = paramStr.split('&');
    const urlObject = {};
    urlList.forEach((item) => {
        const urlItem = item.split('=');
        if (urlItem[1]) {
            urlObject[urlItem[0]] = decodeURIComponent(urlItem[1]);
        }
    });
    return urlObject;
};
export function getAdUrlParams(urlParams = {}) {
    const {
        bd_bxst = '',
        bd_vid = '',
        ch = '',
        fid = '',
        ext_params = ''
    } = urlParams || {};
    return `ch=${ch}&bdvid=${bd_vid}&bd_vid=${bd_vid}&bd_bxst=${bd_bxst}&fid=${fid}${ext_params ? '&' : ''}${ext_params}`;
}
export function getThirdUrlParams() {
    const {
        bd_vid = '',
        ext_params = ''
    } = urlParams || {};
    return `bdvid=${bd_vid}${ext_params ? '&' : ''}${ext_params}`;
}
// 页面初始化只执行一次
const getCachedUrlParams = once(getUrlAllParam);
const getCachedAdUrlParams = once(getAdUrlParams);
const getCachedThirdAdUrlParams = once(getThirdUrlParams);
const urlParams = getCachedUrlParams(location.href);
const ad_url_params = getCachedAdUrlParams(urlParams);
const ad_third_url_params = getCachedThirdAdUrlParams(urlParams);

export async function queryAntiCheats({roomId}) {
    const {
        bd_vid = ''
    } = urlParams || {};
    const params = {
        execTypes: 'queryAntiCheats',
        roomId: +roomId,
        extLog: JSON.stringify({
            bd_vid,
            ad_url_params,
            ad_third_url_params
        }),
        network: getNetwork(),
        _client_type: isIOS() ? 1 : 2 // 类型1-iphone;2-android
    };
    try {
        const res = await request({
            path: 'cluelivec/commonApi/v1.0/bizData/get',
            params
        });
        return res;
    }
    catch (error) {
        console.log(error);
        return {};
    }
}


export async function getRoomEnterInfo({uid, roomId}) {
    const {
        bd_vid = '',
        livesource = '',
        source = ''
    } = urlParams || {};
    const cuid = getCuid();
    const jsonParams = Object.fromEntries(new URLSearchParams(window.location.search).entries());
    const params = {
        execTypes: 'queryAnyMountData,queryDynamicPeopleData',
        roomId: +roomId,
        uid,
        cuid,
        ua: requestUa,
        timestamp: Date.now(),
        liveSource: livesource,
        ext: JSON.stringify({
            ext: {
                source: source,
                bd_vid
            }
        }),
        h5RoomUrlParams: JSON.stringify(jsonParams),
        extLog: JSON.stringify({
            bd_vid,
            ad_url_params,
            ad_third_url_params
        })
    };
    try {
        const res = await request({
            path: 'cluelivec/commonApi/v1.0/bizData/get',
            params
        });
        const originImg = res?.anyMountResult?.anyMountCardImg;
        if (originImg) {
            res.anyMountResult.anyMountCardImg = `${originImg}?x-bce-process=image/quality,q_80`;
        }
        return res;
    }
    catch (error) {
        console.log(error);
        return {};
    }
}

export async function requestRoomEnterInfoWithAntiCheats({uid, roomId}) {
    // 创建超时控制器和结果标记
    let isTimeout = false;
    const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
            isTimeout = true;
            reject(new Error('timeout'));
        }, 300);
    });

    try {
        // 竞速请求：反作弊检查 vs 超时控制
        const res = await Promise.race([
            queryAntiCheats({uid, roomId}),
            timeoutPromise
        ]);

        // 正常返回且未超时的情况
        if (res?.antiCheatsResult?.hitAnti) {
            return {};
        }
        return await getRoomEnterInfo({uid, roomId}); // 未命中继续请求
    }
    catch (error) {
        // 处理超时或其它错误
        if (isTimeout) {
            // 超时后发起正常请求
            return await getRoomEnterInfo({uid, roomId});
        }
        throw error; // 其它异常原样抛出
    }
}
// isPrefetchAvailable
// CDN 健康检查
export const isDependencyLoaded = () => {
    // 检查全局对象是否存在
    const isCookiesReady = typeof Cookies !== 'undefined';
    const isMd5Ready = typeof MD5 !== 'undefined';
    return isCookiesReady && isMd5Ready;
};
const ua = navigator.userAgent.toLocaleLowerCase();
const isLiteBox = / ((lite|info) baiduboxapp\/)|baiduboxvision\//i.test(ua); // 百度青春版和lite保持一直
const isKnews = / knews\//i.test(ua);
export const env = {
    isHaokan: /haokan\//i.test(ua),
    // lite
    isLiteBox: isLiteBox || isKnews,
    isTomas: /tomas\//i.test(ua), // 大字版
    isWechat: /MicroMessenger\/([\d\.]+)/i.test(ua)
};

export const IS_APP_MAP = {
    'haokan': env.isHaokan,
    'lite': env.isLiteBox,
    'tomas': env.isTomas
};

export const getAppSource = appointSource => {
    const {source} = urlParams;
    const urlSrc = decodeURIComponent(source) || '';
    let defaultSource = urlSrc;
    if (urlSrc.includes('|')) {
        defaultSource = urlSrc.split('|')[0];
    }
    Object.keys(IS_APP_MAP).forEach(key => {
        if (IS_APP_MAP[key]) {
            defaultSource = key;
        }
    });
    return appointSource || defaultSource;
};

const APP_NAME_SCHEME = {
    // a6: 'baiduboxvision',
    // i6: 'baiduboxvision',
    baiduboxvision: 'baiduboxvision',
    // 好看视频
    haokan: 'baiduhaokan',
    // 百度App
    default: 'baiduboxapp',
    // 百度极速版
    lite: 'baiduboxlite',
    // 贴吧
    tieba: 'bdtiebalive',
    // 有驾
    youjia: 'youjia',
    // 汉语App
    baidudict: 'baidudict',
    // 古物潮玩
    fortunecat: 'fortunecat',
    // 音磁
    hiphop: 'bdhiphop',
    // 百度健康
    baidujiankangapp: 'bdhealthapp',
    // 百度大字版
    tomas: 'tomas',
    // 荣耀浏览器
    bdhonorbrowser: 'bdhonorbrowser'
};
// appointSource 指定source值获取scheme头，如未传，则根据url source来源获取scheme头
export const getAppSchemeHead = appointSource => {
    let source = getAppSource(appointSource) || appointSource;
    let scheme = APP_NAME_SCHEME[source] || APP_NAME_SCHEME.default;
    return scheme;
};
export const checkHasValue = (value) => {
    if (value) {
        let ignore = ['nil', 'null', 'nullb', 'undefined', '0'];
        let check = true;
        ignore.forEach((item) => {
            if (item === value) {
                check = false;
            }
        });
        return check;
    }
    return false;
};

const HOST = `${location.protocol}//mbd.baidu.com`;
export const searchBoxRequest = async (cmd, datas, action = 'star', urlParams = {}) => {
    let params = {
        cmd,
        action,
        service: 'bdbox',
        osname,
        data: encodeURIComponent(JSON.stringify(datas)),
        ...urlParams
    };
    const res = await jsonp(`${HOST}/searchbox`, params);
    const data = res.data;
    if (res && +res.errno === 0 && res.data && res.data[cmd]) {
        return res.data[cmd];
    }
    let errMsg = '服务错误,请稍后再试';
    let errCode = 0;
    if (+data.errno !== 0 && data.errmsg) {
        errMsg = data.errmsg;
        errCode = data.errno;
    }
    if (res.data && res.data[cmd] && res.data[cmd].error_code > 0) {
        errCode = res.data[cmd].error_code;
        errMsg = res.data[cmd].error_msg;
    }
    return {
        errMsg,
        errCode
    };
};
export const osname = getAppSchemeHead();

export const perfTestMap = {
    test: 1,
    origin: 0,
    default: -1
};
/**
 * 将任意字符串映射为 [0, bucketCount-1] 的整数
 * @param {string} str - 输入字符串
 * @param {number} bucketCount - 分桶数量，默认 10
 * @returns {number} - 返回分桶编号
 */
function getHashBucket(str, bucketCount = 10) {
    const hash = crc32.str(str);
    const unsigned = hash >>> 0; // 转换为无符号整数
    return unsigned % bucketCount;
}
export function getTestValue() {
    const bd_vid = urlParams.bd_vid || '';
    // 广告流量
    if (bd_vid) {
        const N = getHashBucket(bd_vid, 10);
        if (N < 5) {
            return perfTestMap.test;
        }
        return perfTestMap.origin;
    }
    // 自然流量
    const N = Math.random();
    if (N < 0.5) {
        return perfTestMap.test;
    }
    return perfTestMap.origin;

}
