/* eslint-disable max-len */

import {checkHasValue, env, osname, getCuid, getUrlAllParam, requestUa, searchBoxRequest} from './utils';
import {getPrefetchDatasource, setPrefetchAvailable} from './window';

const cuid = getCuid();
const cmdEnum = {
    MAIN: 371 // 主页面 (预约状态和直播状态都一个接口返回 根据template == 'live' 判断状态)
};

export function jsonpAPIRequst() {
    const prefetchDatasource = getPrefetchDatasource();
    setPrefetchAvailable(true);

    function prefetchAPIController(name) {
        prefetchDatasource[name] = req();
    }

    async function req() {
        const res = await jsonpAPIRequst_();
        prefetchDatasource.$source.requestMainData = res;
        return res;
    }

    prefetchAPIController('requestMainData');
}

async function jsonpAPIRequst_() {
    const queryParams = getUrlAllParam();
    const {
        nid, bd_vid, room_id, ask_id,
        replay_slice,
        wish_record_id,
        source,
        share_cuk, share_uid,
        share_ecid,
        zb_tag,
        ext_params, share_from
    } = queryParams;
    let data = {
        data: {
            room_id: room_id,
            device_id: cuid,
            source_type: 0,
            osname // 外层osname rd反馈取不到具体值 统一被赋值为baiduboxapp了
        }
    };
    let askId = ask_id || '';
    if (askId) {
        data.data.ask_id = askId;
    }
    data.replay_slice = replay_slice || 0;
    data.nid = checkHasValue(nid) ? nid : '';
    // 高考挂牌切片分享，添加wish_record_id标识区分
    if (wish_record_id) {
        data.data.wish_record_id = +wish_record_id;
    }
    // server 生成调起scheme 和 口令所需参数
    const browser = env.isWechat ? 'wechat' : 'other';
    // 粉丝团任务回流用的参数
    const shareTaskInfo = [
        'cUk',
        'bUk',
        'taskId',
        'fansGroupId',
        'activityType',
        'room_id',
        'shareSource',
        'lotteryActivityId',
        'shareExt'
    ].reduce((pre, cur) => {
        // tempValue为decode一次后的
        const tempValue = queryParams[cur];
        if (cur === 'shareExt') {
            try {
                pre[cur] = JSON.parse(tempValue);
            }
            catch (e) {
                pre[cur] = tempValue;
            }
        }
        else {
            pre[cur] = tempValue;
        }
        return pre;
    }, {});
    const decodeSource = decodeURIComponent(source || '');
    const src_pre = decodeSource.includes('|')
        ? decodeSource.split('|')[1]
        : decodeSource;

    data.schemeParams = {
        src_pre: src_pre || (bd_vid && 'bd_ads') || '', // source 前缀
        src_suf: browser, // source 后缀
        bd_vid: bd_vid,
        share_uid: share_uid || '',
        share_cuk: share_cuk || '',
        share_ecid: share_ecid || '', // 直播间分享来源的订单分享人信息透传2,新增加密cuid
        zb_tag: zb_tag || '',
        shareTaskInfo: JSON.stringify(shareTaskInfo), // 拼团裂变相关信息
        share_from: share_from || '',
        ext_params: decodeURIComponent(ext_params || ''),
        nid: nid
    };
    try {
        return await searchBoxRequest(cmdEnum.MAIN, data, 'star', {
            ua: requestUa,
            bd_vid: bd_vid,
            uid: cuid
        });
    }
    catch (error) {
        window.prefetchsearchBoxError = error;
    }
}
