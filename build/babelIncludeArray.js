/* eslint-disable import/unambiguous */
const path = require('path');

module.exports = [
    path.resolve(__dirname, '../src'),
    path.resolve(__dirname, '../node_modules/@baidu/eop-matrix'),
    path.resolve(__dirname, '../node_modules/@baidu/eop-box'),
    path.resolve(__dirname, '../node_modules/@baidu/eop-topics'),
    path.resolve(__dirname, '../node_modules/@baidu/eop-utils'),
    path.resolve(__dirname, '../node_modules/@baidu/xbox'),
    path.resolve(__dirname, '../node_modules/@baidu/xbox-concern'),
    path.resolve(__dirname, '../node_modules/@baidu/xbox-emoticon'),
    path.resolve(__dirname, '../node_modules/@baidu/xbox-native'),
    path.resolve(__dirname, '../node_modules/@baidu/xbox-sdk'),
    path.resolve(__dirname, '../node_modules/@baidu/ug-invoke-app'),
    path.resolve(__dirname, '../node_modules/@baidu/ug-matrix'),
    path.resolve(__dirname, '../node_modules/@baidu/ug-h5-utils'),
    path.resolve(__dirname, '../node_modules/@baidu/ug-task-consumer'),
    path.resolve(__dirname, '../node_modules/@baidu/ug-share-sdk'),
    path.resolve(__dirname, '../node_modules/@baidu/ubc-report-sdk'),
    path.resolve(__dirname, '../node_modules/@baidu/mvideo-ui'),
    path.resolve(__dirname, '../node_modules/@baidu/mvideo-tool'),
    path.resolve(__dirname, '../node_modules/@baidu/boxx'),
    path.resolve(__dirname, '../node_modules/@huse/debounce'),
    path.resolve(__dirname, '../node_modules/tua-body-scroll-lock'),
    path.resolve(__dirname, '../node_modules/dom7'),
    path.resolve(__dirname, '../node_modules/clipboard'),
    path.resolve(__dirname, '../node_modules/ssr-window'),
    path.resolve(__dirname, '../node_modules/query-string'),
    path.resolve(__dirname, '../node_modules/@super-fe'),
    path.resolve(__dirname, '../node_modules/swiper'),
    path.resolve(__dirname, '../node_modules/has-flag')
];
