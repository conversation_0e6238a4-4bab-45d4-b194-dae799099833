import {defineConfig} from 'tsup'
import { readFileSync, writeFileSync } from 'fs'
import { resolve } from 'path'

export default defineConfig({
  entry: ['src/prefetch/index.js'],
  format: 'iife',
  outDir: 'dist',
  target: 'es5',
  minify: true,
  watch: true,
  onSuccess: async () => {
    try {
      // 读取打包后的文件内容
      const bundleContent = readFileSync(resolve(__dirname, 'dist/index.global.js'), 'utf-8')

      // 读取 live.ejs 文件
      const liveEjsPath = resolve(__dirname, 'src/templates/live.ejs')
      let liveEjsContent = readFileSync(liveEjsPath, 'utf-8')

      // 使用正则表达式替换 prefetch-start 和 prefetch-end 之间的内容
      const regex = /<!-- prefetch-start -->[\s\S]*?<!-- prefetch-end -->/
      const replacement = `<!-- prefetch-start -->
    <script>
    ${bundleContent}
    </script>
        <!-- prefetch-end -->`

      liveEjsContent = liveEjsContent.replace(regex, replacement)

      // 写回文件
      writeFileSync(liveEjsPath, liveEjsContent, 'utf-8')

      console.log('Successfully injected prefetch bundle into live.ejs')
    } catch (error) {
      console.error('Error injecting prefetch bundle:', error)
    }
  }
})
