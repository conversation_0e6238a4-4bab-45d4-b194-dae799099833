module.exports = {
    presets: [
        ['@babel/preset-env', {
            'useBuiltIns': 'usage',
            'modules': false,
            'corejs': {
                'version': 3,
                'proposals': true
            }
        }],
        '@babel/preset-react'
        // '@babel/preset-typescript'
    ],
    plugins: [
        '@babel/plugin-transform-runtime',
        ['@babel/plugin-proposal-decorators', {
            'legacy': true
        }],
        '@babel/plugin-syntax-dynamic-import',
        [
            'babel-plugin-import',
            {
                'libraryName': 'antd',
                'style': true
            },
            'antd'
        ],
        [
            'babel-plugin-import',
            {
                libraryName: 'antd-mobile',
                style: 'css'
            },
            'antd-mobile'
        ],
        [
            'babel-plugin-import',
            {
                'libraryName': '@baidu/mvideo-ui',
                'style': name => `${name}/index.css`
            },
            '@baidu/mvideo-ui'
        ],
        [
            'babel-plugin-import',
            {
                'libraryName': '@baidu/mvideo-tool',
                'camel2DashComponentName': false
            },
            '@baidu/mvideo-tool'
        ],
        [
            'babel-plugin-import',
            {
                'libraryName': '@baidu/nano-react',
                'libraryDirectory': 'output/es',
                'style': true // less环境使用true, sass等其他环境配置 style: 'css'
            },
            '@baidu/nano-react'
        ]
    ],
    sourceType: 'unambiguous'
};
