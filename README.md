# 百度泛知识直播间分享页-线索直播

## 责任人

- 解钧堡（<EMAIL>）

## Dev Setup

```bash
# 安装项目依赖
npm i

# 启动dev
npm run dev

# 打包
npm run build

```

## 测试环境
- [说明文档](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/1ab8uT5A9J/WnHOIjVElQ/AJEKLoRlcOugu3)
- 使用已有环境或创建新环境作为测试环境，https://console.cloud.baidu-int.com/vercel/application/922/detail/environment-settings

- 1.流水线自动部署
- 在前端一站式配置中，选中测试环境，匹配规则选择精确匹配，填入开发分支名称，点击提交
- 之后开发分支提交代码时，就可以自动部署到该环境了

- 2.本地手动推送测试环境

```
# fis-fcnap.js 文件内配置测试环境（recieverMap 依赖fsr支持 注意协议需要用http)，以lqw为例
sh push-fcnap.sh lqw
```

- 访问地址：https://live-clue-lqw-fcnap.appspace.baidu.com/m/media/clue/liveshow/index.html?room_id=7474146536

## 交付说明
- [上线流程](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/1ab8uT5A9J/WnHOIjVElQ/7BfpeYdleg1kht)
- 在合并到master上线后把开发分支删掉

## 监控与统计
- [百度统计](https://tongji.baidu.com/web/homepage/index)
- [性能监控](https://weirwood.baidu-int.com/platform/app/111/home)
