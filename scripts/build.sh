#!/usr/bin/env bash

set -e

MOD_NAME=${PWD##*/}

# ci.yml的command中传入的命令行参数
export BUILD_TARGET="$1"

# 编译日志中打印Node和npm的版本。
echo "env check"
echo "MOD_NAME: ${MOD_NAME}"
echo "node $(node -v)"
echo "npm $(npm -v)"
echo "yarn $(yarn -v)"
echo "BUILD_TARGET: ${BUILD_TARGET}"
echo "build start"
echo "stage $2"

# 使用 内部源 安装依赖包
npm i --registry=http://registry.npm.baidu-int.com --legacy-peer-deps

cd src/abtest-sdk && npm i --registry=http://registry.npm.baidu-int.com --legacy-peer-deps

cd ../..

if [ $BUILD_TARGET == "development" ]; then
    npm run build:dev
else
    stage=$2
    npm run build
fi

# 删除信息敏感的页面
rm -rf dist/report.html

mkdir output
cp -r spec output/
cp -r dist/* output/

## start ##
# 检测如果是上线环境，需要上传 sourcemap
# 文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/gv0WKdV3ZF/60incnF1Zd/YzVmIARmcLNK7t
if [ "$BUILD_TARGET" != "development" ]; then
    echo "weirwood sourcemap start"
    readonly JOBDIR=$(pwd)
    readonly OUTPUT_DIR=${JOBDIR}/output

    # 核心代码：获取提交 HASH，拼接【日期 + buildId 前 7 位】，生成示例: 2021-04-26-9472769
    # 也可以自定义，符合 buildid 规范即可：buildid 只支持如下字符：英文字母(大小写)，数字(0-9)，下划线(_)，中划线(-)，点(.)，@ 符号
    revision=${AGILE_REVISION}
    buildId=$(date '+%Y-%m-%d')-${revision:0:7}

    echo "revision: ${revision}"
    echo "buildId: ${buildId}"
    echo "JOBDIR: ${JOBDIR}"
    echo "OUTPUT_DIR: ${OUTPUT_DIR}"

    # 核心代码：替换 <buildid> 为当前构建版本，要在打包前替换
    sed -i "s/<buildid>/${buildId}/g" weirwood.json

    # 将 weirwood.json 拷贝到输出目录
    cp -ar --target-directory=${OUTPUT_DIR} ${JOBDIR}/weirwood.json

    # 切换至output目录
    cd ${OUTPUT_DIR}

    # 创建临时目录
    temp_dir=$(mktemp -d)

    # 复制文件到临时目录并移除路径
    find static -type f \( -name "*.js" -o -name "*.js.map" \) -exec cp {} "$temp_dir" \;

    # 压缩临时目录中的文件，压缩所有 sourcemap，注意不能包含文件夹，必须扁平压缩
    tar -czf sourcemap.tar.gz -C "$temp_dir" .

    # 清理临时目录
    rm -rf "$temp_dir"

    # 核心代码：使用 npx 下载 @baidu/weirwood-cli 进行上传，这里需要注意 sourcemap.tar.gz 和 weirwood.json 在同一个目录
    npx @baidu/weirwood-cli -c weirwood.json

    # 核心代码： 删除所有 map 文件和 map 压缩包
    find static -type f -name "*.js.map" -exec rm {} \;
    rm sourcemap.tar.gz

    cd ${JOBDIR}
fi
## end ##

echo '[build] done'
exit 0
